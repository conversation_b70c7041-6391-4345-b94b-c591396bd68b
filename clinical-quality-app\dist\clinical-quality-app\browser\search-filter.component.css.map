{"version": 3, "sources": ["src/app/features/dashboard/components/search-filter/search-filter.component.scss"], "sourcesContent": ["@use 'variables' as variables;\r\n@use 'mixins' as mix;\r\n\r\n.search-filter {\r\n  width: 320px;\r\n}\r\n\r\n.search-input-container {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 8px 12px;\r\n  border-radius: 8px;\r\n  border: 1px solid var(--light-content, #D9E1E7);\r\n  background-color: var(--white, white);\r\n  gap: 8px;\r\n}\r\n\r\n.search-icon {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.search-input {\r\n  flex: 1;\r\n  border: none;\r\n  outline: none;\r\n  background: transparent;\r\n  font-size: 11px;\r\n  font-weight: 300;\r\n  line-height: 20px;\r\n  color: #384455;\r\n  width: 100%;\r\n  \r\n  &::placeholder {\r\n    color: #384455;\r\n  }\r\n}"], "mappings": ";AAGA,CAAA;AACE,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,WAAA,IAAA;AACA,iBAAA;AACA,UAAA,IAAA,MAAA,IAAA,eAAA,EAAA;AACA,oBAAA,IAAA,OAAA,EAAA;AACA,OAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,mBAAA;;AAGF,CAAA;AACE,QAAA;AACA,UAAA;AACA,WAAA;AACA,cAAA;AACA,aAAA;AACA,eAAA;AACA,eAAA;AACA,SAAA;AACA,SAAA;;AAEA,CAXF,YAWE;AACE,SAAA;;", "names": []}
{"version": 3, "sources": ["src/app/shared/components/buttons/submit-button.component.scss"], "sourcesContent": [".submit-button-default {\n  padding: 8px 16px;\n  border-radius: 8px;\n  font-size: 12px;\n  font-family: Urbane;\n  font-weight: 500;\n  line-height: 20px;\n  text-align: center;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  min-width: 80px;\n  transition: all 0.2s ease;\n  background-color: #1976d2;\n  color: #ffffff;\n  border: none;\n  cursor: pointer;\n\n  &:hover {\n    background-color: #1565c0;\n    transform: translateY(-1px);\n    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  }\n\n  &:active {\n    background-color: #0d47a1;\n    transform: translateY(0);\n  }\n}\n\n.submit-button-inactive {\n  padding: 8px 16px;\n  border-radius: 8px;\n  font-size: 12px;\n  font-family: Urbane;\n  font-weight: 500;\n  line-height: 20px;\n  text-align: center;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  min-width: 80px;\n  background-color: #BFD0EE;\n  color: #ffffff;\n  border: none;\n  cursor: not-allowed;\n  opacity: 0.8;\n\n  &:hover {\n    background-color: #BFD0EE;\n    transform: none;\n    box-shadow: none;\n  }\n}\n"], "mappings": ";AAAA,CAAA;AACE,WAAA,IAAA;AACA,iBAAA;AACA,aAAA;AACA,eAAA;AACA,eAAA;AACA,eAAA;AACA,cAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,aAAA;AACA,cAAA,IAAA,KAAA;AACA,oBAAA;AACA,SAAA;AACA,UAAA;AACA,UAAA;;AAEA,CAlBF,qBAkBE;AACE,oBAAA;AACA,aAAA,WAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CAxBF,qBAwBE;AACE,oBAAA;AACA,aAAA,WAAA;;AAIJ,CAAA;AACE,WAAA,IAAA;AACA,iBAAA;AACA,aAAA;AACA,eAAA;AACA,eAAA;AACA,eAAA;AACA,cAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,aAAA;AACA,oBAAA;AACA,SAAA;AACA,UAAA;AACA,UAAA;AACA,WAAA;;AAEA,CAlBF,sBAkBE;AACE,oBAAA;AACA,aAAA;AACA,cAAA;;", "names": []}
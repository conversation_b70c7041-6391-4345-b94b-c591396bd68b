<!DOCTYPE html><html lang="en"><head>
  <meta charset="utf-8">
  <title>ClinicalQualityApp</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/png" href="assets/logos/Stellarus-Favicon-red.png">
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&amp;display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
<link rel="stylesheet" href="styles.css"><style ng-app-id="ng">

.component-test-container[_ngcontent-ng-c3567730054] {
  max-width: 1600px;
  margin: 0 auto;
  padding: 30px;
  background-color: #F6F6F6;
}
.page-title[_ngcontent-ng-c3567730054] {
  font-size: 24px;
  font-weight: 600;
  line-height: 32px;
  color: #17181A;
  margin-bottom: 16px;
}
.page-description[_ngcontent-ng-c3567730054] {
  font-size: 14px;
  font-weight: 300;
  line-height: 20px;
  color: #17181A;
  margin-bottom: 30px;
}
.component-section[_ngcontent-ng-c3567730054] {
  background: #FFFFFF;
  border-radius: 8px;
  outline: 1px #F1F5F7 solid;
  outline-offset: -1px;
  padding: 20px;
  margin-bottom: 20px;
  margin-bottom: 30px;
}
.section-title[_ngcontent-ng-c3567730054] {
  font-size: 20px;
  font-weight: 600;
  line-height: 32px;
  color: #17181A;
  margin-bottom: 20px;
}
.section-description[_ngcontent-ng-c3567730054] {
  font-size: 14px;
  color: #547996;
  margin-bottom: 24px;
  line-height: 1.5;
}
.subsection-description[_ngcontent-ng-c3567730054] {
  font-size: 14px;
  color: #547996;
  margin-bottom: 16px;
  line-height: 1.5;
}
.subsection-title[_ngcontent-ng-c3567730054] {
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  color: #17181A;
  margin: 24px 0 16px 0;
  padding-left: 12px;
  border-left: 3px solid #3870B8;
}
.component-subtitle[_ngcontent-ng-c3567730054] {
  font-size: 11px;
  font-weight: 300;
  line-height: 16px;
  color: #547996;
  margin-bottom: 12px;
  font-style: italic;
}
.component-row[_ngcontent-ng-c3567730054] {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 40px;
  min-width: 100%;
}
.component-row[_ngcontent-ng-c3567730054]:last-child {
  margin-bottom: 0;
}
.component-row.demographics-row[_ngcontent-ng-c3567730054] {
  flex-wrap: nowrap;
  overflow-x: visible;
}
.component-item[_ngcontent-ng-c3567730054] {
  flex: 1;
  min-width: 300px;
}
.component-item.full-width[_ngcontent-ng-c3567730054] {
  flex: 0 0 100%;
}
.component-title[_ngcontent-ng-c3567730054] {
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  color: #17181A;
  margin-bottom: 12px;
}
.component-demo[_ngcontent-ng-c3567730054] {
  padding: 20px;
  background-color: #FFFFFF;
  border-radius: 8px;
  outline: 1px #F1F5F7 solid;
  outline-offset: -1px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 80px;
  overflow-x: visible;
}
.component-demo.table-demo[_ngcontent-ng-c3567730054] {
  justify-content: flex-start;
  align-items: flex-start;
  padding: 0;
  display: block;
}
.component-demo.figma-sized[_ngcontent-ng-c3567730054] {
  justify-content: flex-start;
  align-items: flex-start;
}
.component-demo.figma-sized.button-demo[_ngcontent-ng-c3567730054] {
  width: fit-content;
  min-width: auto;
  padding: 20px;
}
.component-demo.figma-sized.form-demo[_ngcontent-ng-c3567730054] {
  width: 300px;
  min-width: 300px;
  min-height: 400px;
  padding: 20px 20px 60px 20px;
  overflow: visible;
}
.component-demo.figma-sized.form-demo[_ngcontent-ng-c3567730054]   app-dropdown[_ngcontent-ng-c3567730054], 
.component-demo.figma-sized.form-demo[_ngcontent-ng-c3567730054]   app-date-picker[_ngcontent-ng-c3567730054], 
.component-demo.figma-sized.form-demo[_ngcontent-ng-c3567730054]   app-notes[_ngcontent-ng-c3567730054] {
  width: 100%;
}
.component-demo.figma-sized.form-demo[_ngcontent-ng-c3567730054]   app-dropdown[_ngcontent-ng-c3567730054], 
.component-demo.figma-sized.form-demo[_ngcontent-ng-c3567730054]   app-date-picker[_ngcontent-ng-c3567730054] {
  position: relative;
  z-index: 10;
}
.component-demo.figma-sized.hits-demo[_ngcontent-ng-c3567730054] {
  width: 557px;
  min-width: 557px;
  padding: 20px;
}
.component-demo.figma-sized.hits-demo[_ngcontent-ng-c3567730054]   app-hits[_ngcontent-ng-c3567730054] {
  width: 517px;
}
.component-demo.figma-sized.demographics-demo[_ngcontent-ng-c3567730054] {
  width: 100%;
  min-width: 1420px;
  padding: 20px;
  overflow-x: visible;
}
.component-demo.figma-sized.results-demo[_ngcontent-ng-c3567730054] {
  width: 400px;
  min-width: 400px;
  padding: 20px;
}
.component-demo.figma-sized.menu-demo[_ngcontent-ng-c3567730054] {
  width: 100%;
  padding: 0;
}
.component-demo.figma-sized.status-demo[_ngcontent-ng-c3567730054] {
  width: fit-content;
  min-width: auto;
  padding: 20px;
}
.component-demo.figma-sized.checkbox-demo[_ngcontent-ng-c3567730054] {
  width: fit-content;
  min-width: auto;
  padding: 20px;
}
.component-code[_ngcontent-ng-c3567730054] {
  background-color: #F9FBFC;
  border-radius: 8px;
  padding: 12px;
  overflow: auto;
}
.component-code[_ngcontent-ng-c3567730054]   pre[_ngcontent-ng-c3567730054] {
  margin: 0;
  font-family: monospace;
  font-size: 12px;
  line-height: 1.5;
  color: #384455;
}
.full-width[_ngcontent-ng-c3567730054] {
  width: 100%;
}
@media (min-width: 600px) {
  .component-row[_ngcontent-ng-c3567730054] {
    flex-wrap: nowrap;
  }
}
@media (max-width: 599px) {
  .component-item[_ngcontent-ng-c3567730054] {
    flex: 0 0 100%;
  }
}
.icons-grid[_ngcontent-ng-c3567730054] {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  padding: 16px;
}
.icon-demo-item[_ngcontent-ng-c3567730054] {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border: 1px solid #F1F5F7;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}
.icon-demo-item[_ngcontent-ng-c3567730054]:hover {
  background-color: #F1F5F7;
}
.icon-label[_ngcontent-ng-c3567730054] {
  font-size: 10px;
  font-family: "Urbane", sans-serif;
  font-weight: 300;
  color: #547996;
  text-align: center;
}
/*# sourceMappingURL=/component-test.component.css.map */</style><style ng-app-id="ng">

@font-face {
  font-family: "Urbane";
  src: url("./media/Urbane-Light.woff2") format("woff2"), url("./media/Urbane-Light.woff") format("woff");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Urbane";
  src: url("./media/Urbane-Medium.woff2") format("woff2"), url("./media/Urbane-Medium.woff") format("woff");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Urbane";
  src: url("./media/Urbane-Medium.woff2") format("woff2"), url("./media/Urbane-Medium.woff") format("woff");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Urbane";
  src: url("./media/Urbane-DemiBold.woff2") format("woff2"), url("./media/Urbane-DemiBold.woff") format("woff");
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}
.text-xs[_ngcontent-ng-c502759576] {
  font-size: 10px;
  font-family: "Urbane", sans-serif;
  line-height: 20px;
}
.text-sm[_ngcontent-ng-c502759576] {
  font-size: 11px;
  font-family: "Urbane", sans-serif;
  line-height: 20px;
}
.text-base[_ngcontent-ng-c502759576] {
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  line-height: 20px;
}
.text-md[_ngcontent-ng-c502759576] {
  font-size: 14px;
  font-family: "Urbane", sans-serif;
  line-height: 20px;
}
.text-lg[_ngcontent-ng-c502759576] {
  font-size: 16px;
  font-family: "Urbane", sans-serif;
  line-height: 20px;
}
.text-xl[_ngcontent-ng-c502759576] {
  font-size: 20px;
  font-family: "Urbane", sans-serif;
  line-height: 32px;
}
.text-xxl[_ngcontent-ng-c502759576] {
  font-size: 24px;
  font-family: "Urbane", sans-serif;
  line-height: 32px;
}
.font-light[_ngcontent-ng-c502759576] {
  font-weight: 300;
}
.font-regular[_ngcontent-ng-c502759576] {
  font-weight: 400;
}
.font-medium[_ngcontent-ng-c502759576] {
  font-weight: 500;
}
.font-semibold[_ngcontent-ng-c502759576] {
  font-weight: 600;
}
.label-text[_ngcontent-ng-c502759576] {
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  line-height: 20px;
  font-weight: 300;
  color: #17181A;
}
.link-text[_ngcontent-ng-c502759576] {
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  line-height: 20px;
  font-weight: 500;
  color: #0071BC;
}
.heading-text[_ngcontent-ng-c502759576] {
  font-size: 20px;
  font-family: "Urbane", sans-serif;
  line-height: 32px;
  font-weight: 600;
  color: #17181A;
}
.subheading-text[_ngcontent-ng-c502759576] {
  font-size: 14px;
  font-family: "Urbane", sans-serif;
  line-height: 20px;
  font-weight: 600;
  color: #17181A;
}
.caption-text[_ngcontent-ng-c502759576] {
  font-size: 10px;
  font-family: "Urbane", sans-serif;
  line-height: 20px;
  font-weight: 500;
  color: #547996;
}
body[_ngcontent-ng-c502759576] {
  font-family: "Urbane", sans-serif;
  font-size: 12px;
  line-height: 20px;
  color: #17181A;
}
.button[_ngcontent-ng-c502759576] {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border-radius: 8px;
  font-family: "Urbane", sans-serif;
  font-size: 12px;
  font-weight: 500;
  line-height: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  outline: none;
}
.button[_ngcontent-ng-c502759576]:focus {
  outline: none;
}
.button.button-with-icon[_ngcontent-ng-c502759576] {
  gap: 8px;
}
.button[_ngcontent-ng-c502759576]   .button-icon[_ngcontent-ng-c502759576] {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}
.button[_ngcontent-ng-c502759576]   app-refresh-icon[_ngcontent-ng-c502759576] {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  vertical-align: middle;
  flex-shrink: 0;
}
.button.btn-icon-right[_ngcontent-ng-c502759576] {
  flex-direction: row-reverse;
}
.button-primary[_ngcontent-ng-c502759576] {
  background-color: #3870B8;
  color: #FFFFFF;
  padding: 8px 16px;
}
.button-primary.figma-exact[_ngcontent-ng-c502759576] {
  padding: 10px 16px;
}
.button-primary.figma-state-inactive[_ngcontent-ng-c502759576] {
  background-color: #BFD0EE;
  color: #FFFFFF;
  cursor: not-allowed;
}
.button-primary.figma-state-inactive[_ngcontent-ng-c502759576]:hover {
  background-color: #BFD0EE;
}
.button-primary.figma-state-default[_ngcontent-ng-c502759576] {
  background-color: #3870B8;
  color: #FFFFFF;
}
.button-primary.figma-state-hover[_ngcontent-ng-c502759576] {
  background-color: #468CE7;
  color: #FFFFFF;
}
.button-primary.figma-state-click[_ngcontent-ng-c502759576] {
  background-color: #285082;
  color: #FFFFFF;
}
.button-primary[_ngcontent-ng-c502759576]:not(.figma-state-inactive):not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click):hover {
  background-color: #468CE7;
}
.button-primary[_ngcontent-ng-c502759576]:not(.figma-state-inactive):not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click):active {
  background-color: #285082;
}
.button-primary.figma-exact[_ngcontent-ng-c502759576]:not(.figma-state-inactive):not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click):hover {
  background-color: #468CE7;
}
.button-primary.figma-exact[_ngcontent-ng-c502759576]:not(.figma-state-inactive):not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click):active {
  background-color: #285082;
}
.button-primary.button-disabled[_ngcontent-ng-c502759576] {
  background-color: #BFD0EE;
  color: #FFFFFF;
  cursor: not-allowed;
}
.button-secondary[_ngcontent-ng-c502759576] {
  background-color: #FFFFFF;
  color: #17181A;
  border: 1px solid #D9E1E7;
  padding: 8px 12px;
}
.button-secondary.figma-exact[_ngcontent-ng-c502759576] {
  padding: 8px 14px;
}
.button-secondary.figma-state-default[_ngcontent-ng-c502759576] {
  background-color: #FFFFFF;
  color: #17181A;
  border: 1px solid #D9E1E7;
}
.button-secondary.figma-state-default[_ngcontent-ng-c502759576]   app-refresh-icon[_ngcontent-ng-c502759576] {
  color: #17181A;
}
.button-secondary.figma-state-hover[_ngcontent-ng-c502759576] {
  background-color: #F1F5F7;
  color: #17181A;
  border: 1px solid #D9E1E7;
}
.button-secondary.figma-state-hover[_ngcontent-ng-c502759576]   app-refresh-icon[_ngcontent-ng-c502759576] {
  color: #17181A;
}
.button-secondary.figma-state-click[_ngcontent-ng-c502759576] {
  background-color: #17181A;
  color: #FFFFFF;
  border: 1px solid #D9E1E7;
}
.button-secondary.figma-state-click[_ngcontent-ng-c502759576]   app-refresh-icon[_ngcontent-ng-c502759576] {
  color: #FFFFFF;
}
.button-secondary[_ngcontent-ng-c502759576]:not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click):hover {
  background-color: #F1F5F7;
}
.button-secondary[_ngcontent-ng-c502759576]:not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click):active {
  background-color: #17181A;
  color: #FFFFFF;
}
.button-secondary.figma-exact[_ngcontent-ng-c502759576]:not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click)   app-refresh-icon[_ngcontent-ng-c502759576] {
  color: #17181A;
}
.button-secondary.figma-exact[_ngcontent-ng-c502759576]:not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click):hover {
  background-color: #F1F5F7;
}
.button-secondary.figma-exact[_ngcontent-ng-c502759576]:not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click):hover   app-refresh-icon[_ngcontent-ng-c502759576] {
  color: #17181A;
}
.button-secondary.figma-exact[_ngcontent-ng-c502759576]:not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click):active {
  background-color: #17181A;
  color: #FFFFFF;
}
.button-secondary.figma-exact[_ngcontent-ng-c502759576]:not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click):active   app-refresh-icon[_ngcontent-ng-c502759576] {
  color: #FFFFFF;
}
.button-secondary.button-disabled[_ngcontent-ng-c502759576] {
  color: #547996;
  border-color: #F1F5F7;
  cursor: not-allowed;
}
.button-tertiary[_ngcontent-ng-c502759576] {
  background-color: transparent;
  color: #3870B8;
  box-shadow: none;
}
.button-tertiary[_ngcontent-ng-c502759576]:hover {
  background-color: rgba(56, 112, 184, 0.05);
}
.button-tertiary[_ngcontent-ng-c502759576]:active {
  background-color: rgba(56, 112, 184, 0.1);
}
.button-tertiary.button-disabled[_ngcontent-ng-c502759576] {
  color: #547996;
  cursor: not-allowed;
}
.button-sm[_ngcontent-ng-c502759576] {
  padding: 8px 12px;
  font-size: 11px;
}
.button-lg[_ngcontent-ng-c502759576] {
  padding: 16px 20px;
  font-size: 14px;
}
.button-block[_ngcontent-ng-c502759576] {
  width: 100%;
  display: flex;
}
/*# sourceMappingURL=/button.component.css.map */</style><style ng-app-id="ng">

[_nghost-ng-c2659116941] {
  display: inline-block;
  width: 16px;
  height: 16px;
}
.refresh-icon-default[_ngcontent-ng-c2659116941] {
  color: #17181A;
}
.refresh-icon-hover[_ngcontent-ng-c2659116941] {
  color: #17181A;
}
.refresh-icon-click[_ngcontent-ng-c2659116941] {
  color: #FFFFFF;
}
svg[_ngcontent-ng-c2659116941] {
  width: 16px;
  height: 16px;
  display: block;
}
/*# sourceMappingURL=/refresh-icon.component.css.map */</style><style ng-app-id="ng">

.checkbox-container[_ngcontent-ng-c2693716353] {
  display: flex;
  align-items: center;
  gap: 8px;
}
.checkbox[_ngcontent-ng-c2693716353] {
  width: 16px;
  height: 16px;
  border-radius: 5px;
  border: 1px solid #D9E1E7;
  background-color: #FFFFFF;
  cursor: pointer;
  transition: all 0.2s ease;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  position: relative;
  margin: 0;
  cursor: pointer;
}
.checkbox[_ngcontent-ng-c2693716353]:checked {
  background-color: #17181A;
  border-color: #17181A;
}
.checkbox[_ngcontent-ng-c2693716353]:checked::after {
  content: "";
  position: absolute;
  left: 4px;
  top: 4px;
  width: 8.33px;
  height: 7.5px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8.33' height='7.5' viewBox='0 0 8.33 7.5'%3E%3Cpath d='M1 3.75L3.5 6.25L7.33 1.25' stroke='white' stroke-width='1.5' fill='none' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
.checkbox[_ngcontent-ng-c2693716353]:hover:not(:disabled) {
  border-color: #547996;
}
.checkbox[_ngcontent-ng-c2693716353]:focus {
  outline: 2px solid #3870B8;
  outline-offset: 2px;
}
.checkbox[_ngcontent-ng-c2693716353]:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.checkbox[_ngcontent-ng-c2693716353]:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.checkbox-label[_ngcontent-ng-c2693716353] {
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  font-weight: 300;
  color: #17181A;
  cursor: pointer;
}
/*# sourceMappingURL=/checkbox.component.css.map */</style><style ng-app-id="ng">

.dropdown-container[_ngcontent-ng-c81275535] {
  position: relative;
  width: 100%;
}
.dropdown-container.disabled[_ngcontent-ng-c81275535] {
  opacity: 0.5;
  pointer-events: none;
}
.dropdown-label[_ngcontent-ng-c81275535] {
  display: block;
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  font-weight: 300;
  color: #17181A;
  margin-bottom: 4px;
}
.dropdown-label[_ngcontent-ng-c81275535]   .required-indicator[_ngcontent-ng-c81275535] {
  color: #3870B8;
  margin-left: 2px;
}
.dropdown-trigger[_ngcontent-ng-c81275535] {
  padding: 4px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 0px;
  overflow: hidden;
  border-radius: 10px;
  border: 1px solid #d9e1e7;
  background: #ffffff;
  height: 48px;
  width: 100%;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  box-sizing: border-box;
}
.dropdown-trigger[_ngcontent-ng-c81275535]:hover:not(.disabled) {
  border-color: #547996;
}
.dropdown-trigger.open[_ngcontent-ng-c81275535] {
  border-color: #547996;
}
.dropdown-trigger[_ngcontent-ng-c81275535]:focus {
  outline: none;
  border-color: #1976d2;
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.1);
}
.dropdown-trigger.disabled[_ngcontent-ng-c81275535] {
  background-color: #f1f5f7;
  cursor: not-allowed;
}
.dropdown-trigger.has-error[_ngcontent-ng-c81275535] {
  border-color: #F4454E;
}
.dropdown-content[_ngcontent-ng-c81275535] {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
  box-sizing: border-box;
}
.dropdown-text[_ngcontent-ng-c81275535] {
  color: #547996;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
  flex: 1;
}
.dropdown-text.placeholder[_ngcontent-ng-c81275535] {
  color: #547996;
}
.dropdown-text.has-value[_ngcontent-ng-c81275535] {
  color: #17181a;
}
.dropdown-icon[_ngcontent-ng-c81275535] {
  position: relative;
  display: flex;
  transition: transform 0.2s ease;
  color: #547996;
  box-sizing: border-box;
  overflow: hidden;
}
.dropdown-icon.rotated[_ngcontent-ng-c81275535] {
  transform: rotate(180deg);
}
.dropdown-icon[_ngcontent-ng-c81275535]   svg[_ngcontent-ng-c81275535] {
  top: 2px;
  left: 4px;
  position: absolute;
  display: flex;
  width: 14px;
  height: 8px;
}
.dropdown-options[_ngcontent-ng-c81275535] {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #FFFFFF;
  border-radius: 10px;
  border: 1px solid #547996;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
  margin-top: -1px;
}
.dropdown-separator[_ngcontent-ng-c81275535] {
  height: 1px;
  background: #D9E1E7;
  margin: 0 8px;
}
.dropdown-option[_ngcontent-ng-c81275535] {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  gap: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-radius: 6px;
  margin: 0 4px;
}
.dropdown-option[_ngcontent-ng-c81275535]:hover:not(.disabled) {
  background-color: #F1F5F7;
}
.dropdown-option.selected[_ngcontent-ng-c81275535] {
  background-color: #F9FBFC;
}
.dropdown-option.disabled[_ngcontent-ng-c81275535] {
  opacity: 0.5;
  cursor: not-allowed;
}
.option-checkbox[_ngcontent-ng-c81275535] {
  display: flex;
  align-items: center;
}
.option-checkbox[_ngcontent-ng-c81275535]   input[type=checkbox][_ngcontent-ng-c81275535] {
  width: 16px;
  height: 16px;
  border-radius: 5px;
  border: 1px solid #D9E1E7;
  background-color: #FFFFFF;
  cursor: pointer;
  transition: all 0.2s ease;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  position: relative;
  margin: 0;
  pointer-events: none;
}
.option-checkbox[_ngcontent-ng-c81275535]   input[type=checkbox][_ngcontent-ng-c81275535]:checked {
  background-color: #17181A;
  border-color: #17181A;
}
.option-checkbox[_ngcontent-ng-c81275535]   input[type=checkbox][_ngcontent-ng-c81275535]:checked::after {
  content: "";
  position: absolute;
  left: 4px;
  top: 4px;
  width: 8.33px;
  height: 7.5px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8.33' height='7.5' viewBox='0 0 8.33 7.5'%3E%3Cpath d='M1 3.75L3.5 6.25L7.33 1.25' stroke='white' stroke-width='1.5' fill='none' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
.option-checkbox[_ngcontent-ng-c81275535]   input[type=checkbox][_ngcontent-ng-c81275535]:hover:not(:disabled) {
  border-color: #547996;
}
.option-checkbox[_ngcontent-ng-c81275535]   input[type=checkbox][_ngcontent-ng-c81275535]:focus {
  outline: 2px solid #3870B8;
  outline-offset: 2px;
}
.option-checkbox[_ngcontent-ng-c81275535]   input[type=checkbox][_ngcontent-ng-c81275535]:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.option-text[_ngcontent-ng-c81275535] {
  flex: 1;
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  font-weight: 300;
  line-height: 20px;
  color: #17181A;
}
.error-message[_ngcontent-ng-c81275535] {
  margin-top: 4px;
  font-size: 11px;
  font-family: "Urbane", sans-serif;
  font-weight: 300;
  color: #F4454E;
}
@media (max-width: 599px) {
  .dropdown-options[_ngcontent-ng-c81275535] {
    max-height: 200px;
  }
}
/*# sourceMappingURL=/dropdown.component.css.map */</style><style ng-app-id="ng">

.calendar-container[_ngcontent-ng-c1227210501] {
  position: relative;
  flex: 1 1 0;
  min-width: 0;
  box-sizing: border-box;
  width: 100%;
  max-width: 100%;
}
.calendar-container.disabled[_ngcontent-ng-c1227210501] {
  opacity: 0.5;
  pointer-events: none;
}
.calendar-label[_ngcontent-ng-c1227210501] {
  display: block;
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  font-weight: 300;
  color: #17181A;
  margin-bottom: 4px;
}
.calendar-label[_ngcontent-ng-c1227210501]   .required-indicator[_ngcontent-ng-c1227210501] {
  color: #3870B8;
  margin-left: 2px;
}
.date-input-container[_ngcontent-ng-c1227210501] {
  padding: 4px;
  overflow: hidden;
  border-radius: 10px;
  border: 1px solid #d9e1e7;
  background: #ffffff;
  height: 48px;
  width: 100%;
  flex: 1 1 0;
  min-width: 0;
  max-width: 100%;
  box-sizing: border-box;
  position: relative;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: stretch;
}
.date-input-container[_ngcontent-ng-c1227210501]:hover:not(.disabled) {
  border-color: #547996;
}
.date-input-container.focused[_ngcontent-ng-c1227210501] {
  border-color: #547996;
}
.date-input-container.has-error[_ngcontent-ng-c1227210501] {
  border-color: #F4454E;
}
.date-input-container.disabled[_ngcontent-ng-c1227210501] {
  background-color: #f1f5f7;
  cursor: not-allowed;
}
.date-input-content[_ngcontent-ng-c1227210501] {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  gap: 12px;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
  box-sizing: border-box;
}
.date-input[_ngcontent-ng-c1227210501] {
  flex: 1;
  color: #547996;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  font-weight: 300;
  text-align: left;
  border: none;
  outline: none;
  background: transparent;
}
.date-input[_ngcontent-ng-c1227210501]::placeholder {
  color: #547996;
}
.date-input[_ngcontent-ng-c1227210501]:disabled {
  cursor: not-allowed;
  color: #547996;
}
.date-input[_ngcontent-ng-c1227210501]:focus {
  color: #17181a;
}
.date-input-container.has-value[_ngcontent-ng-c1227210501]   .date-input[_ngcontent-ng-c1227210501] {
  color: #17181a;
}
.calendar-icon-button[_ngcontent-ng-c1227210501] {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  cursor: pointer;
  padding: 0;
  transition: color 0.2s ease;
  flex-shrink: 0;
}
.calendar-icon-button[_ngcontent-ng-c1227210501]:hover:not(:disabled) {
  color: #17181a;
}
.calendar-icon-button[_ngcontent-ng-c1227210501]:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}
.calendar-icon[_ngcontent-ng-c1227210501] {
  width: 16px;
  height: 18px;
  color: #547996;
  transition: color 0.2s ease;
}
.date-input-container.focused[_ngcontent-ng-c1227210501]   .calendar-icon[_ngcontent-ng-c1227210501], 
.date-input-container.has-value[_ngcontent-ng-c1227210501]   .calendar-icon[_ngcontent-ng-c1227210501] {
  color: #17181a;
}
.calendar-popup[_ngcontent-ng-c1227210501] {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #FFFFFF;
  border-radius: 10px;
  border: 1px solid #547996;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  margin-top: 4px;
  padding: 16px;
  min-width: 280px;
}
.calendar-header[_ngcontent-ng-c1227210501] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}
.nav-button[_ngcontent-ng-c1227210501] {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  color: #547996;
  transition: all 0.2s ease;
}
.nav-button[_ngcontent-ng-c1227210501]:hover {
  background-color: #F1F5F7;
  color: #17181A;
}
.nav-button[_ngcontent-ng-c1227210501]   svg[_ngcontent-ng-c1227210501] {
  width: 8px;
  height: 12px;
}
.month-year[_ngcontent-ng-c1227210501] {
  font-size: 14px;
  font-family: "Urbane", sans-serif;
  font-weight: 600;
  color: #17181A;
  text-align: center;
  flex: 1;
}
.days-header[_ngcontent-ng-c1227210501] {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 4px;
  margin-bottom: 8px;
}
.day-header[_ngcontent-ng-c1227210501] {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  font-size: 11px;
  font-family: "Urbane", sans-serif;
  font-weight: 600;
  color: #547996;
  text-transform: uppercase;
}
.calendar-days[_ngcontent-ng-c1227210501] {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 4px;
}
.calendar-day[_ngcontent-ng-c1227210501] {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  font-weight: 300;
  color: #17181A;
  transition: all 0.2s ease;
}
.calendar-day[_ngcontent-ng-c1227210501]:hover:not(:disabled):not(.empty) {
  background-color: #F1F5F7;
}
.calendar-day.selected[_ngcontent-ng-c1227210501] {
  background-color: #3870B8;
  color: #FFFFFF;
}
.calendar-day.selected[_ngcontent-ng-c1227210501]:hover {
  background-color: rgb(44.1, 88.2, 144.9);
}
.calendar-day.today[_ngcontent-ng-c1227210501] {
  font-weight: 600;
  color: #3870B8;
}
.calendar-day.today[_ngcontent-ng-c1227210501]:not(.selected) {
  background-color: rgba(56, 112, 184, 0.1);
}
.calendar-day.empty[_ngcontent-ng-c1227210501] {
  cursor: default;
  visibility: hidden;
}
.calendar-day[_ngcontent-ng-c1227210501]:disabled {
  cursor: not-allowed;
  opacity: 0.3;
}
.error-message[_ngcontent-ng-c1227210501] {
  margin-top: 4px;
  font-size: 11px;
  font-family: "Urbane", sans-serif;
  font-weight: 300;
  color: #F4454E;
}
@media (max-width: 599px) {
  .calendar-popup[_ngcontent-ng-c1227210501] {
    min-width: 260px;
    padding: 12px;
  }
  .calendar-day[_ngcontent-ng-c1227210501] {
    width: 28px;
    height: 28px;
    font-size: 11px;
  }
  .day-header[_ngcontent-ng-c1227210501] {
    height: 28px;
    font-size: 10px;
  }
}
[_nghost-ng-c1227210501] {
  display: block;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}
.calendar-container[_ngcontent-ng-c1227210501] {
  width: 100%;
  box-sizing: border-box;
}
.date-input-container[_ngcontent-ng-c1227210501]   input[_ngcontent-ng-c1227210501] {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}
/*# sourceMappingURL=/calendar.component.css.map */</style><style ng-app-id="ng">

.results-container[_ngcontent-ng-c2018085561] {
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: stretch;
  gap: 0px;
  border-radius: 8px;
  border: 1px solid #f1f5f7;
  background: #ffffff;
  width: 100%;
  box-sizing: border-box;
}
.results-container.disabled[_ngcontent-ng-c2018085561] {
  opacity: 0.6;
  pointer-events: none;
}
.results-container.has-error[_ngcontent-ng-c2018085561] {
  border-color: #F4454E;
}
.results-header[_ngcontent-ng-c2018085561] {
  padding: 0px 0px 12px 0px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  gap: 20px;
  width: 100%;
  box-sizing: border-box;
}
.results-title-section[_ngcontent-ng-c2018085561] {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 12px;
  width: 100%;
  box-sizing: border-box;
}
.results-title[_ngcontent-ng-c2018085561] {
  color: #17181A;
  font-family: Urbane;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 32px;
  text-align: left;
  text-wrap: nowrap;
  margin: 0;
}
.tab-navigation[_ngcontent-ng-c2018085561] {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  gap: 0px;
  border-bottom: 1px solid #d9e1e7;
  background: #ffffff;
  width: 100%;
  box-sizing: content-box;
}
.tab-button[_ngcontent-ng-c2018085561] {
  padding: 4px 8px 4px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  gap: 12px;
  height: 100%;
  background: transparent;
  border: none;
  border-bottom: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  box-sizing: border-box;
  color: #547996;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}
.tab-button[_ngcontent-ng-c2018085561]:hover:not(:disabled) {
  color: #17181a;
}
.tab-button.active[_ngcontent-ng-c2018085561] {
  color: #1976d2;
  border-bottom: 1px solid #1976d2;
}
.tab-button[_ngcontent-ng-c2018085561]:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}
.tab-content[_ngcontent-ng-c2018085561] {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  gap: 12px;
  width: 100%;
  box-sizing: border-box;
}
.error-message[_ngcontent-ng-c2018085561] {
  color: #F4454E;
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  font-weight: 300;
  line-height: 16px;
  margin-top: 4px;
}
@media (max-width: 768px) {
  .results-container[_ngcontent-ng-c2018085561] {
    padding: 16px;
  }
  .tab-navigation[_ngcontent-ng-c2018085561] {
    flex-wrap: wrap;
    gap: 8px;
  }
  .tab-button[_ngcontent-ng-c2018085561] {
    font-size: 12px;
    padding: 2px 6px;
  }
}
/*# sourceMappingURL=/results.component.css.map */</style><style ng-app-id="ng">

.assigned-table_1134-1191[_ngcontent-ng-c2702405285] {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 20px;
  box-sizing: border-box;
  width: 1380px;
}
.table_1134-1192[_ngcontent-ng-c2702405285] {
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  border-radius: 8px;
  border-color: #f1f5f7;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  width: 100%;
}
.table_1134-1208[_ngcontent-ng-c2702405285] {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: 100%;
}
.columns_1134-1209[_ngcontent-ng-c2702405285] {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: 100%;
}
.column_1134-1210[_ngcontent-ng-c2702405285] {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: 100%;
}
.header-item_1134-1211[_ngcontent-ng-c2702405285] {
  padding: 0px 12px 0px 12px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  width: 100%;
}
.table-item_1134-1212[_ngcontent-ng-c2702405285] {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 68px;
}
.text-label_1134-1214[_ngcontent-ng-c2702405285] {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}
.table-item_1134-1215[_ngcontent-ng-c2702405285] {
  padding: 13px 12px 13px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 68px;
}
.text-label_1134-1216[_ngcontent-ng-c2702405285] {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}
.column_1235-930[_ngcontent-ng-c2702405285] {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: 107px;
}
.header-item_1235-931[_ngcontent-ng-c2702405285] {
  padding: 0px 12px 0px 12px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  width: 100%;
}
.table-item_1235-932[_ngcontent-ng-c2702405285] {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 68px;
}
.text-label_1235-934[_ngcontent-ng-c2702405285] {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}
.table-item_1235-939[_ngcontent-ng-c2702405285] {
  padding: 10px 12px 10px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 68px;
}
.icon-text_1235-940[_ngcontent-ng-c2702405285] {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_1235-941[_ngcontent-ng-c2702405285] {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}
.column_1134-1235[_ngcontent-ng-c2702405285] {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: 106px;
}
.header-item_1134-1236[_ngcontent-ng-c2702405285] {
  padding: 0px 12px 0px 12px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  width: 100%;
}
.table-item_1134-1237[_ngcontent-ng-c2702405285] {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 68px;
}
.text-label_1134-1239[_ngcontent-ng-c2702405285] {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}
.table-item_1134-1240[_ngcontent-ng-c2702405285] {
  padding: 10px 12px 10px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 68px;
}
.icon-text_1134-1241[_ngcontent-ng-c2702405285] {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_1134-1242[_ngcontent-ng-c2702405285] {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}
.column_1235-969[_ngcontent-ng-c2702405285] {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: 102px;
}
.header-item_1235-970[_ngcontent-ng-c2702405285] {
  padding: 0px 12px 0px 12px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  width: 100%;
}
.table-item_1235-971[_ngcontent-ng-c2702405285] {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 68px;
}
.text-label_1235-973[_ngcontent-ng-c2702405285] {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}
.table-item_1235-974[_ngcontent-ng-c2702405285] {
  padding: 10px 12px 10px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 68px;
}
.icon-text_1235-975[_ngcontent-ng-c2702405285] {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_1235-979[_ngcontent-ng-c2702405285] {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 78px;
}
.column_1134-1270[_ngcontent-ng-c2702405285] {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: 99px;
}
.header-item_1134-1271[_ngcontent-ng-c2702405285] {
  padding: 0px 12px 0px 12px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  width: 100%;
}
.table-item_1134-1272[_ngcontent-ng-c2702405285] {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 68px;
}
.text-label_1134-1274[_ngcontent-ng-c2702405285] {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}
.table-item_1134-1275[_ngcontent-ng-c2702405285] {
  padding: 10px 12px 10px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 68px;
}
.icon-text_1134-1276[_ngcontent-ng-c2702405285] {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_1134-1277[_ngcontent-ng-c2702405285] {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}
.no-charts-message[_ngcontent-ng-c2702405285] {
  padding: 40px 20px;
  text-align: center;
  font-size: 14px;
  font-family: "Urbane", sans-serif;
  color: #757575;
  font-style: italic;
  background: #ffffff;
}
/*# sourceMappingURL=/assigned-table.component.css.map */</style><style ng-app-id="ng">

.notes-container[_ngcontent-ng-c3668244963] {
  position: relative;
  width: 100%;
}
.notes-container.disabled[_ngcontent-ng-c3668244963] {
  opacity: 0.5;
  pointer-events: none;
}
.notes-label[_ngcontent-ng-c3668244963] {
  display: block;
  font-size: 10px;
  font-family: "Urbane", sans-serif;
  font-weight: 300;
  color: #547996;
  margin-bottom: 4px;
}
.notes-label[_ngcontent-ng-c3668244963]   .required-indicator[_ngcontent-ng-c3668244963] {
  color: #3870B8;
  margin-left: 2px;
}
.notes-input-wrapper[_ngcontent-ng-c3668244963] {
  position: relative;
  width: 100%;
  box-sizing: border-box;
}
.notes-textarea[_ngcontent-ng-c3668244963] {
  width: 100%;
  min-height: 120px;
  padding: 12px;
  border: 1px solid #D9E1E7;
  border-radius: 8px;
  background: #ffffff;
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  font-weight: 300;
  line-height: 16px;
  color: #547996;
  resize: vertical;
  box-sizing: border-box;
  outline: none;
  transition: all 0.2s ease;
}
.notes-textarea[_ngcontent-ng-c3668244963]::placeholder {
  color: #547996;
}
.notes-textarea[_ngcontent-ng-c3668244963]:focus {
  border-color: #547996;
  color: #17181A;
}
.notes-textarea.has-value[_ngcontent-ng-c3668244963] {
  color: #17181A;
}
.notes-textarea[_ngcontent-ng-c3668244963]:disabled {
  background-color: #F1F5F7;
  cursor: not-allowed;
  color: #547996;
}
.notes-textarea[_ngcontent-ng-c3668244963]:hover:not(:disabled) {
  border-color: #547996;
}
.notes-textarea[_ngcontent-ng-c3668244963]::-webkit-scrollbar {
  width: 6px;
}
.notes-textarea[_ngcontent-ng-c3668244963]::-webkit-scrollbar-track {
  background: transparent;
}
.notes-textarea[_ngcontent-ng-c3668244963]::-webkit-scrollbar-thumb {
  background: #D9E1E7;
  border-radius: 3px;
}
.notes-textarea[_ngcontent-ng-c3668244963]::-webkit-scrollbar-thumb:hover {
  background: #547996;
}
.character-counter[_ngcontent-ng-c3668244963] {
  position: absolute;
  bottom: 8px;
  right: 12px;
  font-size: 10px;
  font-family: "Urbane", sans-serif;
  font-weight: 300;
  line-height: 20px;
  color: #547996;
  background: rgba(255, 255, 255, 0.9);
  padding: 2px 4px;
  border-radius: 4px;
  pointer-events: none;
}
.character-counter.near-limit[_ngcontent-ng-c3668244963] {
  color: #F4A261;
}
.character-counter.over-limit[_ngcontent-ng-c3668244963] {
  color: #F4454E;
}
.error-message[_ngcontent-ng-c3668244963] {
  margin-top: 4px;
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  font-weight: 300;
  color: #F4454E;
  line-height: 16px;
}
@media (max-width: 599px) {
  .notes-textarea[_ngcontent-ng-c3668244963] {
    min-height: 100px;
    padding: 10px;
  }
  .character-counter[_ngcontent-ng-c3668244963] {
    font-size: 9px;
    bottom: 6px;
    right: 10px;
  }
}
@media (min-width: 600px) {
  .notes-textarea[_ngcontent-ng-c3668244963] {
    min-height: 120px;
    padding: 12px;
  }
  .character-counter[_ngcontent-ng-c3668244963] {
    font-size: 10px;
    bottom: 8px;
    right: 12px;
  }
}
/*# sourceMappingURL=/notes.component.css.map */</style><style ng-app-id="ng">

.demographics-container[_ngcontent-ng-c1061394248] {
  width: 100%;
  padding: 20px;
  background: #FFFFFF;
  border-radius: 8px;
  border: 1px solid #F1F5F7;
}
.demographics-content[_ngcontent-ng-c1061394248] {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 48px;
  width: 100%;
}
.left-section[_ngcontent-ng-c1061394248] {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  gap: 48px;
}
.header-section[_ngcontent-ng-c1061394248] {
  display: flex;
  align-items: flex-start;
  gap: 36px;
}
.back-button[_ngcontent-ng-c1061394248] {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: opacity 0.2s ease;
}
.back-button[_ngcontent-ng-c1061394248]:hover {
  opacity: 0.8;
}
.back-icon[_ngcontent-ng-c1061394248] {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #0071BC;
}
.back-text[_ngcontent-ng-c1061394248] {
  color: #0071BC;
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  font-weight: 500;
  line-height: 20px;
}
.measure-info[_ngcontent-ng-c1061394248] {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.measure-title[_ngcontent-ng-c1061394248] {
  color: #17181A;
  font-size: 20px;
  font-family: "Urbane", sans-serif;
  font-weight: 600;
  line-height: 32px;
}
.measure-subtitle[_ngcontent-ng-c1061394248] {
  color: #547996;
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  font-weight: 500;
  line-height: 20px;
}
.right-section[_ngcontent-ng-c1061394248] {
  display: flex;
  align-items: center;
  gap: 60px;
}
.demographics-group[_ngcontent-ng-c1061394248] {
  display: flex;
  align-items: center;
}
.demographics-group.primary-group[_ngcontent-ng-c1061394248] {
  gap: 40px;
}
.demographics-group.provider-group[_ngcontent-ng-c1061394248] {
  gap: 24px;
}
.demographic-item[_ngcontent-ng-c1061394248] {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: fit-content;
}
.demographic-item[_ngcontent-ng-c1061394248]:nth-child(1) {
  width: 68px;
}
.demographic-item[_ngcontent-ng-c1061394248]:nth-child(2) {
  width: 57px;
  min-width: 57px;
}
.demographic-item[_ngcontent-ng-c1061394248]:nth-child(3) {
  width: 69px;
}
.demographic-item[_ngcontent-ng-c1061394248]:nth-child(4) {
  min-width: auto;
}
.demographic-item[_ngcontent-ng-c1061394248]:nth-child(5) {
  width: 51px;
}
.provider-group[_ngcontent-ng-c1061394248]   .demographic-item[_ngcontent-ng-c1061394248]:nth-child(1) {
  width: 115px;
  min-width: 115px;
}
.provider-group[_ngcontent-ng-c1061394248]   .demographic-item[_ngcontent-ng-c1061394248]:nth-child(2) {
  width: 68px;
}
.demographic-value[_ngcontent-ng-c1061394248] {
  color: #17181A;
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  font-weight: 600;
  line-height: 20px;
  word-wrap: break-word;
}
.demographic-label[_ngcontent-ng-c1061394248] {
  color: #547996;
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  font-weight: 500;
  line-height: 20px;
  word-wrap: break-word;
}
@media (max-width: 599px) {
  .demographics-content[_ngcontent-ng-c1061394248] {
    flex-direction: column;
    gap: 24px;
  }
  .right-section[_ngcontent-ng-c1061394248] {
    flex-direction: column;
    gap: 24px;
    align-items: flex-start;
  }
  .demographics-group[_ngcontent-ng-c1061394248] {
    flex-wrap: wrap;
    gap: 16px !important;
  }
  .demographic-item[_ngcontent-ng-c1061394248] {
    min-width: auto;
    width: auto !important;
  }
  .header-section[_ngcontent-ng-c1061394248] {
    gap: 20px;
  }
}
@media (min-width: 600px) {
  .demographics-content[_ngcontent-ng-c1061394248] {
    flex-direction: row;
  }
  .right-section[_ngcontent-ng-c1061394248] {
    flex-direction: row;
  }
}
/*# sourceMappingURL=/demographics.component.css.map */</style><style ng-app-id="ng">

.hits-container[_ngcontent-ng-c1281433528] {
  padding: 20px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #f1f5f7;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 0px;
  width: 100%;
  box-sizing: border-box;
}
.hits-header[_ngcontent-ng-c1281433528] {
  padding: 0px 0px 12px 0px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  gap: 20px;
  width: 100%;
  box-sizing: border-box;
}
.hits-title[_ngcontent-ng-c1281433528] {
  color: #17181A;
  font-family: Urbane;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 32px;
}
.hits-table[_ngcontent-ng-c1281433528] {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 0px;
  width: 100%;
  box-sizing: border-box;
}
.hits-table-columns[_ngcontent-ng-c1281433528] {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 0px;
  width: 100%;
  box-sizing: border-box;
}
.hits-row[_ngcontent-ng-c1281433528] {
  display: flex;
  align-self: stretch;
}
.hits-row.highlighted[_ngcontent-ng-c1281433528] {
  background: #D9E1E7;
}
.hits-row[_ngcontent-ng-c1281433528]:not(.highlighted) {
  background: #FFFFFF;
}
.hits-column[_ngcontent-ng-c1281433528] {
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  display: inline-flex;
  box-sizing: border-box;
}
.hits-column.dos-column[_ngcontent-ng-c1281433528] {
  width: 80px;
}
.hits-column.sys-column[_ngcontent-ng-c1281433528] {
  width: 60px;
}
.hits-column.dias-column[_ngcontent-ng-c1281433528] {
  width: 60px;
}
.hits-column.page-column[_ngcontent-ng-c1281433528] {
  width: 60px;
}
.hits-column.comment-column[_ngcontent-ng-c1281433528] {
  width: 216px;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  display: inline-flex;
}
.hits-column.include-column[_ngcontent-ng-c1281433528] {
  width: 60px;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  display: inline-flex;
}
.hits-column.include-column[_ngcontent-ng-c1281433528]   .include-column-inner[_ngcontent-ng-c1281433528] {
  width: 60px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  display: flex;
}
.header-item[_ngcontent-ng-c1281433528] {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 10px;
  border-bottom: 1px solid #f1f5f7;
  height: 40px;
  width: 100%;
  box-sizing: border-box;
}
.header-item[_ngcontent-ng-c1281433528]   .table-item[_ngcontent-ng-c1281433528] {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  gap: 12px;
  height: 40px;
  box-sizing: border-box;
}
.header-item[_ngcontent-ng-c1281433528]   .table-item[_ngcontent-ng-c1281433528]   .label[_ngcontent-ng-c1281433528] {
  color: #17181A;
  font-size: 12px;
  font-family: Urbane;
  font-weight: 500;
  line-height: 20px;
  word-wrap: break-word;
}
.comment-column[_ngcontent-ng-c1281433528]   .header-item[_ngcontent-ng-c1281433528] {
  align-items: center;
}
.comment-column[_ngcontent-ng-c1281433528]   .header-item[_ngcontent-ng-c1281433528]   .table-item[_ngcontent-ng-c1281433528] {
  justify-content: center;
  width: 100%;
}
.include-column[_ngcontent-ng-c1281433528]   .header-item[_ngcontent-ng-c1281433528] {
  align-items: center;
}
.include-column[_ngcontent-ng-c1281433528]   .header-item[_ngcontent-ng-c1281433528]   .table-item[_ngcontent-ng-c1281433528] {
  justify-content: center;
  width: 100%;
}
.table-item[_ngcontent-ng-c1281433528] {
  height: 40px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  width: 100%;
  padding: 10px 8px 10px 8px;
}
.table-item.highlighted[_ngcontent-ng-c1281433528] {
  background: #D9E1E7;
}
.table-item[_ngcontent-ng-c1281433528]:not(.highlighted) {
  background: #ffffff;
}
.comment-column[_ngcontent-ng-c1281433528]   .table-item[_ngcontent-ng-c1281433528] {
  padding: 2px 0px 2px 0px;
  justify-content: center;
}
.table-item[_ngcontent-ng-c1281433528]   .icon-text[_ngcontent-ng-c1281433528] {
  justify-content: flex-start;
  align-items: center;
  gap: 12px;
  display: flex;
  width: 100%;
}
.table-item[_ngcontent-ng-c1281433528]   .icon-text[_ngcontent-ng-c1281433528]   .label[_ngcontent-ng-c1281433528] {
  text-box-trim: trim-both;
  text-box-edge: cap alphabetic;
  color: #17181A;
  font-size: 12px;
  font-family: Urbane;
  font-weight: 300;
  line-height: 16px;
  word-wrap: break-word;
}
.dos-column[_ngcontent-ng-c1281433528]   .cell-content[_ngcontent-ng-c1281433528], 
.sys-column[_ngcontent-ng-c1281433528]   .cell-content[_ngcontent-ng-c1281433528], 
.dias-column[_ngcontent-ng-c1281433528]   .cell-content[_ngcontent-ng-c1281433528] {
  color: #17181A;
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  font-weight: 300;
  line-height: 16px;
}
.page-link[_ngcontent-ng-c1281433528] {
  background: none;
  border: none;
  color: #0071BC;
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  font-weight: 300;
  text-decoration: underline;
  line-height: 16px;
  cursor: pointer;
  padding: 0;
}
.page-link[_ngcontent-ng-c1281433528]:hover {
  opacity: 0.8;
}
.page-link[_ngcontent-ng-c1281433528]:focus {
  outline: 2px solid #3870B8;
  outline-offset: 2px;
}
.comment-box-component[_ngcontent-ng-c1281433528] {
  height: 30px;
  width: 100%;
}
.checkbox-wrapper[_ngcontent-ng-c1281433528] {
  width: 60px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.include-checkbox[_ngcontent-ng-c1281433528] {
  width: 16px;
  height: 16px;
  border-radius: 5px;
  border: 1px solid #D9E1E7;
  background-color: #FFFFFF;
  cursor: pointer;
  transition: all 0.2s ease;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  position: relative;
  width: 16px;
  height: 16px;
  margin: 0;
}
.include-checkbox[_ngcontent-ng-c1281433528]:checked {
  background-color: #17181A;
  border-color: #17181A;
}
.include-checkbox[_ngcontent-ng-c1281433528]:checked::after {
  content: "";
  position: absolute;
  left: 4px;
  top: 4px;
  width: 8.33px;
  height: 7.5px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8.33' height='7.5' viewBox='0 0 8.33 7.5'%3E%3Cpath d='M1 3.75L3.5 6.25L7.33 1.25' stroke='white' stroke-width='1.5' fill='none' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
.include-checkbox[_ngcontent-ng-c1281433528]:hover:not(:disabled) {
  border-color: #547996;
}
.include-checkbox[_ngcontent-ng-c1281433528]:focus {
  outline: 2px solid #3870B8;
  outline-offset: 2px;
}
.include-checkbox[_ngcontent-ng-c1281433528]:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.checkbox-label[_ngcontent-ng-c1281433528] {
  display: none;
}
@media (max-width: 599px) {
  .hits-container[_ngcontent-ng-c1281433528] {
    padding: 16px;
  }
  .hits-table[_ngcontent-ng-c1281433528] {
    overflow-x: auto;
  }
  .hits-column[_ngcontent-ng-c1281433528] {
    min-width: 60px;
    padding: 0;
  }
  .hits-column.dos-column[_ngcontent-ng-c1281433528] {
    width: 70px;
  }
  .hits-column.sys-column[_ngcontent-ng-c1281433528] {
    width: 50px;
  }
  .hits-column.dias-column[_ngcontent-ng-c1281433528] {
    width: 50px;
  }
  .hits-column.page-column[_ngcontent-ng-c1281433528] {
    width: 50px;
  }
  .hits-column.comment-column[_ngcontent-ng-c1281433528] {
    width: 180px;
  }
}
@media (min-width: 600px) {
  .hits-container[_ngcontent-ng-c1281433528] {
    padding: 20px;
  }
}
/*# sourceMappingURL=/hits.component.css.map */</style><style ng-app-id="ng">

.menu-container[_ngcontent-ng-c3806090911] {
  width: 100%;
  height: 80px;
  background: #FFFFFF;
  border-bottom: 1px solid #F1F5F7;
  position: sticky;
  top: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
}
.menu-content[_ngcontent-ng-c3806090911] {
  width: 100%;
  height: 100%;
  padding: 12px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.logo-section[_ngcontent-ng-c3806090911] {
  flex: 1;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.logo-container[_ngcontent-ng-c3806090911] {
  width: 240px;
  padding: 10px 20px;
  background: #FFFFFF;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: opacity 0.2s ease;
}
.logo-container[_ngcontent-ng-c3806090911]:hover {
  opacity: 0.8;
}
.logo-image[_ngcontent-ng-c3806090911] {
  width: 150px;
  height: 37.4px;
  object-fit: contain;
}
.logo-placeholder[_ngcontent-ng-c3806090911] {
  font-size: 18px;
  font-family: "Urbane", sans-serif;
  font-weight: 600;
  color: #17181A;
}
.user-section[_ngcontent-ng-c3806090911] {
  position: relative;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 16px;
}
.user-info[_ngcontent-ng-c3806090911] {
  padding: 12px 20px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 12px;
}
.user-name[_ngcontent-ng-c3806090911] {
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  font-weight: 300;
  line-height: 20px;
  color: #17181A;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.user-avatar[_ngcontent-ng-c3806090911] {
  cursor: pointer;
  transition: transform 0.2s ease;
}
.user-avatar[_ngcontent-ng-c3806090911]:hover {
  transform: scale(1.05);
}
.avatar-circle[_ngcontent-ng-c3806090911] {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #F1F5F7;
  border: 1px solid #D9E1E7;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}
.avatar-circle[_ngcontent-ng-c3806090911]:hover {
  border-color: #547996;
  background: #FFFFFF;
}
.user-icon[_ngcontent-ng-c3806090911] {
  width: 20px;
  height: 20px;
  color: #547996;
  transition: color 0.2s ease;
}
.avatar-circle[_ngcontent-ng-c3806090911]:hover   .user-icon[_ngcontent-ng-c3806090911] {
  color: #17181A;
}
.dropdown-arrow[_ngcontent-ng-c3806090911] {
  cursor: pointer;
  transition: transform 0.2s ease;
  padding: 4px;
}
.dropdown-arrow.open[_ngcontent-ng-c3806090911] {
  transform: rotate(180deg);
}
.dropdown-arrow[_ngcontent-ng-c3806090911]:hover {
  background-color: #F1F5F7;
  border-radius: 4px;
}
.arrow-icon[_ngcontent-ng-c3806090911] {
  width: 16px;
  height: 16px;
  color: #547996;
  transition: color 0.2s ease;
}
.dropdown-arrow[_ngcontent-ng-c3806090911]:hover   .arrow-icon[_ngcontent-ng-c3806090911] {
  color: #17181A;
}
.user-dropdown[_ngcontent-ng-c3806090911] {
  position: absolute;
  top: 100%;
  right: 0;
  background: #FFFFFF;
  border-radius: 10px;
  border: 1px solid #D9E1E7;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1001;
  margin-top: 8px;
  min-width: 200px;
  overflow: hidden;
}
.dropdown-content[_ngcontent-ng-c3806090911] {
  padding: 8px 0;
}
.dropdown-item[_ngcontent-ng-c3806090911] {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  font-weight: 300;
  color: #17181A;
}
.dropdown-item[_ngcontent-ng-c3806090911]:hover {
  background-color: #F1F5F7;
}
.dropdown-item[_ngcontent-ng-c3806090911]:active {
  background-color: #D9E1E7;
}
.item-icon[_ngcontent-ng-c3806090911] {
  font-size: 14px;
  width: 16px;
  text-align: center;
}
.item-label[_ngcontent-ng-c3806090911] {
  flex: 1;
}
@media (min-width: 600px) {
  .menu-content[_ngcontent-ng-c3806090911] {
    padding: 12px 20px;
  }
  .logo-container[_ngcontent-ng-c3806090911] {
    width: 200px;
    padding: 8px 16px;
  }
  .logo-image[_ngcontent-ng-c3806090911] {
    width: 120px;
    height: 30px;
  }
}
@media (max-width: 599px) {
  .menu-content[_ngcontent-ng-c3806090911] {
    padding: 8px 16px;
  }
  .logo-container[_ngcontent-ng-c3806090911] {
    width: auto;
    padding: 4px 8px;
  }
  .logo-image[_ngcontent-ng-c3806090911] {
    width: 100px;
    height: 25px;
  }
  .user-name[_ngcontent-ng-c3806090911] {
    display: none;
  }
  .user-dropdown[_ngcontent-ng-c3806090911] {
    right: -16px;
    min-width: 180px;
  }
}
/*# sourceMappingURL=/menu.component.css.map */</style><style ng-app-id="ng">

.inclusions-tab-content[_ngcontent-ng-c1722032080] {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 12px;
  box-sizing: border-box;
  width: 100%;
}
.frame-942[_ngcontent-ng-c1722032080] {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 8px;
  box-sizing: border-box;
}
.frame-936[_ngcontent-ng-c1722032080] {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 100%;
}
.frame-939[_ngcontent-ng-c1722032080] {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 100%;
}
.frame-940[_ngcontent-ng-c1722032080] {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 127px;
}
.frame-941[_ngcontent-ng-c1722032080] {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 127px;
}
.frame-937[_ngcontent-ng-c1722032080] {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 215px;
}
.frame-938[_ngcontent-ng-c1722032080] {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 100%;
}
.text-sys[_ngcontent-ng-c1722032080], 
.text-dias[_ngcontent-ng-c1722032080], 
.text-date-of-service[_ngcontent-ng-c1722032080], 
.text-notes[_ngcontent-ng-c1722032080] {
  color: #547996;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}
.dropdown-inclusion[_ngcontent-ng-c1722032080] {
  padding: 12px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 10px;
  border-color: #D9E1E7;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  height: 48px;
  width: 100%;
  outline: none;
  color: #547996;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  font-weight: 300;
  text-align: left;
}
.dropdown-inclusion[_ngcontent-ng-c1722032080]::placeholder {
  color: #547996;
}
.dropdown-inclusion[_ngcontent-ng-c1722032080]:focus {
  border-color: #547996;
}
.dropdown-inclusion[_ngcontent-ng-c1722032080]:disabled {
  background-color: #F1F5F7;
  cursor: not-allowed;
  color: #547996;
}
.calendar[_ngcontent-ng-c1722032080] {
  height: 48px;
  width: 100%;
}
.notes[_ngcontent-ng-c1722032080] {
  width: 100%;
}
@media (max-width: 768px) {
  .frame-939[_ngcontent-ng-c1722032080] {
    flex-direction: column;
    gap: 8px;
  }
  .frame-940[_ngcontent-ng-c1722032080], 
   .frame-941[_ngcontent-ng-c1722032080], 
   .frame-937[_ngcontent-ng-c1722032080] {
    width: 100%;
  }
}
/*# sourceMappingURL=/inclusions-tab.component.css.map */</style><style ng-app-id="ng">

.submit-button-default[_ngcontent-ng-c2891085390] {
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 12px;
  font-family: Urbane;
  font-weight: 500;
  line-height: 20px;
  text-align: center;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 80px;
  transition: all 0.2s ease;
  background-color: #1976d2;
  color: #ffffff;
  border: none;
  cursor: pointer;
}
.submit-button-default[_ngcontent-ng-c2891085390]:hover {
  background-color: #1565c0;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.submit-button-default[_ngcontent-ng-c2891085390]:active {
  background-color: #0d47a1;
  transform: translateY(0);
}
.submit-button-inactive[_ngcontent-ng-c2891085390] {
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 12px;
  font-family: Urbane;
  font-weight: 500;
  line-height: 20px;
  text-align: center;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 80px;
  background-color: #BFD0EE;
  color: #ffffff;
  border: none;
  cursor: not-allowed;
  opacity: 0.8;
}
.submit-button-inactive[_ngcontent-ng-c2891085390]:hover {
  background-color: #BFD0EE;
  transform: none;
  box-shadow: none;
}
/*# sourceMappingURL=/submit-button.component.css.map */</style><style ng-app-id="ng">

.comment-box[_ngcontent-ng-c62406540] {
  flex: 1 1 0;
  height: 30px;
  padding: 4px;
  background: white !important;
  overflow: hidden;
  border-radius: 10px;
  outline-offset: -1px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  display: inline-flex;
  box-sizing: border-box;
}
.comment-box.default[_ngcontent-ng-c62406540] {
  outline: 1px #D9E1E7 solid;
}
.comment-box.active[_ngcontent-ng-c62406540] {
  outline: 1px #547996 solid;
}
.comment-box.entered[_ngcontent-ng-c62406540] {
  outline: 1px #D9E1E7 solid;
}
.comment-box.disabled[_ngcontent-ng-c62406540] {
  background-color: #F1F5F7;
  cursor: not-allowed;
}
.row[_ngcontent-ng-c62406540] {
  align-self: stretch;
  padding-left: 12px;
  padding-right: 12px;
  background: transparent;
  border-radius: 6px;
  justify-content: flex-start;
  align-items: center;
  gap: 12px;
  display: inline-flex;
}
.comment-input[_ngcontent-ng-c62406540] {
  flex: 1 1 0;
  border: none !important;
  outline: none !important;
  background: transparent !important;
  color: #17181A !important;
  font-size: 10px;
  font-family: "Urbane", sans-serif;
  font-weight: 300;
  line-height: 12px;
  word-wrap: break-word;
  padding: 0;
  width: 100%;
  height: 100%;
}
.comment-input[_ngcontent-ng-c62406540]::placeholder {
  color: #547996;
}
.active[_ngcontent-ng-c62406540]   .comment-input[_ngcontent-ng-c62406540], 
.entered[_ngcontent-ng-c62406540]   .comment-input[_ngcontent-ng-c62406540] {
  color: #17181A;
}
.default[_ngcontent-ng-c62406540]   .comment-input[_ngcontent-ng-c62406540] {
  color: #547996;
}
.comment-input[_ngcontent-ng-c62406540]:disabled {
  cursor: not-allowed;
  color: #547996;
}
.comment-input[_ngcontent-ng-c62406540]:focus {
  outline: none;
}
/*# sourceMappingURL=/comment-box.component.css.map */</style><style ng-app-id="ng">

.exclusions-tab-content[_ngcontent-ng-c2467306289] {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: stretch;
  gap: 12px;
  box-sizing: border-box;
  width: 100% !important;
  max-width: none !important;
  min-width: 0 !important;
}
.frame-916[_ngcontent-ng-c2467306289] {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: stretch;
  gap: 12px;
  box-sizing: border-box;
  width: 100% !important;
  max-width: none !important;
  min-width: 0 !important;
}
.frame-939[_ngcontent-ng-c2467306289] {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: stretch;
  gap: 4px;
  box-sizing: border-box;
  width: 100% !important;
  max-width: none !important;
  min-width: 0 !important;
}
.frame-940[_ngcontent-ng-c2467306289] {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: stretch;
  gap: 4px;
  box-sizing: border-box;
  width: 100% !important;
  max-width: none !important;
  min-width: 0 !important;
}
.frame-941[_ngcontent-ng-c2467306289] {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: stretch;
  gap: 4px;
  box-sizing: border-box;
  width: 100% !important;
  max-width: none !important;
  min-width: 0 !important;
}
.text-reasoning[_ngcontent-ng-c2467306289], 
.text-date-of-service[_ngcontent-ng-c2467306289], 
.text-notes[_ngcontent-ng-c2467306289] {
  color: #547996;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}
.dropdown-exclusion[_ngcontent-ng-c2467306289] {
  height: 44px;
  width: 100% !important;
  max-width: none !important;
  min-width: 0 !important;
  flex: 1 1 100% !important;
  box-sizing: border-box;
}
.calendar[_ngcontent-ng-c2467306289] {
  height: 48px;
  width: 100% !important;
  max-width: none !important;
  min-width: 0 !important;
  flex: 1 1 100% !important;
  box-sizing: border-box;
}
.notes[_ngcontent-ng-c2467306289] {
  width: 100% !important;
  max-width: none !important;
  min-width: 0 !important;
  flex: 1 1 100% !important;
  box-sizing: border-box;
}
@media (max-width: 768px) {
  .frame-916[_ngcontent-ng-c2467306289] {
    gap: 8px;
  }
}
/*# sourceMappingURL=/exclusions-tab.component.css.map */</style></head>
<body class="mat-typography"><!--nghm--><script type="text/javascript" id="ng-event-dispatch-contract">(()=>{function p(t,n,r,o,e,i,f,m){return{eventType:t,event:n,targetElement:r,eic:o,timeStamp:e,eia:i,eirp:f,eiack:m}}function u(t){let n=[],r=e=>{n.push(e)};return{c:t,q:n,et:[],etc:[],d:r,h:e=>{r(p(e.type,e,e.target,t,Date.now()))}}}function s(t,n,r){for(let o=0;o<n.length;o++){let e=n[o];(r?t.etc:t.et).push(e),t.c.addEventListener(e,t.h,r)}}function c(t,n,r,o,e=window){let i=u(t);e._ejsas||(e._ejsas={}),e._ejsas[n]=i,s(i,r),s(i,o,!0)}window.__jsaction_bootstrap=c;})();
</script><script>window.__jsaction_bootstrap(document.body,"ng",["click","keydown","mousedown","mouseup","change","input","submit","compositionstart","compositionend"],["blur","focus"]);</script>
  <app-root ng-version="19.2.9" ngh="19" ng-server-context="ssg"><router-outlet></router-outlet><app-component-test _nghost-ng-c3567730054="" ngh="18"><div _ngcontent-ng-c3567730054="" class="component-test-container"><h1 _ngcontent-ng-c3567730054="" class="page-title">Component Test Page</h1><p _ngcontent-ng-c3567730054="" class="page-description">This page showcases all implemented components with their different states and variations.</p><section _ngcontent-ng-c3567730054="" class="component-section"><h2 _ngcontent-ng-c3567730054="" class="section-title">Buttons</h2><p _ngcontent-ng-c3567730054="" class="section-description"> The following demos show the four button states from the Figma style guide: <strong _ngcontent-ng-c3567730054="">Inactive</strong>, <strong _ngcontent-ng-c3567730054="">Default</strong>, <strong _ngcontent-ng-c3567730054="">Hover</strong>, and <strong _ngcontent-ng-c3567730054="">Click</strong>. Static state demos show each state individually, while interactive demos show natural state transitions on hover and click. </p><div _ngcontent-ng-c3567730054="" class="component-row"><div _ngcontent-ng-c3567730054="" class="component-item"><h3 _ngcontent-ng-c3567730054="" class="component-title">Primary Button - Inactive</h3><div _ngcontent-ng-c3567730054="" class="component-subtitle">Style Guide: "Buttons, selectors, and icons" | Implementation: "ButtonComponent"</div><div _ngcontent-ng-c3567730054="" class="component-demo figma-sized button-demo"><app-button _ngcontent-ng-c3567730054="" variant="primary" figmastate="inactive" _nghost-ng-c502759576="" ng-reflect-variant="primary" ng-reflect-figma-state="inactive" ng-reflect-figma-exact="true" ngh="0"><button _ngcontent-ng-c502759576="" type="button" disabled="" ng-reflect-ng-class="button,button-primary,,,button" class="button button-primary button-icon-left figma-exact figma-state-inactive" jsaction="click:;mousedown:;mouseup:;"><!--bindings={
  "ng-reflect-ng-if": null
}-->Submit<!--bindings={
  "ng-reflect-ng-if": null
}--></button></app-button></div><div _ngcontent-ng-c3567730054="" class="component-code"><pre _ngcontent-ng-c3567730054=""><code _ngcontent-ng-c3567730054="">&lt;app-button variant="primary" figmaState="inactive"&gt;Submit&lt;/app-button&gt;</code></pre></div></div><div _ngcontent-ng-c3567730054="" class="component-item"><h3 _ngcontent-ng-c3567730054="" class="component-title">Primary Button - Default</h3><div _ngcontent-ng-c3567730054="" class="component-subtitle">Style Guide: "Buttons, selectors, and icons" | Implementation: "ButtonComponent"</div><div _ngcontent-ng-c3567730054="" class="component-demo figma-sized button-demo"><app-button _ngcontent-ng-c3567730054="" variant="primary" figmastate="default" _nghost-ng-c502759576="" ng-reflect-variant="primary" ng-reflect-figma-state="default" ng-reflect-figma-exact="true" ngh="0" jsaction="click:;"><button _ngcontent-ng-c502759576="" type="button" ng-reflect-ng-class="button,button-primary,,,button" class="button button-primary button-icon-left figma-exact figma-state-default" jsaction="click:;mousedown:;mouseup:;"><!--bindings={
  "ng-reflect-ng-if": null
}-->Submit<!--bindings={
  "ng-reflect-ng-if": null
}--></button></app-button></div><div _ngcontent-ng-c3567730054="" class="component-code"><pre _ngcontent-ng-c3567730054=""><code _ngcontent-ng-c3567730054="">&lt;app-button variant="primary" figmaState="default"&gt;Submit&lt;/app-button&gt;</code></pre></div></div></div><div _ngcontent-ng-c3567730054="" class="component-row"><div _ngcontent-ng-c3567730054="" class="component-item"><h3 _ngcontent-ng-c3567730054="" class="component-title">Primary Button - Hover</h3><div _ngcontent-ng-c3567730054="" class="component-subtitle">Style Guide: "Buttons, selectors, and icons" | Implementation: "ButtonComponent"</div><div _ngcontent-ng-c3567730054="" class="component-demo figma-sized button-demo"><app-button _ngcontent-ng-c3567730054="" variant="primary" figmastate="hover" _nghost-ng-c502759576="" ng-reflect-variant="primary" ng-reflect-figma-state="hover" ng-reflect-figma-exact="true" ngh="0" jsaction="click:;"><button _ngcontent-ng-c502759576="" type="button" ng-reflect-ng-class="button,button-primary,,,button" class="button button-primary button-icon-left figma-exact figma-state-hover" jsaction="click:;mousedown:;mouseup:;"><!--bindings={
  "ng-reflect-ng-if": null
}-->Submit<!--bindings={
  "ng-reflect-ng-if": null
}--></button></app-button></div><div _ngcontent-ng-c3567730054="" class="component-code"><pre _ngcontent-ng-c3567730054=""><code _ngcontent-ng-c3567730054="">&lt;app-button variant="primary" figmaState="hover"&gt;Submit&lt;/app-button&gt;</code></pre></div></div><div _ngcontent-ng-c3567730054="" class="component-item"><h3 _ngcontent-ng-c3567730054="" class="component-title">Primary Button - Click</h3><div _ngcontent-ng-c3567730054="" class="component-subtitle">Style Guide: "Buttons, selectors, and icons" | Implementation: "ButtonComponent"</div><div _ngcontent-ng-c3567730054="" class="component-demo figma-sized button-demo"><app-button _ngcontent-ng-c3567730054="" variant="primary" figmastate="click" _nghost-ng-c502759576="" ng-reflect-variant="primary" ng-reflect-figma-state="click" ng-reflect-figma-exact="true" ngh="0" jsaction="click:;"><button _ngcontent-ng-c502759576="" type="button" ng-reflect-ng-class="button,button-primary,,,button" class="button button-primary button-icon-left figma-exact figma-state-click" jsaction="click:;mousedown:;mouseup:;"><!--bindings={
  "ng-reflect-ng-if": null
}-->Submit<!--bindings={
  "ng-reflect-ng-if": null
}--></button></app-button></div><div _ngcontent-ng-c3567730054="" class="component-code"><pre _ngcontent-ng-c3567730054=""><code _ngcontent-ng-c3567730054="">&lt;app-button variant="primary" figmaState="click"&gt;Submit&lt;/app-button&gt;</code></pre></div></div></div><div _ngcontent-ng-c3567730054="" class="component-row"><div _ngcontent-ng-c3567730054="" class="component-item"><h3 _ngcontent-ng-c3567730054="" class="component-title">Secondary Button - Default</h3><div _ngcontent-ng-c3567730054="" class="component-subtitle">Style Guide: "Buttons, selectors, and icons" | Implementation: "ButtonComponent"</div><div _ngcontent-ng-c3567730054="" class="component-demo figma-sized button-demo"><app-button _ngcontent-ng-c3567730054="" variant="secondary" figmastate="default" _nghost-ng-c502759576="" ng-reflect-variant="secondary" ng-reflect-figma-state="default" ng-reflect-figma-exact="true" ngh="1" jsaction="click:;"><button _ngcontent-ng-c502759576="" type="button" ng-reflect-ng-class="button,button-secondary,,,butt" class="button button-secondary button-icon-left figma-exact figma-state-default" jsaction="click:;mousedown:;mouseup:;"><!--bindings={
  "ng-reflect-ng-if": null
}--><app-refresh-icon _ngcontent-ng-c3567730054="" color="default" _nghost-ng-c2659116941="" ng-reflect-color="default" ngh="2"><svg _ngcontent-ng-c2659116941="" width="16" height="16" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" ng-reflect-ng-class="refresh-icon-default" class="refresh-icon-default"><path _ngcontent-ng-c2659116941="" d="M9.9993 18.3332C14.6017 18.3332 18.3327 14.6022 18.3327 9.9998C18.3327 5.3975 14.6017 1.6665 9.9993 1.6665C5.397 1.6665 1.666 5.3975 1.666 9.9998C1.666 14.6022 5.397 18.3332 9.9993 18.3332Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path _ngcontent-ng-c2659116941="" d="M6.6758 12.0917C6.8258 12.3417 7.0091 12.5751 7.2174 12.7834C8.7508 14.3167 11.2424 14.3167 12.7841 12.7834C13.4091 12.1584 13.7674 11.3667 13.8841 10.5583" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path _ngcontent-ng-c2659116941="" d="M6.1172 9.4417C6.2339 8.625 6.5922 7.8417 7.2172 7.2167C8.7505 5.6833 11.2422 5.6833 12.7839 7.2167C13.0005 7.4333 13.1755 7.6667 13.3255 7.9083" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path _ngcontent-ng-c2659116941="" d="M6.5176 14.3165V12.0916H8.7426" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path _ngcontent-ng-c2659116941="" d="M13.4848 5.6831V7.9081H11.2598" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></app-refresh-icon>Refresh charts <!--bindings={
  "ng-reflect-ng-if": null
}--></button></app-button></div><div _ngcontent-ng-c3567730054="" class="component-code"><pre _ngcontent-ng-c3567730054=""><code _ngcontent-ng-c3567730054="">&lt;app-button variant="secondary" figmaState="default"&gt;Refresh charts&lt;/app-button&gt;</code></pre></div></div><div _ngcontent-ng-c3567730054="" class="component-item"><h3 _ngcontent-ng-c3567730054="" class="component-title">Secondary Button - Hover</h3><div _ngcontent-ng-c3567730054="" class="component-subtitle">Style Guide: "Buttons, selectors, and icons" | Implementation: "ButtonComponent"</div><div _ngcontent-ng-c3567730054="" class="component-demo figma-sized button-demo"><app-button _ngcontent-ng-c3567730054="" variant="secondary" figmastate="hover" _nghost-ng-c502759576="" ng-reflect-variant="secondary" ng-reflect-figma-state="hover" ng-reflect-figma-exact="true" ngh="1" jsaction="click:;"><button _ngcontent-ng-c502759576="" type="button" ng-reflect-ng-class="button,button-secondary,,,butt" class="button button-secondary button-icon-left figma-exact figma-state-hover" jsaction="click:;mousedown:;mouseup:;"><!--bindings={
  "ng-reflect-ng-if": null
}--><app-refresh-icon _ngcontent-ng-c3567730054="" color="hover" _nghost-ng-c2659116941="" ng-reflect-color="hover" ngh="2"><svg _ngcontent-ng-c2659116941="" width="16" height="16" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" ng-reflect-ng-class="refresh-icon-hover" class="refresh-icon-hover"><path _ngcontent-ng-c2659116941="" d="M9.9993 18.3332C14.6017 18.3332 18.3327 14.6022 18.3327 9.9998C18.3327 5.3975 14.6017 1.6665 9.9993 1.6665C5.397 1.6665 1.666 5.3975 1.666 9.9998C1.666 14.6022 5.397 18.3332 9.9993 18.3332Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path _ngcontent-ng-c2659116941="" d="M6.6758 12.0917C6.8258 12.3417 7.0091 12.5751 7.2174 12.7834C8.7508 14.3167 11.2424 14.3167 12.7841 12.7834C13.4091 12.1584 13.7674 11.3667 13.8841 10.5583" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path _ngcontent-ng-c2659116941="" d="M6.1172 9.4417C6.2339 8.625 6.5922 7.8417 7.2172 7.2167C8.7505 5.6833 11.2422 5.6833 12.7839 7.2167C13.0005 7.4333 13.1755 7.6667 13.3255 7.9083" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path _ngcontent-ng-c2659116941="" d="M6.5176 14.3165V12.0916H8.7426" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path _ngcontent-ng-c2659116941="" d="M13.4848 5.6831V7.9081H11.2598" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></app-refresh-icon>Refresh charts <!--bindings={
  "ng-reflect-ng-if": null
}--></button></app-button></div><div _ngcontent-ng-c3567730054="" class="component-code"><pre _ngcontent-ng-c3567730054=""><code _ngcontent-ng-c3567730054="">&lt;app-button variant="secondary" figmaState="hover"&gt;Refresh charts&lt;/app-button&gt;</code></pre></div></div></div><div _ngcontent-ng-c3567730054="" class="component-row"><div _ngcontent-ng-c3567730054="" class="component-item"><h3 _ngcontent-ng-c3567730054="" class="component-title">Secondary Button - Click</h3><div _ngcontent-ng-c3567730054="" class="component-subtitle">Style Guide: "Buttons, selectors, and icons" | Implementation: "ButtonComponent"</div><div _ngcontent-ng-c3567730054="" class="component-demo figma-sized button-demo"><app-button _ngcontent-ng-c3567730054="" variant="secondary" figmastate="click" _nghost-ng-c502759576="" ng-reflect-variant="secondary" ng-reflect-figma-state="click" ng-reflect-figma-exact="true" ngh="1" jsaction="click:;"><button _ngcontent-ng-c502759576="" type="button" ng-reflect-ng-class="button,button-secondary,,,butt" class="button button-secondary button-icon-left figma-exact figma-state-click" jsaction="click:;mousedown:;mouseup:;"><!--bindings={
  "ng-reflect-ng-if": null
}--><app-refresh-icon _ngcontent-ng-c3567730054="" color="click" _nghost-ng-c2659116941="" ng-reflect-color="click" ngh="2"><svg _ngcontent-ng-c2659116941="" width="16" height="16" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" ng-reflect-ng-class="refresh-icon-click" class="refresh-icon-click"><path _ngcontent-ng-c2659116941="" d="M9.9993 18.3332C14.6017 18.3332 18.3327 14.6022 18.3327 9.9998C18.3327 5.3975 14.6017 1.6665 9.9993 1.6665C5.397 1.6665 1.666 5.3975 1.666 9.9998C1.666 14.6022 5.397 18.3332 9.9993 18.3332Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path _ngcontent-ng-c2659116941="" d="M6.6758 12.0917C6.8258 12.3417 7.0091 12.5751 7.2174 12.7834C8.7508 14.3167 11.2424 14.3167 12.7841 12.7834C13.4091 12.1584 13.7674 11.3667 13.8841 10.5583" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path _ngcontent-ng-c2659116941="" d="M6.1172 9.4417C6.2339 8.625 6.5922 7.8417 7.2172 7.2167C8.7505 5.6833 11.2422 5.6833 12.7839 7.2167C13.0005 7.4333 13.1755 7.6667 13.3255 7.9083" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path _ngcontent-ng-c2659116941="" d="M6.5176 14.3165V12.0916H8.7426" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path _ngcontent-ng-c2659116941="" d="M13.4848 5.6831V7.9081H11.2598" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></app-refresh-icon>Refresh charts <!--bindings={
  "ng-reflect-ng-if": null
}--></button></app-button></div><div _ngcontent-ng-c3567730054="" class="component-code"><pre _ngcontent-ng-c3567730054=""><code _ngcontent-ng-c3567730054="">&lt;app-button variant="secondary" figmaState="click"&gt;Refresh charts&lt;/app-button&gt;</code></pre></div></div></div><h3 _ngcontent-ng-c3567730054="" class="subsection-title">Interactive Button Demos</h3><p _ngcontent-ng-c3567730054="" class="subsection-description">These buttons demonstrate natural state transitions. Hover and click to see the Figma-specified color changes.</p><div _ngcontent-ng-c3567730054="" class="component-row"><div _ngcontent-ng-c3567730054="" class="component-item"><h3 _ngcontent-ng-c3567730054="" class="component-title">Interactive Primary Button Demo</h3><div _ngcontent-ng-c3567730054="" class="component-subtitle">Hover and click to see natural state transitions</div><div _ngcontent-ng-c3567730054="" class="component-demo figma-sized button-demo"><app-button _ngcontent-ng-c3567730054="" variant="primary" _nghost-ng-c502759576="" ng-reflect-variant="primary" ng-reflect-figma-exact="true" ngh="0" jsaction="click:;"><button _ngcontent-ng-c502759576="" type="button" ng-reflect-ng-class="button,button-primary,,,button" class="button button-primary button-icon-left figma-exact" jsaction="click:;mousedown:;mouseup:;"><!--bindings={
  "ng-reflect-ng-if": null
}-->Submit<!--bindings={
  "ng-reflect-ng-if": null
}--></button></app-button></div><div _ngcontent-ng-c3567730054="" class="component-code"><pre _ngcontent-ng-c3567730054=""><code _ngcontent-ng-c3567730054="">&lt;app-button variant="primary" [figmaExact]="true"&gt;Submit&lt;/app-button&gt;</code></pre></div></div><div _ngcontent-ng-c3567730054="" class="component-item"><h3 _ngcontent-ng-c3567730054="" class="component-title">Interactive Secondary Button Demo</h3><div _ngcontent-ng-c3567730054="" class="component-subtitle">Hover and click to see natural state transitions</div><div _ngcontent-ng-c3567730054="" class="component-demo figma-sized button-demo"><app-button _ngcontent-ng-c3567730054="" variant="secondary" _nghost-ng-c502759576="" ng-reflect-variant="secondary" ng-reflect-figma-exact="true" ngh="1" jsaction="click:;"><button _ngcontent-ng-c502759576="" type="button" ng-reflect-ng-class="button,button-secondary,,,butt" class="button button-secondary button-icon-left figma-exact" jsaction="click:;mousedown:;mouseup:;"><!--bindings={
  "ng-reflect-ng-if": null
}--><app-refresh-icon _ngcontent-ng-c3567730054="" _nghost-ng-c2659116941="" ngh="2"><svg _ngcontent-ng-c2659116941="" width="16" height="16" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" ng-reflect-ng-class="refresh-icon-default" class="refresh-icon-default"><path _ngcontent-ng-c2659116941="" d="M9.9993 18.3332C14.6017 18.3332 18.3327 14.6022 18.3327 9.9998C18.3327 5.3975 14.6017 1.6665 9.9993 1.6665C5.397 1.6665 1.666 5.3975 1.666 9.9998C1.666 14.6022 5.397 18.3332 9.9993 18.3332Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path _ngcontent-ng-c2659116941="" d="M6.6758 12.0917C6.8258 12.3417 7.0091 12.5751 7.2174 12.7834C8.7508 14.3167 11.2424 14.3167 12.7841 12.7834C13.4091 12.1584 13.7674 11.3667 13.8841 10.5583" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path _ngcontent-ng-c2659116941="" d="M6.1172 9.4417C6.2339 8.625 6.5922 7.8417 7.2172 7.2167C8.7505 5.6833 11.2422 5.6833 12.7839 7.2167C13.0005 7.4333 13.1755 7.6667 13.3255 7.9083" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path _ngcontent-ng-c2659116941="" d="M6.5176 14.3165V12.0916H8.7426" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path _ngcontent-ng-c2659116941="" d="M13.4848 5.6831V7.9081H11.2598" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></app-refresh-icon>Refresh charts <!--bindings={
  "ng-reflect-ng-if": null
}--></button></app-button></div><div _ngcontent-ng-c3567730054="" class="component-code"><pre _ngcontent-ng-c3567730054=""><code _ngcontent-ng-c3567730054="">&lt;app-button variant="secondary" [figmaExact]="true"&gt;Refresh charts&lt;/app-button&gt;</code></pre></div></div></div></section><section _ngcontent-ng-c3567730054="" class="component-section"><h2 _ngcontent-ng-c3567730054="" class="section-title">Form Controls</h2><div _ngcontent-ng-c3567730054="" class="component-row"><div _ngcontent-ng-c3567730054="" class="component-item"><h3 _ngcontent-ng-c3567730054="" class="component-title">Checkbox</h3><div _ngcontent-ng-c3567730054="" class="component-subtitle">Style Guide: "Buttons, selectors, and icons" | Implementation: "CheckboxComponent"</div><div _ngcontent-ng-c3567730054="" class="component-demo figma-sized checkbox-demo"><app-checkbox _ngcontent-ng-c3567730054="" label="Checkbox" _nghost-ng-c2693716353="" ng-reflect-label="Checkbox" ng-reflect-model="false" class="ng-untouched ng-pristine ng-valid" ngh="3"><div _ngcontent-ng-c2693716353="" class="checkbox-container"><input _ngcontent-ng-c2693716353="" type="checkbox" class="checkbox" id="checkbox-35cjoj4" name="" jsaction="change:;blur:;"><label _ngcontent-ng-c2693716353="" class="checkbox-label" for="checkbox-35cjoj4"> Checkbox </label><!--bindings={
  "ng-reflect-ng-if": "Checkbox"
}--></div></app-checkbox></div><div _ngcontent-ng-c3567730054="" class="component-code"><pre _ngcontent-ng-c3567730054=""><code _ngcontent-ng-c3567730054="">&lt;app-checkbox label="Checkbox"&gt;&lt;/app-checkbox&gt;</code></pre></div></div></div><h3 _ngcontent-ng-c3567730054="" class="subsection-title">Advanced Form Controls</h3><div _ngcontent-ng-c3567730054="" class="component-row"><div _ngcontent-ng-c3567730054="" class="component-item"><h3 _ngcontent-ng-c3567730054="" class="component-title">Dropdown (Single Select)</h3><div _ngcontent-ng-c3567730054="" class="component-demo figma-sized form-demo"><app-dropdown _ngcontent-ng-c3567730054="" label="Reasoning" placeholder="Select reasoning" _nghost-ng-c81275535="" ng-reflect-label="Reasoning" ng-reflect-placeholder="Select reasoning" ng-reflect-options="[object Object],[object Object" class="ng-untouched ng-pristine ng-valid" ngh="4" jsaction="keydown:;"><div _ngcontent-ng-c81275535="" class="dropdown-container"><label _ngcontent-ng-c81275535="" class="dropdown-label" for="dropdown-p36e17x"> Reasoning <!--bindings={
  "ng-reflect-ng-if": "false"
}--></label><!--bindings={
  "ng-reflect-ng-if": "Reasoning"
}--><div _ngcontent-ng-c81275535="" class="dropdown-trigger" tabindex="0" aria-expanded="false" aria-haspopup="true" role="combobox" aria-labelledby="dropdown-p36e17x-label" jsaction="click:;keydown:;"><div _ngcontent-ng-c81275535="" class="dropdown-content"><span _ngcontent-ng-c81275535="" class="dropdown-text placeholder"> Select reasoning </span><span _ngcontent-ng-c81275535="" class="dropdown-icon"><svg _ngcontent-ng-c81275535="" width="14" height="8" viewBox="0 0 14 8" fill="none" xmlns="http://www.w3.org/2000/svg"><path _ngcontent-ng-c81275535="" d="M1 1L7 7L13 1" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></span></div></div><!--bindings={
  "ng-reflect-ng-if": "false"
}--><!--bindings={
  "ng-reflect-ng-if": ""
}--></div></app-dropdown></div><div _ngcontent-ng-c3567730054="" class="component-code"><pre _ngcontent-ng-c3567730054=""><code _ngcontent-ng-c3567730054="">&lt;app-dropdown label="Reasoning" [options]="options"&gt;&lt;/app-dropdown&gt;</code></pre></div></div><div _ngcontent-ng-c3567730054="" class="component-item"><h3 _ngcontent-ng-c3567730054="" class="component-title">Dropdown (Multi Select)</h3><div _ngcontent-ng-c3567730054="" class="component-demo figma-sized form-demo"><app-dropdown _ngcontent-ng-c3567730054="" label="Multiple Reasoning" placeholder="Select multiple" _nghost-ng-c81275535="" ng-reflect-label="Multiple Reasoning" ng-reflect-placeholder="Select multiple" ng-reflect-options="[object Object],[object Object" ng-reflect-multi-select="true" ng-reflect-model="" class="ng-untouched ng-pristine ng-valid" ngh="4" jsaction="keydown:;"><div _ngcontent-ng-c81275535="" class="dropdown-container"><label _ngcontent-ng-c81275535="" class="dropdown-label" for="dropdown-uotin8a"> Multiple Reasoning <!--bindings={
  "ng-reflect-ng-if": "false"
}--></label><!--bindings={
  "ng-reflect-ng-if": "Multiple Reasoning"
}--><div _ngcontent-ng-c81275535="" class="dropdown-trigger" tabindex="0" aria-expanded="false" aria-haspopup="true" role="combobox" aria-labelledby="dropdown-uotin8a-label" jsaction="click:;keydown:;"><div _ngcontent-ng-c81275535="" class="dropdown-content"><span _ngcontent-ng-c81275535="" class="dropdown-text placeholder"> Select multiple </span><span _ngcontent-ng-c81275535="" class="dropdown-icon"><svg _ngcontent-ng-c81275535="" width="14" height="8" viewBox="0 0 14 8" fill="none" xmlns="http://www.w3.org/2000/svg"><path _ngcontent-ng-c81275535="" d="M1 1L7 7L13 1" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></span></div></div><!--bindings={
  "ng-reflect-ng-if": "false"
}--><!--bindings={
  "ng-reflect-ng-if": ""
}--></div></app-dropdown></div><div _ngcontent-ng-c3567730054="" class="component-code"><pre _ngcontent-ng-c3567730054=""><code _ngcontent-ng-c3567730054="">&lt;app-dropdown [multiSelect]="true" [options]="options"&gt;&lt;/app-dropdown&gt;</code></pre></div></div></div><div _ngcontent-ng-c3567730054="" class="component-row"><div _ngcontent-ng-c3567730054="" class="component-item"><h3 _ngcontent-ng-c3567730054="" class="component-title">Calendar</h3><div _ngcontent-ng-c3567730054="" class="component-demo figma-sized form-demo"><app-calendar _ngcontent-ng-c3567730054="" label="Date of Service" placeholder="Date of Service" _nghost-ng-c1227210501="" ng-reflect-label="Date of Service" ng-reflect-placeholder="Date of Service" ng-reflect-model="" class="ng-untouched ng-pristine ng-valid" ngh="5"><div _ngcontent-ng-c1227210501="" class="calendar-container"><label _ngcontent-ng-c1227210501="" class="calendar-label" for="calendar-ov4z0nb"> Date of Service <!--bindings={
  "ng-reflect-ng-if": "false"
}--></label><!--bindings={
  "ng-reflect-ng-if": "Date of Service"
}--><div _ngcontent-ng-c1227210501="" class="date-input-container"><div _ngcontent-ng-c1227210501="" class="date-input-content"><input _ngcontent-ng-c1227210501="" type="text" class="date-input" id="calendar-ov4z0nb" name="" placeholder="Date of Service" value="" min="" max="" aria-expanded="false" aria-haspopup="true" role="combobox" jsaction="input:;focus:;blur:;"><button _ngcontent-ng-c1227210501="" type="button" class="calendar-icon-button" aria-label="Open calendar" jsaction="click:;"><svg _ngcontent-ng-c1227210501="" width="16" height="18" viewBox="0 0 16 18" fill="none" xmlns="http://www.w3.org/2000/svg" class="calendar-icon"><rect _ngcontent-ng-c1227210501="" x="0.75" y="2.75" width="14.5" height="14.5" rx="1.25" stroke="currentColor" stroke-width="1.5"></rect><path _ngcontent-ng-c1227210501="" d="M4 1V4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path><path _ngcontent-ng-c1227210501="" d="M12 1V4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path><path _ngcontent-ng-c1227210501="" d="M1 7H15" stroke="currentColor" stroke-width="1.5"></path></svg></button></div></div><!--bindings={
  "ng-reflect-ng-if": "false"
}--><!--bindings={
  "ng-reflect-ng-if": ""
}--></div></app-calendar></div><div _ngcontent-ng-c3567730054="" class="component-code"><pre _ngcontent-ng-c3567730054=""><code _ngcontent-ng-c3567730054="">&lt;app-calendar label="Date of Service"&gt;&lt;/app-calendar&gt;</code></pre></div></div><div _ngcontent-ng-c3567730054="" class="component-item"><h3 _ngcontent-ng-c3567730054="" class="component-title">Calendar (Required)</h3><div _ngcontent-ng-c3567730054="" class="component-demo figma-sized form-demo"><app-calendar _ngcontent-ng-c3567730054="" label="Required Date" placeholder="Select date" _nghost-ng-c1227210501="" ng-reflect-label="Required Date" ng-reflect-placeholder="Select date" ng-reflect-required="true" ng-reflect-model="" class="ng-untouched ng-pristine ng-invalid" required="" ngh="6"><div _ngcontent-ng-c1227210501="" class="calendar-container"><label _ngcontent-ng-c1227210501="" class="calendar-label" for="calendar-pxkadpk"> Required Date <span _ngcontent-ng-c1227210501="" class="required-indicator">*</span><!--bindings={
  "ng-reflect-ng-if": "true"
}--></label><!--bindings={
  "ng-reflect-ng-if": "Required Date"
}--><div _ngcontent-ng-c1227210501="" class="date-input-container"><div _ngcontent-ng-c1227210501="" class="date-input-content"><input _ngcontent-ng-c1227210501="" type="text" class="date-input" id="calendar-pxkadpk" name="" placeholder="Select date" value="" required="" min="" max="" aria-expanded="false" aria-haspopup="true" role="combobox" jsaction="input:;focus:;blur:;"><button _ngcontent-ng-c1227210501="" type="button" class="calendar-icon-button" aria-label="Open calendar" jsaction="click:;"><svg _ngcontent-ng-c1227210501="" width="16" height="18" viewBox="0 0 16 18" fill="none" xmlns="http://www.w3.org/2000/svg" class="calendar-icon"><rect _ngcontent-ng-c1227210501="" x="0.75" y="2.75" width="14.5" height="14.5" rx="1.25" stroke="currentColor" stroke-width="1.5"></rect><path _ngcontent-ng-c1227210501="" d="M4 1V4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path><path _ngcontent-ng-c1227210501="" d="M12 1V4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path><path _ngcontent-ng-c1227210501="" d="M1 7H15" stroke="currentColor" stroke-width="1.5"></path></svg></button></div></div><!--bindings={
  "ng-reflect-ng-if": "false"
}--><!--bindings={
  "ng-reflect-ng-if": ""
}--></div></app-calendar></div><div _ngcontent-ng-c3567730054="" class="component-code"><pre _ngcontent-ng-c3567730054=""><code _ngcontent-ng-c3567730054="">&lt;app-calendar [required]="true"&gt;&lt;/app-calendar&gt;</code></pre></div></div></div><div _ngcontent-ng-c3567730054="" class="component-row"><div _ngcontent-ng-c3567730054="" class="component-item"><h3 _ngcontent-ng-c3567730054="" class="component-title">Results</h3><div _ngcontent-ng-c3567730054="" class="component-demo figma-sized results-demo"><app-results _ngcontent-ng-c3567730054="" title="Results" _nghost-ng-c2018085561="" ng-reflect-title="Results" ng-reflect-model="[object Object]" class="ng-untouched ng-pristine ng-valid" ngh="9"><div _ngcontent-ng-c2018085561="" class="results-container"><div _ngcontent-ng-c2018085561="" class="results-header"><div _ngcontent-ng-c2018085561="" class="results-title-section"><h1 _ngcontent-ng-c2018085561="" class="results-title">Results</h1><div _ngcontent-ng-c2018085561="" role="tablist" class="tab-navigation"><button _ngcontent-ng-c2018085561="" type="button" class="tab-button active" role="tab" aria-selected="true" aria-controls="results-5ug9ndn-inclusions-panel" id="results-5ug9ndn-inclusions-tab" jsaction="click:;"> Inclusions </button><button _ngcontent-ng-c2018085561="" type="button" class="tab-button" role="tab" aria-selected="false" aria-controls="results-5ug9ndn-exclusions-panel" id="results-5ug9ndn-exclusions-tab" jsaction="click:;"> Exclusions </button><button _ngcontent-ng-c2018085561="" type="button" class="tab-button" role="tab" aria-selected="false" aria-controls="results-5ug9ndn-none-found-panel" id="results-5ug9ndn-none-found-tab" jsaction="click:;"> None found </button><!--bindings={
  "ng-reflect-ng-for-of": "[object Object],[object Object"
}--></div></div></div><div _ngcontent-ng-c2018085561="" class="tab-content"><app-inclusions-tab _ngcontent-ng-c2018085561="" _nghost-ng-c1722032080="" ng-reflect-form-group="[object Object]" ng-reflect-form="[object Object]" ng-reflect-disabled="false" ng-reflect-id="results-5ug9ndn-inclusions" class="ng-untouched ng-pristine ng-valid" ngh="2" jsaction="submit:;"><div _ngcontent-ng-c1722032080="" class="inclusions-tab-content ng-untouched ng-pristine ng-valid" ng-reflect-form="[object Object]" jsaction="submit:;"><div _ngcontent-ng-c1722032080="" class="frame-942"><app-checkbox _ngcontent-ng-c1722032080="" label="Telehealth" formcontrolname="telehealth" _nghost-ng-c2693716353="" ng-reflect-label="Telehealth" ng-reflect-name="telehealth" ng-reflect-disabled="false" ng-reflect-is-disabled="false" class="ng-untouched ng-pristine ng-valid" ngh="3" jsaction="change:;"><div _ngcontent-ng-c2693716353="" class="checkbox-container"><input _ngcontent-ng-c2693716353="" type="checkbox" class="checkbox" id="checkbox-ya1a42i" name="" jsaction="change:;blur:;"><label _ngcontent-ng-c2693716353="" class="checkbox-label" for="checkbox-ya1a42i"> Telehealth </label><!--bindings={
  "ng-reflect-ng-if": "Telehealth"
}--></div></app-checkbox></div><div _ngcontent-ng-c1722032080="" class="frame-936"><div _ngcontent-ng-c1722032080="" class="frame-939"><div _ngcontent-ng-c1722032080="" class="frame-940"><span _ngcontent-ng-c1722032080="" class="text-sys">Sys</span><input _ngcontent-ng-c1722032080="" type="text" placeholder="Value" formcontrolname="sys" class="dropdown-inclusion ng-untouched ng-pristine ng-valid" ng-reflect-name="sys" ng-reflect-is-disabled="false" value="" jsaction="input:;blur:;compositionstart:;compositionend:;"></div><div _ngcontent-ng-c1722032080="" class="frame-941"><span _ngcontent-ng-c1722032080="" class="text-dias">Dias</span><input _ngcontent-ng-c1722032080="" type="text" placeholder="Value" formcontrolname="dias" class="dropdown-inclusion ng-untouched ng-pristine ng-valid" ng-reflect-name="dias" ng-reflect-is-disabled="false" value="" jsaction="input:;blur:;compositionstart:;compositionend:;"></div><div _ngcontent-ng-c1722032080="" class="frame-937"><span _ngcontent-ng-c1722032080="" class="text-date-of-service">Date of Service</span><app-calendar _ngcontent-ng-c1722032080="" placeholder="MM/DD/YY" formcontrolname="dateOfService" class="calendar ng-untouched ng-pristine ng-valid" _nghost-ng-c1227210501="" ng-reflect-placeholder="MM/DD/YY" ng-reflect-name="dateOfService" ng-reflect-disabled="false" ng-reflect-is-disabled="false" ngh="7"><div _ngcontent-ng-c1227210501="" class="calendar-container"><!--bindings={
  "ng-reflect-ng-if": ""
}--><div _ngcontent-ng-c1227210501="" class="date-input-container"><div _ngcontent-ng-c1227210501="" class="date-input-content"><input _ngcontent-ng-c1227210501="" type="text" class="date-input" id="calendar-si8xqqg" name="" placeholder="MM/DD/YY" value="" min="" max="" aria-expanded="false" aria-haspopup="true" role="combobox" jsaction="input:;focus:;blur:;"><button _ngcontent-ng-c1227210501="" type="button" class="calendar-icon-button" aria-label="Open calendar" jsaction="click:;"><svg _ngcontent-ng-c1227210501="" width="16" height="18" viewBox="0 0 16 18" fill="none" xmlns="http://www.w3.org/2000/svg" class="calendar-icon"><rect _ngcontent-ng-c1227210501="" x="0.75" y="2.75" width="14.5" height="14.5" rx="1.25" stroke="currentColor" stroke-width="1.5"></rect><path _ngcontent-ng-c1227210501="" d="M4 1V4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path><path _ngcontent-ng-c1227210501="" d="M12 1V4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path><path _ngcontent-ng-c1227210501="" d="M1 7H15" stroke="currentColor" stroke-width="1.5"></path></svg></button></div></div><!--bindings={
  "ng-reflect-ng-if": "false"
}--><!--bindings={
  "ng-reflect-ng-if": ""
}--></div></app-calendar></div></div></div><div _ngcontent-ng-c1722032080="" class="frame-938"><span _ngcontent-ng-c1722032080="" class="text-notes">Notes</span><app-notes _ngcontent-ng-c1722032080="" label="" placeholder="Notes" formcontrolname="notes" class="notes ng-untouched ng-pristine ng-valid" _nghost-ng-c3668244963="" ng-reflect-label="" ng-reflect-placeholder="Notes" ng-reflect-name="notes" ng-reflect-disabled="false" ng-reflect-is-disabled="false" ngh="8"><div _ngcontent-ng-c3668244963="" class="notes-container"><!--bindings={
  "ng-reflect-ng-if": ""
}--><div _ngcontent-ng-c3668244963="" class="notes-input-wrapper"><textarea _ngcontent-ng-c3668244963="" class="notes-textarea" id="notes-htdq6j0" name="" placeholder="Notes" maxlength="200" aria-describedby="notes-htdq6j0-char-count" jsaction="input:;focus:;blur:;"></textarea><div _ngcontent-ng-c3668244963="" class="character-counter" id="notes-htdq6j0-char-count"> 0/200 </div><!--bindings={
  "ng-reflect-ng-if": "true"
}--></div><!--bindings={
  "ng-reflect-ng-if": ""
}--></div></app-notes></div></div></app-inclusions-tab><!--bindings={
  "ng-reflect-ng-if": "true"
}--><!--bindings={
  "ng-reflect-ng-if": "false"
}--><!--bindings={
  "ng-reflect-ng-if": "false"
}--></div><!--bindings={
  "ng-reflect-ng-if": ""
}--></div></app-results></div><div _ngcontent-ng-c3567730054="" class="component-code"><pre _ngcontent-ng-c3567730054=""><code _ngcontent-ng-c3567730054="">&lt;app-results title="Results"&gt;&lt;/app-results&gt;</code></pre></div></div><div _ngcontent-ng-c3567730054="" class="component-item"><h3 _ngcontent-ng-c3567730054="" class="component-title">Results (Exclusions Tab)</h3><div _ngcontent-ng-c3567730054="" class="component-demo figma-sized results-demo"><app-results _ngcontent-ng-c3567730054="" title="Findings" _nghost-ng-c2018085561="" ng-reflect-title="Findings" ng-reflect-model="[object Object]" class="ng-untouched ng-pristine ng-valid" ngh="11"><div _ngcontent-ng-c2018085561="" class="results-container"><div _ngcontent-ng-c2018085561="" class="results-header"><div _ngcontent-ng-c2018085561="" class="results-title-section"><h1 _ngcontent-ng-c2018085561="" class="results-title">Findings</h1><div _ngcontent-ng-c2018085561="" role="tablist" class="tab-navigation"><button _ngcontent-ng-c2018085561="" type="button" class="tab-button" role="tab" aria-selected="false" aria-controls="results-2egvhsx-inclusions-panel" id="results-2egvhsx-inclusions-tab" jsaction="click:;"> Inclusions </button><button _ngcontent-ng-c2018085561="" type="button" class="tab-button active" role="tab" aria-selected="true" aria-controls="results-2egvhsx-exclusions-panel" id="results-2egvhsx-exclusions-tab" jsaction="click:;"> Exclusions </button><button _ngcontent-ng-c2018085561="" type="button" class="tab-button" role="tab" aria-selected="false" aria-controls="results-2egvhsx-none-found-panel" id="results-2egvhsx-none-found-tab" jsaction="click:;"> None found </button><!--bindings={
  "ng-reflect-ng-for-of": "[object Object],[object Object"
}--></div></div></div><div _ngcontent-ng-c2018085561="" class="tab-content"><!--bindings={
  "ng-reflect-ng-if": "false"
}--><app-exclusions-tab _ngcontent-ng-c2018085561="" _nghost-ng-c2467306289="" ng-reflect-form-group="[object Object]" ng-reflect-form="[object Object]" ng-reflect-disabled="false" ng-reflect-id="results-2egvhsx-exclusions" class="ng-untouched ng-pristine ng-valid" ngh="2" jsaction="submit:;"><div _ngcontent-ng-c2467306289="" class="exclusions-tab-content ng-untouched ng-pristine ng-valid" ng-reflect-form="[object Object]" jsaction="submit:;"><div _ngcontent-ng-c2467306289="" class="frame-916"><div _ngcontent-ng-c2467306289="" class="frame-939"><span _ngcontent-ng-c2467306289="" class="text-reasoning">Reasoning</span><app-dropdown _ngcontent-ng-c2467306289="" placeholder="Select" formcontrolname="reasoning" class="dropdown-exclusion ng-untouched ng-pristine ng-valid" _nghost-ng-c81275535="" ng-reflect-placeholder="Select" ng-reflect-name="reasoning" ng-reflect-options="[object Object],[object Object" ng-reflect-multi-select="true" ng-reflect-disabled="false" ng-reflect-is-disabled="false" ngh="10" jsaction="keydown:;"><div _ngcontent-ng-c81275535="" class="dropdown-container"><!--bindings={
  "ng-reflect-ng-if": ""
}--><div _ngcontent-ng-c81275535="" class="dropdown-trigger" tabindex="0" aria-expanded="false" aria-haspopup="true" role="combobox" jsaction="click:;keydown:;"><div _ngcontent-ng-c81275535="" class="dropdown-content"><span _ngcontent-ng-c81275535="" class="dropdown-text placeholder"> Select </span><span _ngcontent-ng-c81275535="" class="dropdown-icon"><svg _ngcontent-ng-c81275535="" width="14" height="8" viewBox="0 0 14 8" fill="none" xmlns="http://www.w3.org/2000/svg"><path _ngcontent-ng-c81275535="" d="M1 1L7 7L13 1" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></span></div></div><!--bindings={
  "ng-reflect-ng-if": "false"
}--><!--bindings={
  "ng-reflect-ng-if": ""
}--></div></app-dropdown></div><div _ngcontent-ng-c2467306289="" class="frame-940"><span _ngcontent-ng-c2467306289="" class="text-date-of-service">Date of Service</span><app-calendar _ngcontent-ng-c2467306289="" placeholder="MM/DD/YY" formcontrolname="dateOfService" class="calendar ng-untouched ng-pristine ng-valid" _nghost-ng-c1227210501="" ng-reflect-placeholder="MM/DD/YY" ng-reflect-name="dateOfService" ng-reflect-disabled="false" ng-reflect-is-disabled="false" ngh="7"><div _ngcontent-ng-c1227210501="" class="calendar-container"><!--bindings={
  "ng-reflect-ng-if": ""
}--><div _ngcontent-ng-c1227210501="" class="date-input-container"><div _ngcontent-ng-c1227210501="" class="date-input-content"><input _ngcontent-ng-c1227210501="" type="text" class="date-input" id="calendar-qayqhm8" name="" placeholder="MM/DD/YY" value="" min="" max="" aria-expanded="false" aria-haspopup="true" role="combobox" jsaction="input:;focus:;blur:;"><button _ngcontent-ng-c1227210501="" type="button" class="calendar-icon-button" aria-label="Open calendar" jsaction="click:;"><svg _ngcontent-ng-c1227210501="" width="16" height="18" viewBox="0 0 16 18" fill="none" xmlns="http://www.w3.org/2000/svg" class="calendar-icon"><rect _ngcontent-ng-c1227210501="" x="0.75" y="2.75" width="14.5" height="14.5" rx="1.25" stroke="currentColor" stroke-width="1.5"></rect><path _ngcontent-ng-c1227210501="" d="M4 1V4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path><path _ngcontent-ng-c1227210501="" d="M12 1V4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path><path _ngcontent-ng-c1227210501="" d="M1 7H15" stroke="currentColor" stroke-width="1.5"></path></svg></button></div></div><!--bindings={
  "ng-reflect-ng-if": "false"
}--><!--bindings={
  "ng-reflect-ng-if": ""
}--></div></app-calendar></div><div _ngcontent-ng-c2467306289="" class="frame-941"><span _ngcontent-ng-c2467306289="" class="text-notes">Notes</span><app-notes _ngcontent-ng-c2467306289="" label="" placeholder="Notes" formcontrolname="notes" class="notes ng-untouched ng-pristine ng-valid" _nghost-ng-c3668244963="" ng-reflect-label="" ng-reflect-placeholder="Notes" ng-reflect-name="notes" ng-reflect-disabled="false" ng-reflect-is-disabled="false" ngh="8"><div _ngcontent-ng-c3668244963="" class="notes-container"><!--bindings={
  "ng-reflect-ng-if": ""
}--><div _ngcontent-ng-c3668244963="" class="notes-input-wrapper"><textarea _ngcontent-ng-c3668244963="" class="notes-textarea has-value" id="notes-la8t3i8" name="" placeholder="Notes" maxlength="200" aria-describedby="notes-la8t3i8-char-count" jsaction="input:;focus:;blur:;">Sample exclusion note</textarea><div _ngcontent-ng-c3668244963="" class="character-counter" id="notes-la8t3i8-char-count"> 21/200 </div><!--bindings={
  "ng-reflect-ng-if": "true"
}--></div><!--bindings={
  "ng-reflect-ng-if": ""
}--></div></app-notes></div></div></div></app-exclusions-tab><!--bindings={
  "ng-reflect-ng-if": "true"
}--><!--bindings={
  "ng-reflect-ng-if": "false"
}--></div><!--bindings={
  "ng-reflect-ng-if": ""
}--></div></app-results></div><div _ngcontent-ng-c3567730054="" class="component-code"><pre _ngcontent-ng-c3567730054=""><code _ngcontent-ng-c3567730054="">&lt;app-results title="Findings" [ngModel]="exclusionsData"&gt;&lt;/app-results&gt;</code></pre></div></div></div></section><section _ngcontent-ng-c3567730054="" class="component-section"><h2 _ngcontent-ng-c3567730054="" class="section-title">Tables</h2><div _ngcontent-ng-c3567730054="" class="component-row full-width"><div _ngcontent-ng-c3567730054="" class="component-item full-width"><h3 _ngcontent-ng-c3567730054="" class="component-title">Assigned Table (Dashboard Table)</h3><div _ngcontent-ng-c3567730054="" class="component-demo table-demo"><app-assigned-table _ngcontent-ng-c3567730054="" _nghost-ng-c2702405285="" ng-reflect-charts="[object Object],[object Object" ngh="12"><div _ngcontent-ng-c2702405285="" class="assigned-table_1134-1191"><div _ngcontent-ng-c2702405285="" class="table_1134-1192"><div _ngcontent-ng-c2702405285="" class="table_1134-1208"><div _ngcontent-ng-c2702405285="" class="columns_1134-1209"><div _ngcontent-ng-c2702405285="" class="column_1134-1210"><div _ngcontent-ng-c2702405285="" class="header-item_1134-1211"><div _ngcontent-ng-c2702405285="" class="table-item_1134-1212"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1214">Member ID</span></div></div><div _ngcontent-ng-c2702405285="" class="table-item_1134-1215"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1216">55820474</span></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1134-1215"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1216">302274401</span></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1134-1215"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1216">7729471914</span></div><!--ng-container--><!--bindings={
  "ng-reflect-ng-for-of": "[object Object],[object Object"
}--></div><div _ngcontent-ng-c2702405285="" class="column_1235-930"><div _ngcontent-ng-c2702405285="" class="header-item_1235-931"><div _ngcontent-ng-c2702405285="" class="table-item_1235-932"><span _ngcontent-ng-c2702405285="" class="text-label_1235-934">First name</span></div></div><div _ngcontent-ng-c2702405285="" class="table-item_1235-939"><div _ngcontent-ng-c2702405285="" class="icon-text_1235-940"><span _ngcontent-ng-c2702405285="" class="text-label_1235-941">John</span></div></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1235-939"><div _ngcontent-ng-c2702405285="" class="icon-text_1235-940"><span _ngcontent-ng-c2702405285="" class="text-label_1235-941">Alma</span></div></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1235-939"><div _ngcontent-ng-c2702405285="" class="icon-text_1235-940"><span _ngcontent-ng-c2702405285="" class="text-label_1235-941">Joanne</span></div></div><!--ng-container--><!--bindings={
  "ng-reflect-ng-for-of": "[object Object],[object Object"
}--></div><div _ngcontent-ng-c2702405285="" class="column_1134-1235"><div _ngcontent-ng-c2702405285="" class="header-item_1134-1236"><div _ngcontent-ng-c2702405285="" class="table-item_1134-1237"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1239">Last name</span></div></div><div _ngcontent-ng-c2702405285="" class="table-item_1134-1240"><div _ngcontent-ng-c2702405285="" class="icon-text_1134-1241"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1242">Dey</span></div></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1134-1240"><div _ngcontent-ng-c2702405285="" class="icon-text_1134-1241"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1242">Anders</span></div></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1134-1240"><div _ngcontent-ng-c2702405285="" class="icon-text_1134-1241"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1242">Smith</span></div></div><!--ng-container--><!--bindings={
  "ng-reflect-ng-for-of": "[object Object],[object Object"
}--></div><div _ngcontent-ng-c2702405285="" class="column_1235-969"><div _ngcontent-ng-c2702405285="" class="header-item_1235-970"><div _ngcontent-ng-c2702405285="" class="table-item_1235-971"><span _ngcontent-ng-c2702405285="" class="text-label_1235-973">Middle name</span></div></div><div _ngcontent-ng-c2702405285="" class="table-item_1235-974"><div _ngcontent-ng-c2702405285="" class="icon-text_1235-975"><!--bindings={
  "ng-reflect-ng-if": ""
}--></div></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1235-974"><div _ngcontent-ng-c2702405285="" class="icon-text_1235-975"><span _ngcontent-ng-c2702405285="" class="text-label_1235-979">G</span><!--bindings={
  "ng-reflect-ng-if": "G"
}--></div></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1235-974"><div _ngcontent-ng-c2702405285="" class="icon-text_1235-975"><!--bindings={
  "ng-reflect-ng-if": ""
}--></div></div><!--ng-container--><!--bindings={
  "ng-reflect-ng-for-of": "[object Object],[object Object"
}--></div><div _ngcontent-ng-c2702405285="" class="column_1134-1270"><div _ngcontent-ng-c2702405285="" class="header-item_1134-1271"><div _ngcontent-ng-c2702405285="" class="table-item_1134-1272"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1274">DOB</span></div></div><div _ngcontent-ng-c2702405285="" class="table-item_1134-1275"><div _ngcontent-ng-c2702405285="" class="icon-text_1134-1276"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1277">01/05/1972</span></div></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1134-1275"><div _ngcontent-ng-c2702405285="" class="icon-text_1134-1276"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1277">12/15/1953</span></div></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1134-1275"><div _ngcontent-ng-c2702405285="" class="icon-text_1134-1276"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1277">06/30/1951</span></div></div><!--ng-container--><!--bindings={
  "ng-reflect-ng-for-of": "[object Object],[object Object"
}--></div><div _ngcontent-ng-c2702405285="" class="column_1134-1305"><div _ngcontent-ng-c2702405285="" class="header-item_1134-1306"><div _ngcontent-ng-c2702405285="" class="table-item_1134-1307"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1309">LOB</span></div></div><div _ngcontent-ng-c2702405285="" class="table-item_1134-1310"><div _ngcontent-ng-c2702405285="" class="icon-text_1134-1311"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1312">MA HMO</span></div></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1134-1310"><div _ngcontent-ng-c2702405285="" class="icon-text_1134-1311"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1312">MA HMO</span></div></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1134-1310"><div _ngcontent-ng-c2702405285="" class="icon-text_1134-1311"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1312">MA HMO</span></div></div><!--ng-container--><!--bindings={
  "ng-reflect-ng-for-of": "[object Object],[object Object"
}--></div><div _ngcontent-ng-c2702405285="" class="column_1134-1340"><div _ngcontent-ng-c2702405285="" class="header-item_1134-1341"><div _ngcontent-ng-c2702405285="" class="table-item_1134-1342"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1344">Measure</span></div></div><div _ngcontent-ng-c2702405285="" class="table-item_1134-1345"><div _ngcontent-ng-c2702405285="" class="icon-text_1134-1346"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1347">CBP</span></div></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1134-1345"><div _ngcontent-ng-c2702405285="" class="icon-text_1134-1346"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1347">CBP</span></div></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1134-1345"><div _ngcontent-ng-c2702405285="" class="icon-text_1134-1346"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1347">CBP</span></div></div><!--ng-container--><!--bindings={
  "ng-reflect-ng-for-of": "[object Object],[object Object"
}--></div><div _ngcontent-ng-c2702405285="" class="column_1134-1375"><div _ngcontent-ng-c2702405285="" class="header-item_1134-1376"><div _ngcontent-ng-c2702405285="" class="table-item_1134-1377"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1379">Review 1</span></div></div><div _ngcontent-ng-c2702405285="" class="table-item_1134-1380"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1381">Jane Chu</span></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1134-1380"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1381">Jane Chu</span></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1134-1380"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1381">Jane Chu</span></div><!--ng-container--><!--bindings={
  "ng-reflect-ng-for-of": "[object Object],[object Object"
}--></div><div _ngcontent-ng-c2702405285="" class="column_1134-1400"><div _ngcontent-ng-c2702405285="" class="header-item_1134-1401"><div _ngcontent-ng-c2702405285="" class="table-item_1134-1402"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1404">Review 2</span></div></div><div _ngcontent-ng-c2702405285="" class="table-item_1134-1405"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1406">-</span></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1134-1405"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1406">-</span></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1134-1405"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1406">-</span></div><!--ng-container--><!--bindings={
  "ng-reflect-ng-for-of": "[object Object],[object Object"
}--></div><div _ngcontent-ng-c2702405285="" class="column_1134-1425"><div _ngcontent-ng-c2702405285="" class="header-item_1134-1426"><div _ngcontent-ng-c2702405285="" class="table-item_1134-1427"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1429">Assigned</span></div></div><div _ngcontent-ng-c2702405285="" class="table-item_1134-1430"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1431">04/15/25 1:30pm</span></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1134-1430"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1431">04/15/25 1:30pm</span></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1134-1430"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1431">04/15/25 1:30pm</span></div><!--ng-container--><!--bindings={
  "ng-reflect-ng-for-of": "[object Object],[object Object"
}--></div><div _ngcontent-ng-c2702405285="" class="column_1134-1450"><div _ngcontent-ng-c2702405285="" class="header-item_1134-1451"><div _ngcontent-ng-c2702405285="" class="table-item_1134-1452"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1454">Status</span></div></div><div _ngcontent-ng-c2702405285="" class="table-item_1134-1455"><app-sumbit-button _ngcontent-ng-c2702405285="" class="sumbit-button_1134-1462" _nghost-ng-c2891085390="" ng-reflect-property1="Default" ngh="2" jsaction="click:;"><button _ngcontent-ng-c2891085390="" class="submit-button-default" jsaction="click:;"> Review </button></app-sumbit-button></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1134-1455"><app-sumbit-button _ngcontent-ng-c2702405285="" class="sumbit-button_1134-1462" _nghost-ng-c2891085390="" ng-reflect-property1="Inactive" ngh="2" jsaction="click:;"><button _ngcontent-ng-c2891085390="" class="submit-button-inactive" disabled="" jsaction="click:;"> Inactive </button></app-sumbit-button></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1134-1455"><app-sumbit-button _ngcontent-ng-c2702405285="" class="sumbit-button_1134-1462" _nghost-ng-c2891085390="" ng-reflect-property1="Inactive" ngh="2" jsaction="click:;"><button _ngcontent-ng-c2891085390="" class="submit-button-inactive" disabled="" jsaction="click:;"> Inactive </button></app-sumbit-button></div><!--ng-container--><!--bindings={
  "ng-reflect-ng-for-of": "[object Object],[object Object"
}--></div></div></div><!--bindings={
  "ng-reflect-ng-if": "false"
}--></div></div></app-assigned-table></div><div _ngcontent-ng-c3567730054="" class="component-code"><pre _ngcontent-ng-c3567730054=""><code _ngcontent-ng-c3567730054="">&lt;app-assigned-table [charts]="charts"&gt;&lt;/app-assigned-table&gt;</code></pre></div></div></div></section><section _ngcontent-ng-c3567730054="" class="component-section"><h2 _ngcontent-ng-c3567730054="" class="section-title">Phase 2 Components</h2><div _ngcontent-ng-c3567730054="" class="component-row"><div _ngcontent-ng-c3567730054="" class="component-item"><h3 _ngcontent-ng-c3567730054="" class="component-title">Notes</h3><div _ngcontent-ng-c3567730054="" class="component-subtitle">Style Guide: "Notes" | Implementation: "NotesComponent"</div><div _ngcontent-ng-c3567730054="" class="component-demo figma-sized form-demo"><app-notes _ngcontent-ng-c3567730054="" label="Notes" placeholder="Notes" _nghost-ng-c3668244963="" ng-reflect-label="Notes" ng-reflect-placeholder="Notes" ng-reflect-model="" class="ng-untouched ng-pristine ng-valid" ngh="13"><div _ngcontent-ng-c3668244963="" class="notes-container"><label _ngcontent-ng-c3668244963="" class="notes-label" for="notes-i63j245"> Notes <!--bindings={
  "ng-reflect-ng-if": "false"
}--></label><!--bindings={
  "ng-reflect-ng-if": "Notes"
}--><div _ngcontent-ng-c3668244963="" class="notes-input-wrapper"><textarea _ngcontent-ng-c3668244963="" class="notes-textarea" id="notes-i63j245" name="" placeholder="Notes" maxlength="200" aria-describedby="notes-i63j245-char-count" jsaction="input:;focus:;blur:;"></textarea><div _ngcontent-ng-c3668244963="" class="character-counter" id="notes-i63j245-char-count"> 0/200 </div><!--bindings={
  "ng-reflect-ng-if": "true"
}--></div><!--bindings={
  "ng-reflect-ng-if": ""
}--></div></app-notes></div><div _ngcontent-ng-c3567730054="" class="component-code"><pre _ngcontent-ng-c3567730054=""><code _ngcontent-ng-c3567730054="">&lt;app-notes label="Notes" [(ngModel)]="notesText"&gt;&lt;/app-notes&gt;</code></pre></div></div><div _ngcontent-ng-c3567730054="" class="component-item"><h3 _ngcontent-ng-c3567730054="" class="component-title">Notes (Limited)</h3><div _ngcontent-ng-c3567730054="" class="component-subtitle">Style Guide: "Notes" | Implementation: "NotesComponent"</div><div _ngcontent-ng-c3567730054="" class="component-demo figma-sized form-demo"><app-notes _ngcontent-ng-c3567730054="" label="Notes" placeholder="Notes" _nghost-ng-c3668244963="" ng-reflect-label="Notes" ng-reflect-placeholder="Notes" ng-reflect-max-length="100" ng-reflect-model="" class="ng-untouched ng-pristine ng-valid" ngh="13"><div _ngcontent-ng-c3668244963="" class="notes-container"><label _ngcontent-ng-c3668244963="" class="notes-label" for="notes-0aqzfdm"> Notes <!--bindings={
  "ng-reflect-ng-if": "false"
}--></label><!--bindings={
  "ng-reflect-ng-if": "Notes"
}--><div _ngcontent-ng-c3668244963="" class="notes-input-wrapper"><textarea _ngcontent-ng-c3668244963="" class="notes-textarea" id="notes-0aqzfdm" name="" placeholder="Notes" maxlength="100" aria-describedby="notes-0aqzfdm-char-count" jsaction="input:;focus:;blur:;"></textarea><div _ngcontent-ng-c3668244963="" class="character-counter" id="notes-0aqzfdm-char-count"> 0/100 </div><!--bindings={
  "ng-reflect-ng-if": "true"
}--></div><!--bindings={
  "ng-reflect-ng-if": ""
}--></div></app-notes></div><div _ngcontent-ng-c3567730054="" class="component-code"><pre _ngcontent-ng-c3567730054=""><code _ngcontent-ng-c3567730054="">&lt;app-notes [maxLength]="100"&gt;&lt;/app-notes&gt;</code></pre></div></div></div><div _ngcontent-ng-c3567730054="" class="component-row demographics-row full-width"><div _ngcontent-ng-c3567730054="" class="component-item full-width"><h3 _ngcontent-ng-c3567730054="" class="component-title">Demographics</h3><div _ngcontent-ng-c3567730054="" class="component-demo figma-sized demographics-demo"><app-demographics _ngcontent-ng-c3567730054="" _nghost-ng-c1061394248="" ng-reflect-data="[object Object]" ngh="14"><div _ngcontent-ng-c1061394248="" class="demographics-container"><div _ngcontent-ng-c1061394248="" class="demographics-content"><div _ngcontent-ng-c1061394248="" class="left-section"><div _ngcontent-ng-c1061394248="" class="header-section"><div _ngcontent-ng-c1061394248="" class="back-button" jsaction="click:;"><div _ngcontent-ng-c1061394248="" class="back-icon"><svg _ngcontent-ng-c1061394248="" width="12" height="10" viewBox="0 0 12 10" fill="none" xmlns="http://www.w3.org/2000/svg"><path _ngcontent-ng-c1061394248="" d="M4.5 1L1 4.5L4.5 8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path _ngcontent-ng-c1061394248="" d="M1 4.5H11" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></div><span _ngcontent-ng-c1061394248="" class="back-text">Back</span></div><!--bindings={
  "ng-reflect-ng-if": "true"
}--><div _ngcontent-ng-c1061394248="" class="measure-info"><div _ngcontent-ng-c1061394248="" class="measure-title">Controlling Blood Pressure (CBP)</div><div _ngcontent-ng-c1061394248="" class="measure-subtitle">Measure</div></div></div></div><div _ngcontent-ng-c1061394248="" class="right-section"><div _ngcontent-ng-c1061394248="" class="demographics-group primary-group"><div _ngcontent-ng-c1061394248="" class="demographic-item"><div _ngcontent-ng-c1061394248="" class="demographic-value">55820474</div><div _ngcontent-ng-c1061394248="" class="demographic-label">Member ID</div></div><div _ngcontent-ng-c1061394248="" class="demographic-item"><div _ngcontent-ng-c1061394248="" class="demographic-value">John Dey</div><div _ngcontent-ng-c1061394248="" class="demographic-label">Member</div></div><div _ngcontent-ng-c1061394248="" class="demographic-item"><div _ngcontent-ng-c1061394248="" class="demographic-value">01/05/1972</div><div _ngcontent-ng-c1061394248="" class="demographic-label">DOB</div></div><div _ngcontent-ng-c1061394248="" class="demographic-item"><div _ngcontent-ng-c1061394248="" class="demographic-value">M</div><div _ngcontent-ng-c1061394248="" class="demographic-label">Gender</div></div><div _ngcontent-ng-c1061394248="" class="demographic-item"><div _ngcontent-ng-c1061394248="" class="demographic-value">MAHMO</div><div _ngcontent-ng-c1061394248="" class="demographic-label">LOB</div></div></div><div _ngcontent-ng-c1061394248="" class="demographics-group provider-group"><div _ngcontent-ng-c1061394248="" class="demographic-item"><div _ngcontent-ng-c1061394248="" class="demographic-value">Nicolas Dejong PA</div><div _ngcontent-ng-c1061394248="" class="demographic-label">Provider</div></div><div _ngcontent-ng-c1061394248="" class="demographic-item"><div _ngcontent-ng-c1061394248="" class="demographic-value">882716229</div><div _ngcontent-ng-c1061394248="" class="demographic-label">NPI</div></div></div></div></div></div></app-demographics></div><div _ngcontent-ng-c3567730054="" class="component-code"><pre _ngcontent-ng-c3567730054=""><code _ngcontent-ng-c3567730054="">&lt;app-demographics [data]="demographicsData"&gt;&lt;/app-demographics&gt;</code></pre></div></div></div><div _ngcontent-ng-c3567730054="" class="component-row full-width"><div _ngcontent-ng-c3567730054="" class="component-item full-width"><h3 _ngcontent-ng-c3567730054="" class="component-title">Hits</h3><div _ngcontent-ng-c3567730054="" class="component-subtitle">Style Guide: "Hits" | Implementation: "HitsComponent"</div><div _ngcontent-ng-c3567730054="" class="component-demo figma-sized hits-demo"><app-hits _ngcontent-ng-c3567730054="" title="Hits" _nghost-ng-c1281433528="" ng-reflect-title="Hits" ng-reflect-data="[object Object],[object Object" ngh="16"><div _ngcontent-ng-c1281433528="" class="hits-container"><div _ngcontent-ng-c1281433528="" class="hits-header"><div _ngcontent-ng-c1281433528="" class="hits-title">Hits</div></div><!--bindings={
  "ng-reflect-ng-if": "true"
}--><div _ngcontent-ng-c1281433528="" class="hits-table"><div _ngcontent-ng-c1281433528="" class="hits-table-columns"><div _ngcontent-ng-c1281433528="" class="hits-column dos-column"><div _ngcontent-ng-c1281433528="" class="header-item"><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="icon-text"><div _ngcontent-ng-c1281433528="" class="label">DoS</div></div></div></div><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="icon-text"><div _ngcontent-ng-c1281433528="" class="label">07/21/24</div></div></div><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="icon-text"><div _ngcontent-ng-c1281433528="" class="label">07/21/24</div></div></div><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="icon-text"><div _ngcontent-ng-c1281433528="" class="label">05/21/24</div></div></div><!--bindings={
  "ng-reflect-ng-for-track-by": "trackByHitId(_index, hit) {\n  "
}--></div><div _ngcontent-ng-c1281433528="" class="hits-column sys-column"><div _ngcontent-ng-c1281433528="" class="header-item"><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="icon-text"><div _ngcontent-ng-c1281433528="" class="label">Sys</div></div></div></div><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="icon-text"><div _ngcontent-ng-c1281433528="" class="label">136</div></div></div><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="icon-text"><div _ngcontent-ng-c1281433528="" class="label">140</div></div></div><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="icon-text"><div _ngcontent-ng-c1281433528="" class="label">150</div></div></div><!--bindings={
  "ng-reflect-ng-for-track-by": "trackByHitId(_index, hit) {\n  "
}--></div><div _ngcontent-ng-c1281433528="" class="hits-column dias-column"><div _ngcontent-ng-c1281433528="" class="header-item"><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="icon-text"><div _ngcontent-ng-c1281433528="" class="label">Dias</div></div></div></div><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="icon-text"><div _ngcontent-ng-c1281433528="" class="label">82</div></div></div><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="icon-text"><div _ngcontent-ng-c1281433528="" class="label">82</div></div></div><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="icon-text"><div _ngcontent-ng-c1281433528="" class="label">90</div></div></div><!--bindings={
  "ng-reflect-ng-for-track-by": "trackByHitId(_index, hit) {\n  "
}--></div><div _ngcontent-ng-c1281433528="" class="hits-column page-column"><div _ngcontent-ng-c1281433528="" class="header-item"><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="icon-text"><div _ngcontent-ng-c1281433528="" class="label">Page</div></div></div></div><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="icon-text"><button _ngcontent-ng-c1281433528="" type="button" class="page-link" jsaction="click:;"><div _ngcontent-ng-c1281433528="" class="label">2</div></button></div></div><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="icon-text"><button _ngcontent-ng-c1281433528="" type="button" class="page-link" jsaction="click:;"><div _ngcontent-ng-c1281433528="" class="label">2</div></button></div></div><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="icon-text"><button _ngcontent-ng-c1281433528="" type="button" class="page-link" jsaction="click:;"><div _ngcontent-ng-c1281433528="" class="label">7</div></button></div></div><!--bindings={
  "ng-reflect-ng-for-track-by": "trackByHitId(_index, hit) {\n  "
}--></div><div _ngcontent-ng-c1281433528="" class="hits-column comment-column"><div _ngcontent-ng-c1281433528="" class="header-item"><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="icon-text"><div _ngcontent-ng-c1281433528="" class="label">Comment</div></div></div></div><div _ngcontent-ng-c1281433528="" class="table-item"><app-comment-box _ngcontent-ng-c1281433528="" class="comment-box-component ng-untouched ng-pristine ng-valid" _nghost-ng-c62406540="" ng-reflect-model="" ng-reflect-placeholder="Comment" aria-label="Comment for hit 1" ngh="2"><div _ngcontent-ng-c62406540="" class="comment-box default"><div _ngcontent-ng-c62406540="" class="row"><input _ngcontent-ng-c62406540="" type="text" class="comment-input" value="" placeholder="Comment" jsaction="input:;focus:;blur:;"></div></div></app-comment-box></div><div _ngcontent-ng-c1281433528="" class="table-item"><app-comment-box _ngcontent-ng-c1281433528="" class="comment-box-component ng-untouched ng-pristine ng-valid" _nghost-ng-c62406540="" ng-reflect-model="" ng-reflect-placeholder="Comment" aria-label="Comment for hit 2" ngh="2"><div _ngcontent-ng-c62406540="" class="comment-box default"><div _ngcontent-ng-c62406540="" class="row"><input _ngcontent-ng-c62406540="" type="text" class="comment-input" value="" placeholder="Comment" jsaction="input:;focus:;blur:;"></div></div></app-comment-box></div><div _ngcontent-ng-c1281433528="" class="table-item"><app-comment-box _ngcontent-ng-c1281433528="" class="comment-box-component ng-untouched ng-pristine ng-valid" _nghost-ng-c62406540="" ng-reflect-model="" ng-reflect-placeholder="Comment" aria-label="Comment for hit 3" ngh="2"><div _ngcontent-ng-c62406540="" class="comment-box default"><div _ngcontent-ng-c62406540="" class="row"><input _ngcontent-ng-c62406540="" type="text" class="comment-input" value="" placeholder="Comment" jsaction="input:;focus:;blur:;"></div></div></app-comment-box></div><!--bindings={
  "ng-reflect-ng-for-track-by": "trackByHitId(_index, hit) {\n  "
}--></div><div _ngcontent-ng-c1281433528="" class="hits-column include-column"><div _ngcontent-ng-c1281433528="" class="header-item"><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="icon-text"><div _ngcontent-ng-c1281433528="" class="label">Include</div></div></div></div><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="include-column-inner"><app-checkbox _ngcontent-ng-c1281433528="" class="include-checkbox ng-untouched ng-pristine ng-valid" _nghost-ng-c2693716353="" ng-reflect-model="false" aria-label="Include hit 1" ngh="15"><div _ngcontent-ng-c2693716353="" class="checkbox-container"><input _ngcontent-ng-c2693716353="" type="checkbox" class="checkbox" id="checkbox-83mzpui" name="" jsaction="change:;blur:;"><!--bindings={
  "ng-reflect-ng-if": ""
}--></div></app-checkbox></div></div><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="include-column-inner"><app-checkbox _ngcontent-ng-c1281433528="" class="include-checkbox ng-untouched ng-pristine ng-valid" _nghost-ng-c2693716353="" ng-reflect-model="false" aria-label="Include hit 2" ngh="15"><div _ngcontent-ng-c2693716353="" class="checkbox-container"><input _ngcontent-ng-c2693716353="" type="checkbox" class="checkbox" id="checkbox-7ivfu1t" name="" jsaction="change:;blur:;"><!--bindings={
  "ng-reflect-ng-if": ""
}--></div></app-checkbox></div></div><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="include-column-inner"><app-checkbox _ngcontent-ng-c1281433528="" class="include-checkbox ng-untouched ng-pristine ng-valid" _nghost-ng-c2693716353="" ng-reflect-model="false" aria-label="Include hit 3" ngh="15"><div _ngcontent-ng-c2693716353="" class="checkbox-container"><input _ngcontent-ng-c2693716353="" type="checkbox" class="checkbox" id="checkbox-qcagops" name="" jsaction="change:;blur:;"><!--bindings={
  "ng-reflect-ng-if": ""
}--></div></app-checkbox></div></div><!--bindings={
  "ng-reflect-ng-for-track-by": "trackByHitId(_index, hit) {\n  "
}--></div></div></div></div></app-hits></div><div _ngcontent-ng-c3567730054="" class="component-code"><pre _ngcontent-ng-c3567730054=""><code _ngcontent-ng-c3567730054="">&lt;app-hits title="Hits" [data]="hitsData"&gt;&lt;/app-hits&gt;</code></pre></div></div></div><div _ngcontent-ng-c3567730054="" class="component-row full-width"><div _ngcontent-ng-c3567730054="" class="component-item full-width"><h3 _ngcontent-ng-c3567730054="" class="component-title">Menu</h3><div _ngcontent-ng-c3567730054="" class="component-demo figma-sized menu-demo"><app-menu _ngcontent-ng-c3567730054="" logosrc="assets/logos/Stellarus_logo_2C_blacktype.png?v=2024" logoalt="Stellarus Logo" _nghost-ng-c3806090911="" ng-reflect-logo-src="assets/logos/Stellarus_logo_2C" ng-reflect-logo-alt="Stellarus Logo" ng-reflect-user="[object Object]" ng-reflect-menu-items="[object Object],[object Object" ngh="17"><nav _ngcontent-ng-c3806090911="" class="menu-container"><div _ngcontent-ng-c3806090911="" class="menu-content"><div _ngcontent-ng-c3806090911="" class="logo-section"><div _ngcontent-ng-c3806090911="" class="logo-container" jsaction="click:;"><img _ngcontent-ng-c3806090911="" class="logo-image" src="assets/logos/Stellarus_logo_2C_blacktype.png?v=2024" alt="Stellarus Logo"><!--bindings={
  "ng-reflect-ng-if": "assets/logos/Stellarus_logo_2C"
}--><!--bindings={
  "ng-reflect-ng-if": "false"
}--></div></div><div _ngcontent-ng-c3806090911="" class="user-section"><div _ngcontent-ng-c3806090911="" class="user-info"><span _ngcontent-ng-c3806090911="" class="user-name">Jane Chu</span><div _ngcontent-ng-c3806090911="" class="user-avatar" jsaction="click:;"><div _ngcontent-ng-c3806090911="" class="avatar-circle"><svg _ngcontent-ng-c3806090911="" viewBox="0 0 20 20" fill="none" class="user-icon"><circle _ngcontent-ng-c3806090911="" cx="10" cy="7.5" r="2.75" stroke="currentColor" stroke-width="1.5" fill="none"></circle><path _ngcontent-ng-c3806090911="" d="M4.5 16.5c0-3 2.5-5.5 5.5-5.5s5.5 2.5 5.5 5.5" stroke="currentColor" stroke-width="1.5" fill="none"></path><circle _ngcontent-ng-c3806090911="" cx="10" cy="10" r="8.33" stroke="currentColor" stroke-width="1.5" fill="none"></circle></svg></div></div><div _ngcontent-ng-c3806090911="" class="dropdown-arrow" jsaction="click:;"><svg _ngcontent-ng-c3806090911="" viewBox="0 0 24 24" fill="none" class="arrow-icon"><path _ngcontent-ng-c3806090911="" d="M8 10l4 4 4-4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></div><!--bindings={
  "ng-reflect-ng-if": "true"
}--></div><!--bindings={
  "ng-reflect-ng-if": "false"
}--></div><!--bindings={
  "ng-reflect-ng-if": "[object Object]"
}--></div></nav></app-menu></div><div _ngcontent-ng-c3567730054="" class="component-code"><pre _ngcontent-ng-c3567730054=""><code _ngcontent-ng-c3567730054="">&lt;app-menu [user]="userProfile" [menuItems]="menuItems"&gt;&lt;/app-menu&gt;</code></pre></div></div></div></section></div></app-component-test><!--container--></app-root>
<link rel="modulepreload" href="chunk-YLWVG4A5.js"><link rel="modulepreload" href="chunk-FVKW5FZS.js"><link rel="modulepreload" href="chunk-JRLQF6CE.js"><link rel="modulepreload" href="chunk-F5HTA5WY.js"><link rel="modulepreload" href="chunk-PC6IZSQ2.js"><script src="polyfills.js" type="module"></script><script src="main.js" type="module"></script>

<script id="ng-state" type="application/json">{"__nghData__":[{"t":{"1":"t0","3":"t1"},"c":{"1":[],"3":[]},"n":{"3":"0fn2"}},{"t":{"1":"t0","3":"t1"},"c":{"1":[],"3":[]},"n":{"3":"0fn3"}},{},{"t":{"2":"t2"},"c":{"2":[{"i":"t2","r":1}]}},{"t":{"2":"t3","10":"t5","11":"t6"},"c":{"2":[{"i":"t3","r":1,"t":{"2":"t4"},"c":{"2":[]}}],"10":[],"11":[]}},{"t":{"2":"t7","13":"t9","14":"t10"},"c":{"2":[{"i":"t7","r":1,"t":{"2":"t8"},"c":{"2":[]}}],"13":[],"14":[]}},{"t":{"2":"t7","13":"t9","14":"t10"},"c":{"2":[{"i":"t7","r":1,"t":{"2":"t8"},"c":{"2":[{"i":"t8","r":1}]}}],"13":[],"14":[]}},{"t":{"2":"t7","13":"t9","14":"t10"},"c":{"2":[],"13":[],"14":[]}},{"t":{"1":"t13","4":"t14","5":"t15"},"c":{"1":[],"4":[{"i":"t14","r":1}],"5":[]}},{"t":{"6":"t11","8":"t12","9":"t16","10":"t17","11":"t18"},"c":{"6":[{"i":"t11","r":1,"x":3}],"8":[{"i":"t12","r":1}],"9":[],"10":[],"11":[]}},{"t":{"2":"t3","10":"t5","11":"t6"},"c":{"2":[],"10":[],"11":[]}},{"t":{"6":"t11","8":"t12","9":"t16","10":"t17","11":"t18"},"c":{"6":[{"i":"t11","r":1,"x":3}],"8":[],"9":[{"i":"t16","r":1}],"10":[],"11":[]}},{"t":{"9":"t19","15":"t20","21":"t21","27":"t22","33":"t24","39":"t25","45":"t26","51":"t27","57":"t28","63":"t29","69":"t30","70":"t31"},"c":{"9":[{"i":"t19","r":2,"e":{"0":1},"x":3}],"15":[{"i":"t20","r":2,"e":{"0":1},"x":3}],"21":[{"i":"t21","r":2,"e":{"0":1},"x":3}],"27":[{"i":"t22","r":2,"e":{"0":1},"t":{"3":"t23"},"c":{"3":[]}},{"i":"t22","r":2,"e":{"0":1},"t":{"3":"t23"},"c":{"3":[{"i":"t23","r":1}]}},{"i":"t22","r":2,"e":{"0":1},"t":{"3":"t23"},"c":{"3":[]}}],"33":[{"i":"t24","r":2,"e":{"0":1},"x":3}],"39":[{"i":"t25","r":2,"e":{"0":1},"x":3}],"45":[{"i":"t26","r":2,"e":{"0":1},"x":3}],"51":[{"i":"t27","r":2,"e":{"0":1},"x":3}],"57":[{"i":"t28","r":2,"e":{"0":1},"x":3}],"63":[{"i":"t29","r":2,"e":{"0":1},"x":3}],"69":[{"i":"t30","r":2,"e":{"0":1},"x":3}],"70":[]}},{"t":{"1":"t13","4":"t14","5":"t15"},"c":{"1":[{"i":"t13","r":1,"t":{"2":"t32"},"c":{"2":[]}}],"4":[{"i":"t14","r":1}],"5":[]}},{"t":{"4":"t33"},"c":{"4":[{"i":"t33","r":1}]}},{"t":{"2":"t2"},"c":{"2":[]}},{"t":{"1":"t34","10":"t35","17":"t36","24":"t37","31":"t38","38":"t39","45":"t40"},"c":{"1":[{"i":"t34","r":1}],"10":[{"i":"t35","r":1,"x":3}],"17":[{"i":"t36","r":1,"x":3}],"24":[{"i":"t37","r":1,"x":3}],"31":[{"i":"t38","r":1,"x":3}],"38":[{"i":"t39","r":1,"x":3}],"45":[{"i":"t40","r":1,"x":3}]}},{"t":{"4":"t41","5":"t42","6":"t43"},"c":{"4":[{"i":"t41","r":1}],"5":[],"6":[{"i":"t43","r":1,"t":{"10":"t44","11":"t45"},"c":{"10":[{"i":"t44","r":1}],"11":[]}}]}},{"n":{"30":"29f2n","42":"41f2n","55":"54f2n","67":"66f2n","80":"79f2n","93":"92f2n","107":"106f2n","125":"124f2n","137":"136f2n"}},{"c":{"0":[{"i":"c3567730054","r":1}]}}]}</script></body></html>
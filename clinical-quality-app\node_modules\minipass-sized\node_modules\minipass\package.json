{"name": "minipass", "version": "3.3.6", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "types": "index.d.ts", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typescript": "^4.7.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "license": "ISC", "files": ["index.d.ts", "index.js"], "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}}
{"version": 3, "sources": ["src/app/shared/components/icons/refresh-icon.component.scss", "src/styles/_variables.scss"], "sourcesContent": ["@use 'styles/variables' as vars;\n\n:host {\n  display: inline-block;\n  width: 16px;\n  height: 16px;\n}\n\n.refresh-icon-default {\n  color: vars.$text-black; // #17181A - Figma: black on white/gray buttons\n}\n\n.refresh-icon-hover {\n  color: vars.$text-black; // #17181A - Figma: stays black on hover\n}\n\n.refresh-icon-click {\n  color: vars.$white; // white - Figma: white on dark background\n}\n\nsvg {\n  width: 16px;\n  height: 16px;\n  display: block;\n}\n", "// Color Variables\r\n$text-black: #17181A;\r\n$primary-blue: #3870B8;\r\n$hover-blue: #468CE7;\r\n$click-blue: #285082;\r\n$light-blue: #BFD0EE;\r\n$link: #0071BC;\r\n$gray-1: #F1F5F7;\r\n$gray-2: #D9E1E7;\r\n$gray-3: #547996;\r\n$gray-4: #384455;\r\n$success-green: #1AD598;\r\n$white: #FFFFFF;\r\n$background-gray: #F6F6F6;\r\n$light-background: #F9FBFC;\r\n$light-primary: #809FB8;\r\n\r\n// Opacity Variables\r\n$primary-blue-opacity-20: rgba(56, 112, 184, 0.20);\r\n$success-green-opacity-10: rgba(26, 213, 152, 0.10);\r\n$success-green-opacity-40: rgba(26, 213, 152, 0.40);\r\n\r\n// Spacing Variables\r\n$spacing-xs: 4px;\r\n$spacing-sm: 8px;\r\n$spacing-md: 12px;\r\n$spacing-lg: 16px;\r\n$spacing-xl: 20px;\r\n$spacing-xxl: 24px;\r\n$spacing-xxxl: 30px;\r\n$spacing-huge: 40px;\r\n$spacing-giant: 48px;\r\n$spacing-mega: 55px;\r\n\r\n// Border Radius\r\n$border-radius-sm: 5px;\r\n$border-radius-md: 6px;\r\n$border-radius-lg: 8px;\r\n$border-radius-xl: 10px;\r\n$border-radius-round: 90px;\r\n$border-radius-circle: 120px;\r\n\r\n// Box Shadow\r\n$box-shadow-sm: 0px 1px 2px rgba(16, 24, 40, 0.05);\r\n\r\n// Border Width\r\n$border-width-default: 1px;\r\n$border-width-thick: 1.5px;\r\n\r\n// Z-index\r\n$z-index-default: 1;\r\n$z-index-dropdown: 1000;\r\n$z-index-sticky: 1020;\r\n$z-index-fixed: 1030;\r\n$z-index-modal-backdrop: 1040;\r\n$z-index-modal: 1050;\r\n$z-index-popover: 1060;\r\n$z-index-tooltip: 1070;"], "mappings": ";AAEA;AACE,WAAA;AACA,SAAA;AACA,UAAA;;AAGF,CAAA;AACE,SCRW;;ADWb,CAAA;AACE,SCZW;;ADeb,CAAA;AACE,SCLM;;ADQR;AACE,SAAA;AACA,UAAA;AACA,WAAA;;", "names": []}
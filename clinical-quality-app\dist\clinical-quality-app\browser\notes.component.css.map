{"version": 3, "sources": ["src/app/shared/components/notes/notes.component.scss", "src/styles/_variables.scss", "src/styles/_mixins.scss"], "sourcesContent": ["@use 'variables' as variables;\n@use 'mixins' as mix;\n\n.notes-container {\n  position: relative;\n  width: 100%;\n\n  &.disabled {\n    opacity: 0.5;\n    pointer-events: none;\n  }\n}\n\n.notes-label {\n  display: block;\n  font-size: 10px; // Figma specification\n  font-family: 'Urbane', sans-serif;\n  font-weight: 300;\n  color: variables.$gray-3; // Figma specification\n  margin-bottom: 4px; // Figma spacing\n\n  .required-indicator {\n    color: variables.$primary-blue;\n    margin-left: 2px;\n  }\n}\n\n// Simple Notes Input Wrapper (Figma design)\n.notes-input-wrapper {\n  position: relative;\n  width: 100%;\n  box-sizing: border-box;\n}\n\n// Simple Notes Textarea (Figma design)\n.notes-textarea {\n  width: 100%;\n  min-height: 120px; // Large height as shown in Figma\n  padding: 12px;\n  border: 1px solid variables.$gray-2;\n  border-radius: 8px;\n  background: #ffffff;\n  font-size: 12px;\n  font-family: 'Urbane', sans-serif;\n  font-weight: 300;\n  line-height: 16px;\n  color: variables.$gray-3;\n  resize: vertical; // Allow vertical resize\n  box-sizing: border-box;\n  outline: none;\n  transition: all 0.2s ease;\n\n  &::placeholder {\n    color: variables.$gray-3;\n  }\n\n  &:focus {\n    border-color: variables.$gray-3;\n    color: variables.$text-black;\n  }\n\n  &.has-value {\n    color: variables.$text-black;\n  }\n\n  &:disabled {\n    background-color: variables.$gray-1;\n    cursor: not-allowed;\n    color: variables.$gray-3;\n  }\n\n  &:hover:not(:disabled) {\n    border-color: variables.$gray-3;\n  }\n\n  // Custom scrollbar\n  &::-webkit-scrollbar {\n    width: 6px;\n  }\n\n  &::-webkit-scrollbar-track {\n    background: transparent;\n  }\n\n  &::-webkit-scrollbar-thumb {\n    background: variables.$gray-2;\n    border-radius: 3px;\n\n    &:hover {\n      background: variables.$gray-3;\n    }\n  }\n}\n\n// Character Counter (bottom-right overlay)\n.character-counter {\n  position: absolute;\n  bottom: 8px;\n  right: 12px;\n  font-size: 10px;\n  font-family: 'Urbane', sans-serif;\n  font-weight: 300;\n  line-height: 20px;\n  color: variables.$gray-3;\n  background: rgba(255, 255, 255, 0.9);\n  padding: 2px 4px;\n  border-radius: 4px;\n  pointer-events: none;\n\n  &.near-limit {\n    color: #F4A261; // Warning color\n  }\n\n  &.over-limit {\n    color: #F4454E; // Error color\n  }\n}\n\n.error-message {\n  margin-top: variables.$spacing-xs;\n  font-size: 12px;\n  font-family: 'Urbane', sans-serif;\n  font-weight: 300;\n  color: #F4454E;\n  line-height: 16px;\n}\n\n// Responsive design\n@include mix.for-phone-only {\n  .notes-textarea {\n    min-height: 100px; // Slightly smaller for mobile\n    padding: 10px;\n  }\n\n  .character-counter {\n    font-size: 9px;\n    bottom: 6px;\n    right: 10px;\n  }\n}\n\n@include mix.for-tablet-portrait-up {\n  .notes-textarea {\n    min-height: 120px; // Full height for larger screens\n    padding: 12px;\n  }\n\n  .character-counter {\n    font-size: 10px;\n    bottom: 8px;\n    right: 12px;\n  }\n\n}", "// Color Variables\r\n$text-black: #17181A;\r\n$primary-blue: #3870B8;\r\n$hover-blue: #468CE7;\r\n$click-blue: #285082;\r\n$light-blue: #BFD0EE;\r\n$link: #0071BC;\r\n$gray-1: #F1F5F7;\r\n$gray-2: #D9E1E7;\r\n$gray-3: #547996;\r\n$gray-4: #384455;\r\n$success-green: #1AD598;\r\n$white: #FFFFFF;\r\n$background-gray: #F6F6F6;\r\n$light-background: #F9FBFC;\r\n$light-primary: #809FB8;\r\n\r\n// Opacity Variables\r\n$primary-blue-opacity-20: rgba(56, 112, 184, 0.20);\r\n$success-green-opacity-10: rgba(26, 213, 152, 0.10);\r\n$success-green-opacity-40: rgba(26, 213, 152, 0.40);\r\n\r\n// Spacing Variables\r\n$spacing-xs: 4px;\r\n$spacing-sm: 8px;\r\n$spacing-md: 12px;\r\n$spacing-lg: 16px;\r\n$spacing-xl: 20px;\r\n$spacing-xxl: 24px;\r\n$spacing-xxxl: 30px;\r\n$spacing-huge: 40px;\r\n$spacing-giant: 48px;\r\n$spacing-mega: 55px;\r\n\r\n// Border Radius\r\n$border-radius-sm: 5px;\r\n$border-radius-md: 6px;\r\n$border-radius-lg: 8px;\r\n$border-radius-xl: 10px;\r\n$border-radius-round: 90px;\r\n$border-radius-circle: 120px;\r\n\r\n// Box Shadow\r\n$box-shadow-sm: 0px 1px 2px rgba(16, 24, 40, 0.05);\r\n\r\n// Border Width\r\n$border-width-default: 1px;\r\n$border-width-thick: 1.5px;\r\n\r\n// Z-index\r\n$z-index-default: 1;\r\n$z-index-dropdown: 1000;\r\n$z-index-sticky: 1020;\r\n$z-index-fixed: 1030;\r\n$z-index-modal-backdrop: 1040;\r\n$z-index-modal: 1050;\r\n$z-index-popover: 1060;\r\n$z-index-tooltip: 1070;", "// Import variables\r\n@use 'variables' as variables;\r\n\r\n// Flexbox Mixins\r\n@mixin flex-row {\r\n  display: flex;\r\n  flex-direction: row;\r\n}\r\n\r\n@mixin flex-column {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n@mixin flex-center {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n@mixin flex-between {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n@mixin flex-start {\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  align-items: center;\r\n}\r\n\r\n@mixin flex-end {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  align-items: center;\r\n}\r\n\r\n// Layout Mixins\r\n@mixin container {\r\n  width: 100%;\r\n  padding-left: variables.$spacing-xxxl;\r\n  padding-right: variables.$spacing-xxxl;\r\n}\r\n\r\n@mixin card {\r\n  background: variables.$white;\r\n  border-radius: variables.$border-radius-lg;\r\n  outline: variables.$border-width-default variables.$gray-1 solid;\r\n  outline-offset: -(variables.$border-width-default);\r\n  padding: variables.$spacing-xl;\r\n  margin-bottom: variables.$spacing-xl;\r\n}\r\n\r\n// Button Mixins\r\n@mixin button-base {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  gap: variables.$spacing-sm;\r\n  border-radius: variables.$border-radius-lg;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  border: none;\r\n  outline: none;\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n@mixin button-primary {\r\n  @include button-base;\r\n  background: variables.$primary-blue;\r\n  color: variables.$white;\r\n  padding: variables.$spacing-sm variables.$spacing-lg;\r\n\r\n  &:hover {\r\n    background: variables.$hover-blue;\r\n  }\r\n\r\n  &:active {\r\n    background: variables.$click-blue;\r\n  }\r\n\r\n  &:disabled {\r\n    background: variables.$light-blue;\r\n    cursor: not-allowed;\r\n  }\r\n}\r\n\r\n@mixin button-secondary {\r\n  @include button-base;\r\n  background: variables.$white;\r\n  outline: variables.$border-width-default variables.$gray-2 solid;\r\n  outline-offset: -(variables.$border-width-default);\r\n  color: variables.$text-black;\r\n  padding: variables.$spacing-sm variables.$spacing-md;\r\n\r\n  &:hover {\r\n    background: variables.$gray-1;\r\n  }\r\n\r\n  &:active {\r\n    background: variables.$text-black;\r\n    color: variables.$white;\r\n  }\r\n}\r\n\r\n@mixin button-icon {\r\n  @include button-base;\r\n  gap: variables.$spacing-sm;\r\n\r\n  .icon {\r\n    width: 20px;\r\n    height: 20px;\r\n  }\r\n}\r\n\r\n// Form Element Mixins\r\n@mixin input-field {\r\n  padding: variables.$spacing-md;\r\n  border-radius: variables.$border-radius-xl;\r\n  outline: variables.$border-width-default variables.$gray-2 solid;\r\n  outline-offset: -(variables.$border-width-default);\r\n  font-size: 12px;\r\n  font-family: 'Urbane', sans-serif;\r\n  width: 100%;\r\n\r\n  &:focus {\r\n    outline-color: variables.$gray-3;\r\n  }\r\n}\r\n\r\n@mixin textarea-field {\r\n  padding: variables.$spacing-md;\r\n  border-radius: variables.$border-radius-xl;\r\n  outline: variables.$border-width-default variables.$gray-2 solid;\r\n  outline-offset: -(variables.$border-width-default);\r\n  font-size: 12px;\r\n  font-family: 'Urbane', sans-serif;\r\n  width: 100%;\r\n  min-height: 88px;\r\n\r\n  &:focus {\r\n    outline-color: variables.$gray-3;\r\n  }\r\n}\r\n\r\n@mixin checkbox {\r\n  width: 16px;\r\n  height: 16px;\r\n  border-radius: 5px; // Figma specifies 5px border radius\r\n  border: 1px solid variables.$gray-2;\r\n  background-color: variables.$white;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  appearance: none;\r\n  -webkit-appearance: none;\r\n  -moz-appearance: none;\r\n  position: relative;\r\n\r\n  &:checked {\r\n    background-color: variables.$text-black;\r\n    border-color: variables.$text-black;\r\n\r\n    &::after {\r\n      content: '';\r\n      position: absolute;\r\n      left: 4px;\r\n      top: 4px;\r\n      width: 8.33px;\r\n      height: 7.5px;\r\n      background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8.33' height='7.5' viewBox='0 0 8.33 7.5'%3E%3Cpath d='M1 3.75L3.5 6.25L7.33 1.25' stroke='white' stroke-width='1.5' fill='none' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E\");\r\n      background-repeat: no-repeat;\r\n      background-position: center;\r\n      background-size: contain;\r\n    }\r\n  }\r\n\r\n  &:hover:not(:disabled) {\r\n    border-color: variables.$gray-3;\r\n  }\r\n\r\n  &:focus {\r\n    outline: 2px solid variables.$primary-blue;\r\n    outline-offset: 2px;\r\n  }\r\n\r\n  &:disabled {\r\n    opacity: 0.5;\r\n    cursor: not-allowed;\r\n  }\r\n}\r\n\r\n// Table Mixins\r\n@mixin table-header {\r\n  padding: variables.$spacing-md;\r\n  border-bottom: variables.$border-width-default variables.$gray-1 solid;\r\n  font-weight: 500;\r\n  color: variables.$text-black;\r\n  height: 68px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n@mixin table-cell {\r\n  padding: variables.$spacing-md;\r\n  font-weight: 300;\r\n  height: 68px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-start;\r\n}\r\n\r\n\r\n\r\n// Icon Mixins\r\n@mixin icon-container {\r\n  width: 20px;\r\n  height: 20px;\r\n  position: relative;\r\n}\r\n\r\n// Status Indicators\r\n@mixin status-badge($color, $bg-color) {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: variables.$spacing-xs variables.$spacing-sm;\r\n  border-radius: variables.$border-radius-round;\r\n  background-color: $bg-color;\r\n  color: $color;\r\n  font-size: 11px;\r\n  font-weight: 300;\r\n}\r\n\r\n@mixin success-badge {\r\n  @include status-badge(variables.$success-green, variables.$success-green-opacity-10);\r\n  outline: variables.$border-width-default variables.$success-green-opacity-40 solid;\r\n  outline-offset: -(variables.$border-width-default);\r\n}\r\n\r\n// Responsive Mixins\r\n@mixin for-phone-only {\r\n  @media (max-width: 599px) { @content; }\r\n}\r\n\r\n@mixin for-tablet-portrait-up {\r\n  @media (min-width: 600px) { @content; }\r\n}\r\n\r\n@mixin for-tablet-landscape-up {\r\n  @media (min-width: 900px) { @content; }\r\n}\r\n\r\n@mixin for-desktop-up {\r\n  @media (min-width: 1200px) { @content; }\r\n}\r\n\r\n@mixin for-big-desktop-up {\r\n  @media (min-width: 1800px) { @content; }\r\n}"], "mappings": ";AAGA,CAAA;AACE,YAAA;AACA,SAAA;;AAEA,CAJF,eAIE,CAAA;AACE,WAAA;AACA,kBAAA;;AAIJ,CAAA;AACE,WAAA;AACA,aAAA;AACA,eAAA,QAAA,EAAA;AACA,eAAA;AACA,SCTO;ADUP,iBAAA;;AAEA,CARF,YAQE,CAAA;AACE,SCpBW;ADqBX,eAAA;;AAKJ,CAAA;AACE,YAAA;AACA,SAAA;AACA,cAAA;;AAIF,CAAA;AACE,SAAA;AACA,cAAA;AACA,WAAA;AACA,UAAA,IAAA,MAAA;AACA,iBAAA;AACA,cAAA;AACA,aAAA;AACA,eAAA,QAAA,EAAA;AACA,eAAA;AACA,eAAA;AACA,SCrCO;ADsCP,UAAA;AACA,cAAA;AACA,WAAA;AACA,cAAA,IAAA,KAAA;;AAEA,CAjBF,cAiBE;AACE,SC5CK;;AD+CP,CArBF,cAqBE;AACE,gBChDK;ADiDL,SCzDS;;AD4DX,CA1BF,cA0BE,CAAA;AACE,SC7DS;;ADgEX,CA9BF,cA8BE;AACE,oBC3DK;AD4DL,UAAA;AACA,SC3DK;;AD8DP,CApCF,cAoCE,MAAA,KAAA;AACE,gBC/DK;;ADmEP,CAzCF,cAyCE;AACE,SAAA;;AAGF,CA7CF,cA6CE;AACE,cAAA;;AAGF,CAjDF,cAiDE;AACE,cC7EK;AD8EL,iBAAA;;AAEA,CArDJ,cAqDI,yBAAA;AACE,cChFG;;ADsFT,CAAA;AACE,YAAA;AACA,UAAA;AACA,SAAA;AACA,aAAA;AACA,eAAA,QAAA,EAAA;AACA,eAAA;AACA,eAAA;AACA,SC9FO;AD+FP,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,WAAA,IAAA;AACA,iBAAA;AACA,kBAAA;;AAEA,CAdF,iBAcE,CAAA;AACE,SAAA;;AAGF,CAlBF,iBAkBE,CAAA;AACE,SAAA;;AAIJ,CAAA;AACE,cChGW;ADiGX,aAAA;AACA,eAAA,QAAA,EAAA;AACA,eAAA;AACA,SAAA;AACA,eAAA;;AEsHA,OAAA,CAAA,SAAA,EAAA;AFjHA,GA9FF;AA+FI,gBAAA;AACA,aAAA;;AAGF,GAvCF;AAwCI,eAAA;AACA,YAAA;AACA,WAAA;;;AE6GF,OAAA,CAAA,SAAA,EAAA;AFxGA,GA3GF;AA4GI,gBAAA;AACA,aAAA;;AAGF,GApDF;AAqDI,eAAA;AACA,YAAA;AACA,WAAA;;;", "names": []}
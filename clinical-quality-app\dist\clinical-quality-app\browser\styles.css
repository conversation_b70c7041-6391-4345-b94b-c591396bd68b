/* src/styles.scss */
@font-face {
  font-family: "Urbane";
  src: url("./media/Urbane-Light.woff2") format("woff2"), url("./media/Urbane-Light.woff") format("woff");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Urbane";
  src: url("./media/Urbane-Medium.woff2") format("woff2"), url("./media/Urbane-Medium.woff") format("woff");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Urbane";
  src: url("./media/Urbane-Medium.woff2") format("woff2"), url("./media/Urbane-Medium.woff") format("woff");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Urbane";
  src: url("./media/Urbane-DemiBold.woff2") format("woff2"), url("./media/Urbane-DemiBold.woff") format("woff");
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}
.text-xs {
  font-size: 10px;
  font-family: "Urbane", sans-serif;
  line-height: 20px;
}
.text-sm {
  font-size: 11px;
  font-family: "Urbane", sans-serif;
  line-height: 20px;
}
.text-base {
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  line-height: 20px;
}
.text-md {
  font-size: 14px;
  font-family: "Urbane", sans-serif;
  line-height: 20px;
}
.text-lg {
  font-size: 16px;
  font-family: "Urbane", sans-serif;
  line-height: 20px;
}
.text-xl {
  font-size: 20px;
  font-family: "Urbane", sans-serif;
  line-height: 32px;
}
.text-xxl {
  font-size: 24px;
  font-family: "Urbane", sans-serif;
  line-height: 32px;
}
.font-light {
  font-weight: 300;
}
.font-regular {
  font-weight: 400;
}
.font-medium {
  font-weight: 500;
}
.font-semibold {
  font-weight: 600;
}
.label-text {
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  line-height: 20px;
  font-weight: 300;
  color: #17181A;
}
.link-text {
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  line-height: 20px;
  font-weight: 500;
  color: #0071BC;
}
.heading-text {
  font-size: 20px;
  font-family: "Urbane", sans-serif;
  line-height: 32px;
  font-weight: 600;
  color: #17181A;
}
.subheading-text {
  font-size: 14px;
  font-family: "Urbane", sans-serif;
  line-height: 20px;
  font-weight: 600;
  color: #17181A;
}
.caption-text {
  font-size: 10px;
  font-family: "Urbane", sans-serif;
  line-height: 20px;
  font-weight: 500;
  color: #547996;
}
body {
  font-family: "Urbane", sans-serif;
  font-size: 12px;
  line-height: 20px;
  color: #17181A;
}
html {
  color-scheme: light dark;
}
html {
  --mat-sys-background: #fcf8fd;
  --mat-sys-error: #ba1a1a;
  --mat-sys-error-container: #ffdad6;
  --mat-sys-inverse-on-surface: #f3eff4;
  --mat-sys-inverse-primary: #bec2ff;
  --mat-sys-inverse-surface: #303034;
  --mat-sys-on-background: #1b1b1f;
  --mat-sys-on-error: #ffffff;
  --mat-sys-on-error-container: #93000a;
  --mat-sys-on-primary: #ffffff;
  --mat-sys-on-primary-container: #0000ef;
  --mat-sys-on-primary-fixed: #00006e;
  --mat-sys-on-primary-fixed-variant: #0000ef;
  --mat-sys-on-secondary: #ffffff;
  --mat-sys-on-secondary-container: #444559;
  --mat-sys-on-secondary-fixed: #191a2c;
  --mat-sys-on-secondary-fixed-variant: #444559;
  --mat-sys-on-surface: #1b1b1f;
  --mat-sys-on-surface-variant: #46464f;
  --mat-sys-on-tertiary: #ffffff;
  --mat-sys-on-tertiary-container: #004f4f;
  --mat-sys-on-tertiary-fixed: #002020;
  --mat-sys-on-tertiary-fixed-variant: #004f4f;
  --mat-sys-outline: #777680;
  --mat-sys-outline-variant: #c7c5d0;
  --mat-sys-primary: #343dff;
  --mat-sys-primary-container: #e0e0ff;
  --mat-sys-primary-fixed: #e0e0ff;
  --mat-sys-primary-fixed-dim: #bec2ff;
  --mat-sys-scrim: #000000;
  --mat-sys-secondary: #5c5d72;
  --mat-sys-secondary-container: #e1e0f9;
  --mat-sys-secondary-fixed: #e1e0f9;
  --mat-sys-secondary-fixed-dim: #c5c4dd;
  --mat-sys-shadow: #000000;
  --mat-sys-surface: #fcf8fd;
  --mat-sys-surface-bright: #fcf8fd;
  --mat-sys-surface-container: #f0edf1;
  --mat-sys-surface-container-high: #ebe7eb;
  --mat-sys-surface-container-highest: #e5e1e6;
  --mat-sys-surface-container-low: #f6f2f7;
  --mat-sys-surface-container-lowest: #ffffff;
  --mat-sys-surface-dim: #dcd9dd;
  --mat-sys-surface-tint: #343dff;
  --mat-sys-surface-variant: #e4e1ec;
  --mat-sys-tertiary: #006a6a;
  --mat-sys-tertiary-container: #00fbfb;
  --mat-sys-tertiary-fixed: #00fbfb;
  --mat-sys-tertiary-fixed-dim: #00dddd;
  --mat-sys-neutral-variant20: #303038;
  --mat-sys-neutral10: #1b1b1f;
}
html {
  --mat-sys-level0:
    0px 0px 0px 0px rgba(0, 0, 0, 0.2),
    0px 0px 0px 0px rgba(0, 0, 0, 0.14),
    0px 0px 0px 0px rgba(0, 0, 0, 0.12);
}
html {
  --mat-sys-level1:
    0px 2px 1px -1px rgba(0, 0, 0, 0.2),
    0px 1px 1px 0px rgba(0, 0, 0, 0.14),
    0px 1px 3px 0px rgba(0, 0, 0, 0.12);
}
html {
  --mat-sys-level2:
    0px 3px 3px -2px rgba(0, 0, 0, 0.2),
    0px 3px 4px 0px rgba(0, 0, 0, 0.14),
    0px 1px 8px 0px rgba(0, 0, 0, 0.12);
}
html {
  --mat-sys-level3:
    0px 3px 5px -1px rgba(0, 0, 0, 0.2),
    0px 6px 10px 0px rgba(0, 0, 0, 0.14),
    0px 1px 18px 0px rgba(0, 0, 0, 0.12);
}
html {
  --mat-sys-level4:
    0px 5px 5px -3px rgba(0, 0, 0, 0.2),
    0px 8px 10px 1px rgba(0, 0, 0, 0.14),
    0px 3px 14px 2px rgba(0, 0, 0, 0.12);
}
html {
  --mat-sys-level5:
    0px 7px 8px -4px rgba(0, 0, 0, 0.2),
    0px 12px 17px 2px rgba(0, 0, 0, 0.14),
    0px 5px 22px 4px rgba(0, 0, 0, 0.12);
}
html {
  --mat-sys-body-large: 400 1rem / 1.5rem Urbane;
  --mat-sys-body-large-font: Urbane;
  --mat-sys-body-large-line-height: 1.5rem;
  --mat-sys-body-large-size: 1rem;
  --mat-sys-body-large-tracking: 0.031rem;
  --mat-sys-body-large-weight: 400;
  --mat-sys-body-medium: 400 0.875rem / 1.25rem Urbane;
  --mat-sys-body-medium-font: Urbane;
  --mat-sys-body-medium-line-height: 1.25rem;
  --mat-sys-body-medium-size: 0.875rem;
  --mat-sys-body-medium-tracking: 0.016rem;
  --mat-sys-body-medium-weight: 400;
  --mat-sys-body-small: 400 0.75rem / 1rem Urbane;
  --mat-sys-body-small-font: Urbane;
  --mat-sys-body-small-line-height: 1rem;
  --mat-sys-body-small-size: 0.75rem;
  --mat-sys-body-small-tracking: 0.025rem;
  --mat-sys-body-small-weight: 400;
  --mat-sys-display-large: 400 3.562rem / 4rem Urbane;
  --mat-sys-display-large-font: Urbane;
  --mat-sys-display-large-line-height: 4rem;
  --mat-sys-display-large-size: 3.562rem;
  --mat-sys-display-large-tracking: -0.016rem;
  --mat-sys-display-large-weight: 400;
  --mat-sys-display-medium: 400 2.812rem / 3.25rem Urbane;
  --mat-sys-display-medium-font: Urbane;
  --mat-sys-display-medium-line-height: 3.25rem;
  --mat-sys-display-medium-size: 2.812rem;
  --mat-sys-display-medium-tracking: 0;
  --mat-sys-display-medium-weight: 400;
  --mat-sys-display-small: 400 2.25rem / 2.75rem Urbane;
  --mat-sys-display-small-font: Urbane;
  --mat-sys-display-small-line-height: 2.75rem;
  --mat-sys-display-small-size: 2.25rem;
  --mat-sys-display-small-tracking: 0;
  --mat-sys-display-small-weight: 400;
  --mat-sys-headline-large: 400 2rem / 2.5rem Urbane;
  --mat-sys-headline-large-font: Urbane;
  --mat-sys-headline-large-line-height: 2.5rem;
  --mat-sys-headline-large-size: 2rem;
  --mat-sys-headline-large-tracking: 0;
  --mat-sys-headline-large-weight: 400;
  --mat-sys-headline-medium: 400 1.75rem / 2.25rem Urbane;
  --mat-sys-headline-medium-font: Urbane;
  --mat-sys-headline-medium-line-height: 2.25rem;
  --mat-sys-headline-medium-size: 1.75rem;
  --mat-sys-headline-medium-tracking: 0;
  --mat-sys-headline-medium-weight: 400;
  --mat-sys-headline-small: 400 1.5rem / 2rem Urbane;
  --mat-sys-headline-small-font: Urbane;
  --mat-sys-headline-small-line-height: 2rem;
  --mat-sys-headline-small-size: 1.5rem;
  --mat-sys-headline-small-tracking: 0;
  --mat-sys-headline-small-weight: 400;
  --mat-sys-label-large: 500 0.875rem / 1.25rem Urbane;
  --mat-sys-label-large-font: Urbane;
  --mat-sys-label-large-line-height: 1.25rem;
  --mat-sys-label-large-size: 0.875rem;
  --mat-sys-label-large-tracking: 0.006rem;
  --mat-sys-label-large-weight: 500;
  --mat-sys-label-large-weight-prominent: 700;
  --mat-sys-label-medium: 500 0.75rem / 1rem Urbane;
  --mat-sys-label-medium-font: Urbane;
  --mat-sys-label-medium-line-height: 1rem;
  --mat-sys-label-medium-size: 0.75rem;
  --mat-sys-label-medium-tracking: 0.031rem;
  --mat-sys-label-medium-weight: 500;
  --mat-sys-label-medium-weight-prominent: 700;
  --mat-sys-label-small: 500 0.688rem / 1rem Urbane;
  --mat-sys-label-small-font: Urbane;
  --mat-sys-label-small-line-height: 1rem;
  --mat-sys-label-small-size: 0.688rem;
  --mat-sys-label-small-tracking: 0.031rem;
  --mat-sys-label-small-weight: 500;
  --mat-sys-title-large: 400 1.375rem / 1.75rem Urbane;
  --mat-sys-title-large-font: Urbane;
  --mat-sys-title-large-line-height: 1.75rem;
  --mat-sys-title-large-size: 1.375rem;
  --mat-sys-title-large-tracking: 0;
  --mat-sys-title-large-weight: 400;
  --mat-sys-title-medium: 500 1rem / 1.5rem Urbane;
  --mat-sys-title-medium-font: Urbane;
  --mat-sys-title-medium-line-height: 1.5rem;
  --mat-sys-title-medium-size: 1rem;
  --mat-sys-title-medium-tracking: 0.009rem;
  --mat-sys-title-medium-weight: 500;
  --mat-sys-title-small: 500 0.875rem / 1.25rem Urbane;
  --mat-sys-title-small-font: Urbane;
  --mat-sys-title-small-line-height: 1.25rem;
  --mat-sys-title-small-size: 0.875rem;
  --mat-sys-title-small-tracking: 0.006rem;
  --mat-sys-title-small-weight: 500;
}
html {
  --mat-sys-corner-extra-large: 28px;
  --mat-sys-corner-extra-large-top: 28px 28px 0 0;
  --mat-sys-corner-extra-small: 4px;
  --mat-sys-corner-extra-small-top: 4px 4px 0 0;
  --mat-sys-corner-full: 9999px;
  --mat-sys-corner-large: 16px;
  --mat-sys-corner-large-end: 0 16px 16px 0;
  --mat-sys-corner-large-start: 16px 0 0 16px;
  --mat-sys-corner-large-top: 16px 16px 0 0;
  --mat-sys-corner-medium: 12px;
  --mat-sys-corner-none: 0;
  --mat-sys-corner-small: 8px;
}
html {
  --mat-sys-dragged-state-layer-opacity: 0.16;
  --mat-sys-focus-state-layer-opacity: 0.12;
  --mat-sys-hover-state-layer-opacity: 0.08;
  --mat-sys-pressed-state-layer-opacity: 0.12;
}
body {
  background: var(--mat-sys-surface);
  color: var(--mat-sys-on-surface);
}
html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
  background-color: #F9FBFC;
}
body {
  font-family: "Urbane", sans-serif;
  color: #17181A;
}
.d-flex {
  display: flex;
}
.d-inline-flex {
  display: inline-flex;
}
.flex-row {
  flex-direction: row;
}
.flex-column {
  flex-direction: column;
}
.flex-wrap {
  flex-wrap: wrap;
}
.flex-nowrap {
  flex-wrap: nowrap;
}
.justify-content-start {
  justify-content: flex-start;
}
.justify-content-end {
  justify-content: flex-end;
}
.justify-content-center {
  justify-content: center;
}
.justify-content-between {
  justify-content: space-between;
}
.justify-content-around {
  justify-content: space-around;
}
.align-items-start {
  align-items: flex-start;
}
.align-items-end {
  align-items: flex-end;
}
.align-items-center {
  align-items: center;
}
.align-items-baseline {
  align-items: baseline;
}
.align-items-stretch {
  align-items: stretch;
}
.align-self-start {
  align-self: flex-start;
}
.align-self-end {
  align-self: flex-end;
}
.align-self-center {
  align-self: center;
}
.align-self-baseline {
  align-self: baseline;
}
.align-self-stretch {
  align-self: stretch;
}
.flex-grow-0 {
  flex-grow: 0;
}
.flex-grow-1 {
  flex-grow: 1;
}
.flex-shrink-0 {
  flex-shrink: 0;
}
.flex-shrink-1 {
  flex-shrink: 1;
}
.m-0 {
  margin: 0;
}
.mt-0 {
  margin-top: 0;
}
.mr-0 {
  margin-right: 0;
}
.mb-0 {
  margin-bottom: 0;
}
.ml-0 {
  margin-left: 0;
}
.mx-0 {
  margin-left: 0;
  margin-right: 0;
}
.my-0 {
  margin-top: 0;
  margin-bottom: 0;
}
.m-1 {
  margin: 4px;
}
.mt-1 {
  margin-top: 4px;
}
.mr-1 {
  margin-right: 4px;
}
.mb-1 {
  margin-bottom: 4px;
}
.ml-1 {
  margin-left: 4px;
}
.mx-1 {
  margin-left: 4px;
  margin-right: 4px;
}
.my-1 {
  margin-top: 4px;
  margin-bottom: 4px;
}
.m-2 {
  margin: 8px;
}
.mt-2 {
  margin-top: 8px;
}
.mr-2 {
  margin-right: 8px;
}
.mb-2 {
  margin-bottom: 8px;
}
.ml-2 {
  margin-left: 8px;
}
.mx-2 {
  margin-left: 8px;
  margin-right: 8px;
}
.my-2 {
  margin-top: 8px;
  margin-bottom: 8px;
}
.m-3 {
  margin: 12px;
}
.mt-3 {
  margin-top: 12px;
}
.mr-3 {
  margin-right: 12px;
}
.mb-3 {
  margin-bottom: 12px;
}
.ml-3 {
  margin-left: 12px;
}
.mx-3 {
  margin-left: 12px;
  margin-right: 12px;
}
.my-3 {
  margin-top: 12px;
  margin-bottom: 12px;
}
.m-4 {
  margin: 16px;
}
.mt-4 {
  margin-top: 16px;
}
.mr-4 {
  margin-right: 16px;
}
.mb-4 {
  margin-bottom: 16px;
}
.ml-4 {
  margin-left: 16px;
}
.mx-4 {
  margin-left: 16px;
  margin-right: 16px;
}
.my-4 {
  margin-top: 16px;
  margin-bottom: 16px;
}
.m-5 {
  margin: 20px;
}
.mt-5 {
  margin-top: 20px;
}
.mr-5 {
  margin-right: 20px;
}
.mb-5 {
  margin-bottom: 20px;
}
.ml-5 {
  margin-left: 20px;
}
.mx-5 {
  margin-left: 20px;
  margin-right: 20px;
}
.my-5 {
  margin-top: 20px;
  margin-bottom: 20px;
}
.p-0 {
  padding: 0;
}
.pt-0 {
  padding-top: 0;
}
.pr-0 {
  padding-right: 0;
}
.pb-0 {
  padding-bottom: 0;
}
.pl-0 {
  padding-left: 0;
}
.px-0 {
  padding-left: 0;
  padding-right: 0;
}
.py-0 {
  padding-top: 0;
  padding-bottom: 0;
}
.p-1 {
  padding: 4px;
}
.pt-1 {
  padding-top: 4px;
}
.pr-1 {
  padding-right: 4px;
}
.pb-1 {
  padding-bottom: 4px;
}
.pl-1 {
  padding-left: 4px;
}
.px-1 {
  padding-left: 4px;
  padding-right: 4px;
}
.py-1 {
  padding-top: 4px;
  padding-bottom: 4px;
}
.p-2 {
  padding: 8px;
}
.pt-2 {
  padding-top: 8px;
}
.pr-2 {
  padding-right: 8px;
}
.pb-2 {
  padding-bottom: 8px;
}
.pl-2 {
  padding-left: 8px;
}
.px-2 {
  padding-left: 8px;
  padding-right: 8px;
}
.py-2 {
  padding-top: 8px;
  padding-bottom: 8px;
}
.p-3 {
  padding: 12px;
}
.pt-3 {
  padding-top: 12px;
}
.pr-3 {
  padding-right: 12px;
}
.pb-3 {
  padding-bottom: 12px;
}
.pl-3 {
  padding-left: 12px;
}
.px-3 {
  padding-left: 12px;
  padding-right: 12px;
}
.py-3 {
  padding-top: 12px;
  padding-bottom: 12px;
}
.p-4 {
  padding: 16px;
}
.pt-4 {
  padding-top: 16px;
}
.pr-4 {
  padding-right: 16px;
}
.pb-4 {
  padding-bottom: 16px;
}
.pl-4 {
  padding-left: 16px;
}
.px-4 {
  padding-left: 16px;
  padding-right: 16px;
}
.py-4 {
  padding-top: 16px;
  padding-bottom: 16px;
}
.p-5 {
  padding: 20px;
}
.pt-5 {
  padding-top: 20px;
}
.pr-5 {
  padding-right: 20px;
}
.pb-5 {
  padding-bottom: 20px;
}
.pl-5 {
  padding-left: 20px;
}
.px-5 {
  padding-left: 20px;
  padding-right: 20px;
}
.py-5 {
  padding-top: 20px;
  padding-bottom: 20px;
}
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.text-primary {
  color: #3870B8;
}
.text-link {
  color: #0071BC;
}
.text-black {
  color: #17181A;
}
.text-gray {
  color: #547996;
}
.text-white {
  color: #FFFFFF;
}
.bg-white {
  background-color: #FFFFFF;
}
.bg-light {
  background-color: #F9FBFC;
}
.bg-primary {
  background-color: #3870B8;
}
.bg-primary-light {
  background-color: rgba(56, 112, 184, 0.2);
}
.border {
  border: 1px solid #D9E1E7;
}
.border-top {
  border-top: 1px solid #D9E1E7;
}
.border-right {
  border-right: 1px solid #D9E1E7;
}
.border-bottom {
  border-bottom: 1px solid #D9E1E7;
}
.border-left {
  border-left: 1px solid #D9E1E7;
}
.rounded-sm {
  border-radius: 5px;
}
.rounded {
  border-radius: 6px;
}
.rounded-lg {
  border-radius: 8px;
}
.rounded-xl {
  border-radius: 10px;
}
.rounded-circle {
  border-radius: 50%;
}
.w-100 {
  width: 100%;
}
.h-100 {
  height: 100%;
}
.card {
  background: #FFFFFF;
  border-radius: 8px;
  outline: 1px #F1F5F7 solid;
  outline-offset: -1px;
  padding: 20px;
  margin-bottom: 20px;
}
.button-primary {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  outline: none;
  transition: background-color 0.3s ease;
  background: #3870B8;
  color: #FFFFFF;
  padding: 8px 16px;
}
.button-primary:hover {
  background: #468CE7;
}
.button-primary:active {
  background: #285082;
}
.button-primary:disabled {
  background: #BFD0EE;
  cursor: not-allowed;
}
.button-secondary {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  outline: none;
  transition: background-color 0.3s ease;
  background: #FFFFFF;
  outline: 1px #D9E1E7 solid;
  outline-offset: -1px;
  color: #17181A;
  padding: 8px 12px;
}
.button-secondary:hover {
  background: #F1F5F7;
}
.button-secondary:active {
  background: #17181A;
  color: #FFFFFF;
}
.input-field {
  padding: 12px;
  border-radius: 10px;
  outline: 1px #D9E1E7 solid;
  outline-offset: -1px;
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  width: 100%;
}
.input-field:focus {
  outline-color: #547996;
}
.textarea-field {
  padding: 12px;
  border-radius: 10px;
  outline: 1px #D9E1E7 solid;
  outline-offset: -1px;
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  width: 100%;
  min-height: 88px;
}
.textarea-field:focus {
  outline-color: #547996;
}
.status-badge-success {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
  border-radius: 90px;
  background-color: rgba(26, 213, 152, 0.1);
  color: #1AD598;
  font-size: 11px;
  font-weight: 300;
  outline: 1px rgba(26, 213, 152, 0.4) solid;
  outline-offset: -1px;
}

/* angular:styles/global:styles */
/*# sourceMappingURL=styles.css.map */

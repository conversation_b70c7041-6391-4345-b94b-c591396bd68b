import {
  CommonModule,
  Component,
  EventEmitter,
  Input,
  NgClass,
  NgForOf,
  NgIf,
  Output,
  Router,
  RouterModule,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassMap,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementContainerEnd,
  ɵɵelementContainerStart,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnamespaceSVG,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-F5HTA5WY.js";

// src/app/core/data/models/chart-data.models.ts
var DEFAULT_TABLE_COLUMNS = [
  { field: "memberId", header: "Member ID", width: "140px", sortable: true },
  { field: "firstName", header: "First name", width: "107px", sortable: true },
  { field: "lastName", header: "Last name", width: "106px", sortable: true },
  { field: "middleName", header: "Middle name", width: "102px", sortable: true },
  { field: "dob", header: "DOB", width: "99px", sortable: true },
  { field: "lob", header: "LOB", width: "100px", sortable: true, filterable: true },
  { field: "measure", header: "Measure", width: "93px", sortable: true, filterable: true },
  { field: "review1", header: "Review 1", width: "140px", sortable: true },
  { field: "review2", header: "Review 2", width: "140px", sortable: true },
  { field: "assigned", header: "Assigned", width: "170px", sortable: true },
  { field: "status", header: "Status", width: "118px", sortable: true, filterable: true }
];

// src/app/shared/components/buttons/submit-button.component.ts
var SubmitButtonComponent = class _SubmitButtonComponent {
  property1 = "Default";
  click = new EventEmitter();
  onClick(event) {
    if (this.property1 !== "Inactive") {
      this.click.emit(event);
    }
  }
  getButtonClass() {
    return this.property1 === "Default" ? "submit-button-default" : "submit-button-inactive";
  }
  getButtonText() {
    return this.property1 === "Default" ? "Review" : "Inactive";
  }
  static \u0275fac = function SubmitButtonComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _SubmitButtonComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _SubmitButtonComponent, selectors: [["app-sumbit-button"]], inputs: { property1: "property1" }, outputs: { click: "click" }, decls: 2, vars: 4, consts: [[3, "click", "disabled"]], template: function SubmitButtonComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "button", 0);
      \u0275\u0275listener("click", function SubmitButtonComponent_Template_button_click_0_listener($event) {
        return ctx.onClick($event);
      });
      \u0275\u0275text(1);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275classMap(ctx.getButtonClass());
      \u0275\u0275property("disabled", ctx.property1 === "Inactive");
      \u0275\u0275advance();
      \u0275\u0275textInterpolate1(" ", ctx.getButtonText(), " ");
    }
  }, dependencies: [CommonModule], styles: ["\n\n.submit-button-default[_ngcontent-%COMP%] {\n  padding: 8px 16px;\n  border-radius: 8px;\n  font-size: 12px;\n  font-family: Urbane;\n  font-weight: 500;\n  line-height: 20px;\n  text-align: center;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  min-width: 80px;\n  transition: all 0.2s ease;\n  background-color: #1976d2;\n  color: #ffffff;\n  border: none;\n  cursor: pointer;\n}\n.submit-button-default[_ngcontent-%COMP%]:hover {\n  background-color: #1565c0;\n  transform: translateY(-1px);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n.submit-button-default[_ngcontent-%COMP%]:active {\n  background-color: #0d47a1;\n  transform: translateY(0);\n}\n.submit-button-inactive[_ngcontent-%COMP%] {\n  padding: 8px 16px;\n  border-radius: 8px;\n  font-size: 12px;\n  font-family: Urbane;\n  font-weight: 500;\n  line-height: 20px;\n  text-align: center;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  min-width: 80px;\n  background-color: #BFD0EE;\n  color: #ffffff;\n  border: none;\n  cursor: not-allowed;\n  opacity: 0.8;\n}\n.submit-button-inactive[_ngcontent-%COMP%]:hover {\n  background-color: #BFD0EE;\n  transform: none;\n  box-shadow: none;\n}\n/*# sourceMappingURL=submit-button.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(SubmitButtonComponent, [{
    type: Component,
    args: [{ selector: "app-sumbit-button", standalone: true, imports: [CommonModule], template: `
    <button 
      [class]="getButtonClass()"
      [disabled]="property1 === 'Inactive'"
      (click)="onClick($event)">
      {{ getButtonText() }}
    </button>
  `, styles: ["/* src/app/shared/components/buttons/submit-button.component.scss */\n.submit-button-default {\n  padding: 8px 16px;\n  border-radius: 8px;\n  font-size: 12px;\n  font-family: Urbane;\n  font-weight: 500;\n  line-height: 20px;\n  text-align: center;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  min-width: 80px;\n  transition: all 0.2s ease;\n  background-color: #1976d2;\n  color: #ffffff;\n  border: none;\n  cursor: pointer;\n}\n.submit-button-default:hover {\n  background-color: #1565c0;\n  transform: translateY(-1px);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n.submit-button-default:active {\n  background-color: #0d47a1;\n  transform: translateY(0);\n}\n.submit-button-inactive {\n  padding: 8px 16px;\n  border-radius: 8px;\n  font-size: 12px;\n  font-family: Urbane;\n  font-weight: 500;\n  line-height: 20px;\n  text-align: center;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  min-width: 80px;\n  background-color: #BFD0EE;\n  color: #ffffff;\n  border: none;\n  cursor: not-allowed;\n  opacity: 0.8;\n}\n.submit-button-inactive:hover {\n  background-color: #BFD0EE;\n  transform: none;\n  box-shadow: none;\n}\n/*# sourceMappingURL=submit-button.component.css.map */\n"] }]
  }], null, { property1: [{
    type: Input
  }], click: [{
    type: Output
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(SubmitButtonComponent, { className: "SubmitButtonComponent", filePath: "src/app/shared/components/buttons/submit-button.component.ts", lineNumber: 18 });
})();

// src/app/features/dashboard/components/assigned-table/assigned-table.component.ts
function AssignedTableComponent_ng_container_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275elementStart(1, "div", 50)(2, "span", 51);
    \u0275\u0275text(3);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const chart_r1 = ctx.$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(chart_r1.memberId);
  }
}
function AssignedTableComponent_ng_container_15_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275elementStart(1, "div", 52)(2, "div", 53)(3, "span", 54);
    \u0275\u0275text(4);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const chart_r2 = ctx.$implicit;
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(chart_r2.firstName);
  }
}
function AssignedTableComponent_ng_container_21_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275elementStart(1, "div", 55)(2, "div", 56)(3, "span", 57);
    \u0275\u0275text(4);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const chart_r3 = ctx.$implicit;
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(chart_r3.lastName);
  }
}
function AssignedTableComponent_ng_container_27_span_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 61);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const chart_r4 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(chart_r4.middleName);
  }
}
function AssignedTableComponent_ng_container_27_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275elementStart(1, "div", 58)(2, "div", 59);
    \u0275\u0275template(3, AssignedTableComponent_ng_container_27_span_3_Template, 2, 1, "span", 60);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const chart_r4 = ctx.$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275property("ngIf", chart_r4.middleName);
  }
}
function AssignedTableComponent_ng_container_33_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275elementStart(1, "div", 62)(2, "div", 63)(3, "span", 64);
    \u0275\u0275text(4);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const chart_r5 = ctx.$implicit;
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(chart_r5.dob);
  }
}
function AssignedTableComponent_ng_container_39_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275elementStart(1, "div", 65)(2, "div", 66)(3, "span", 67);
    \u0275\u0275text(4);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const chart_r6 = ctx.$implicit;
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(chart_r6.lob);
  }
}
function AssignedTableComponent_ng_container_45_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275elementStart(1, "div", 68)(2, "div", 69)(3, "span", 70);
    \u0275\u0275text(4);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const chart_r7 = ctx.$implicit;
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(chart_r7.measure);
  }
}
function AssignedTableComponent_ng_container_51_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275elementStart(1, "div", 71)(2, "span", 72);
    \u0275\u0275text(3);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const chart_r8 = ctx.$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(chart_r8.review1);
  }
}
function AssignedTableComponent_ng_container_57_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275elementStart(1, "div", 73)(2, "span", 74);
    \u0275\u0275text(3);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const chart_r9 = ctx.$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(chart_r9.review2);
  }
}
function AssignedTableComponent_ng_container_63_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275elementStart(1, "div", 75)(2, "span", 76);
    \u0275\u0275text(3);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const chart_r10 = ctx.$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(chart_r10.assigned);
  }
}
function AssignedTableComponent_ng_container_69_Template(rf, ctx) {
  if (rf & 1) {
    const _r11 = \u0275\u0275getCurrentView();
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275elementStart(1, "div", 77)(2, "app-sumbit-button", 78);
    \u0275\u0275listener("click", function AssignedTableComponent_ng_container_69_Template_app_sumbit_button_click_2_listener() {
      const chart_r12 = \u0275\u0275restoreView(_r11).$implicit;
      const ctx_r12 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r12.navigateToChartReview(chart_r12));
    });
    \u0275\u0275elementEnd()();
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const chart_r12 = ctx.$implicit;
    \u0275\u0275advance(2);
    \u0275\u0275property("property1", chart_r12.status === "Review" ? "Default" : "Inactive");
  }
}
function AssignedTableComponent_div_70_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 79);
    \u0275\u0275text(1, " No charts found matching your criteria. ");
    \u0275\u0275elementEnd();
  }
}
var AssignedTableComponent = class _AssignedTableComponent {
  router;
  charts = [];
  searchText = "";
  // Column definitions matching the Figma specifications
  columns = DEFAULT_TABLE_COLUMNS;
  constructor(router) {
    this.router = router;
  }
  // Method to get field value safely
  getFieldValue(chart, field) {
    return chart[field] || "";
  }
  // Method to navigate to chart review page
  navigateToChartReview(chart) {
    if (chart && chart.status && chart.status.toLowerCase() === "review" && chart.memberId) {
      this.router.navigate(["/chart-review", chart.memberId]);
    } else if (chart && chart.status && chart.status.toLowerCase() === "review" && !chart.memberId) {
      console.error("Chart data is missing memberId for navigation but status is review", chart);
    }
  }
  // Method to filter charts based on search text
  get filteredCharts() {
    if (!this.searchText) {
      return this.charts;
    }
    const searchLower = this.searchText.toLowerCase();
    return this.charts.filter((chart) => {
      return Object.values(chart).some((value) => value && typeof value === "string" ? value.toLowerCase().includes(searchLower) : String(value).toLowerCase().includes(searchLower));
    });
  }
  // Method to get status class based on status value
  getStatusClass(status) {
    switch (status.toLowerCase()) {
      case "review":
        return "status-review";
      case "complete":
        return "status-complete";
      case "inactive":
        return "status-inactive";
      default:
        return "";
    }
  }
  static \u0275fac = function AssignedTableComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _AssignedTableComponent)(\u0275\u0275directiveInject(Router));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _AssignedTableComponent, selectors: [["app-assigned-table"]], inputs: { charts: "charts", searchText: "searchText" }, decls: 71, vars: 12, consts: [[1, "assigned-table_1134-1191"], [1, "table_1134-1192"], [1, "table_1134-1208"], [1, "columns_1134-1209"], [1, "column_1134-1210"], [1, "header-item_1134-1211"], [1, "table-item_1134-1212"], [1, "text-label_1134-1214"], [4, "ngFor", "ngForOf"], [1, "column_1235-930"], [1, "header-item_1235-931"], [1, "table-item_1235-932"], [1, "text-label_1235-934"], [1, "column_1134-1235"], [1, "header-item_1134-1236"], [1, "table-item_1134-1237"], [1, "text-label_1134-1239"], [1, "column_1235-969"], [1, "header-item_1235-970"], [1, "table-item_1235-971"], [1, "text-label_1235-973"], [1, "column_1134-1270"], [1, "header-item_1134-1271"], [1, "table-item_1134-1272"], [1, "text-label_1134-1274"], [1, "column_1134-1305"], [1, "header-item_1134-1306"], [1, "table-item_1134-1307"], [1, "text-label_1134-1309"], [1, "column_1134-1340"], [1, "header-item_1134-1341"], [1, "table-item_1134-1342"], [1, "text-label_1134-1344"], [1, "column_1134-1375"], [1, "header-item_1134-1376"], [1, "table-item_1134-1377"], [1, "text-label_1134-1379"], [1, "column_1134-1400"], [1, "header-item_1134-1401"], [1, "table-item_1134-1402"], [1, "text-label_1134-1404"], [1, "column_1134-1425"], [1, "header-item_1134-1426"], [1, "table-item_1134-1427"], [1, "text-label_1134-1429"], [1, "column_1134-1450"], [1, "header-item_1134-1451"], [1, "table-item_1134-1452"], [1, "text-label_1134-1454"], ["class", "no-charts-message", 4, "ngIf"], [1, "table-item_1134-1215"], [1, "text-label_1134-1216"], [1, "table-item_1235-939"], [1, "icon-text_1235-940"], [1, "text-label_1235-941"], [1, "table-item_1134-1240"], [1, "icon-text_1134-1241"], [1, "text-label_1134-1242"], [1, "table-item_1235-974"], [1, "icon-text_1235-975"], ["class", "text-label_1235-979", 4, "ngIf"], [1, "text-label_1235-979"], [1, "table-item_1134-1275"], [1, "icon-text_1134-1276"], [1, "text-label_1134-1277"], [1, "table-item_1134-1310"], [1, "icon-text_1134-1311"], [1, "text-label_1134-1312"], [1, "table-item_1134-1345"], [1, "icon-text_1134-1346"], [1, "text-label_1134-1347"], [1, "table-item_1134-1380"], [1, "text-label_1134-1381"], [1, "table-item_1134-1405"], [1, "text-label_1134-1406"], [1, "table-item_1134-1430"], [1, "text-label_1134-1431"], [1, "table-item_1134-1455"], [1, "sumbit-button_1134-1462", 3, "click", "property1"], [1, "no-charts-message"]], template: function AssignedTableComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "div", 3)(4, "div", 4)(5, "div", 5)(6, "div", 6)(7, "span", 7);
      \u0275\u0275text(8, "Member ID");
      \u0275\u0275elementEnd()()();
      \u0275\u0275template(9, AssignedTableComponent_ng_container_9_Template, 4, 1, "ng-container", 8);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(10, "div", 9)(11, "div", 10)(12, "div", 11)(13, "span", 12);
      \u0275\u0275text(14, "First name");
      \u0275\u0275elementEnd()()();
      \u0275\u0275template(15, AssignedTableComponent_ng_container_15_Template, 5, 1, "ng-container", 8);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(16, "div", 13)(17, "div", 14)(18, "div", 15)(19, "span", 16);
      \u0275\u0275text(20, "Last name");
      \u0275\u0275elementEnd()()();
      \u0275\u0275template(21, AssignedTableComponent_ng_container_21_Template, 5, 1, "ng-container", 8);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(22, "div", 17)(23, "div", 18)(24, "div", 19)(25, "span", 20);
      \u0275\u0275text(26, "Middle name");
      \u0275\u0275elementEnd()()();
      \u0275\u0275template(27, AssignedTableComponent_ng_container_27_Template, 4, 1, "ng-container", 8);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(28, "div", 21)(29, "div", 22)(30, "div", 23)(31, "span", 24);
      \u0275\u0275text(32, "DOB");
      \u0275\u0275elementEnd()()();
      \u0275\u0275template(33, AssignedTableComponent_ng_container_33_Template, 5, 1, "ng-container", 8);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(34, "div", 25)(35, "div", 26)(36, "div", 27)(37, "span", 28);
      \u0275\u0275text(38, "LOB");
      \u0275\u0275elementEnd()()();
      \u0275\u0275template(39, AssignedTableComponent_ng_container_39_Template, 5, 1, "ng-container", 8);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(40, "div", 29)(41, "div", 30)(42, "div", 31)(43, "span", 32);
      \u0275\u0275text(44, "Measure");
      \u0275\u0275elementEnd()()();
      \u0275\u0275template(45, AssignedTableComponent_ng_container_45_Template, 5, 1, "ng-container", 8);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(46, "div", 33)(47, "div", 34)(48, "div", 35)(49, "span", 36);
      \u0275\u0275text(50, "Review 1");
      \u0275\u0275elementEnd()()();
      \u0275\u0275template(51, AssignedTableComponent_ng_container_51_Template, 4, 1, "ng-container", 8);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(52, "div", 37)(53, "div", 38)(54, "div", 39)(55, "span", 40);
      \u0275\u0275text(56, "Review 2");
      \u0275\u0275elementEnd()()();
      \u0275\u0275template(57, AssignedTableComponent_ng_container_57_Template, 4, 1, "ng-container", 8);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(58, "div", 41)(59, "div", 42)(60, "div", 43)(61, "span", 44);
      \u0275\u0275text(62, "Assigned");
      \u0275\u0275elementEnd()()();
      \u0275\u0275template(63, AssignedTableComponent_ng_container_63_Template, 4, 1, "ng-container", 8);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(64, "div", 45)(65, "div", 46)(66, "div", 47)(67, "span", 48);
      \u0275\u0275text(68, "Status");
      \u0275\u0275elementEnd()()();
      \u0275\u0275template(69, AssignedTableComponent_ng_container_69_Template, 3, 1, "ng-container", 8);
      \u0275\u0275elementEnd()()();
      \u0275\u0275template(70, AssignedTableComponent_div_70_Template, 2, 0, "div", 49);
      \u0275\u0275elementEnd()();
    }
    if (rf & 2) {
      \u0275\u0275advance(9);
      \u0275\u0275property("ngForOf", ctx.filteredCharts);
      \u0275\u0275advance(6);
      \u0275\u0275property("ngForOf", ctx.filteredCharts);
      \u0275\u0275advance(6);
      \u0275\u0275property("ngForOf", ctx.filteredCharts);
      \u0275\u0275advance(6);
      \u0275\u0275property("ngForOf", ctx.filteredCharts);
      \u0275\u0275advance(6);
      \u0275\u0275property("ngForOf", ctx.filteredCharts);
      \u0275\u0275advance(6);
      \u0275\u0275property("ngForOf", ctx.filteredCharts);
      \u0275\u0275advance(6);
      \u0275\u0275property("ngForOf", ctx.filteredCharts);
      \u0275\u0275advance(6);
      \u0275\u0275property("ngForOf", ctx.filteredCharts);
      \u0275\u0275advance(6);
      \u0275\u0275property("ngForOf", ctx.filteredCharts);
      \u0275\u0275advance(6);
      \u0275\u0275property("ngForOf", ctx.filteredCharts);
      \u0275\u0275advance(6);
      \u0275\u0275property("ngForOf", ctx.filteredCharts);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.filteredCharts.length === 0);
    }
  }, dependencies: [CommonModule, NgForOf, NgIf, RouterModule, SubmitButtonComponent], styles: ['\n\n.assigned-table_1134-1191[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 20px;\n  box-sizing: border-box;\n  width: 1380px;\n}\n.table_1134-1192[_ngcontent-%COMP%] {\n  padding: 20px;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 0px;\n  box-sizing: border-box;\n  border-radius: 8px;\n  border-color: #f1f5f7;\n  border-style: solid;\n  border-width: 1px;\n  background: #ffffff;\n  width: 100%;\n}\n.table_1134-1208[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 0px;\n  box-sizing: border-box;\n  width: 100%;\n}\n.columns_1134-1209[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 0px;\n  box-sizing: border-box;\n  width: 100%;\n}\n.column_1134-1210[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 0px;\n  box-sizing: border-box;\n  width: 100%;\n}\n.header-item_1134-1211[_ngcontent-%COMP%] {\n  padding: 0px 12px 0px 12px;\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 10px;\n  box-sizing: border-box;\n  border-color: #f1f5f7;\n  border-style: solid;\n  border-bottom-width: 1px;\n  width: 100%;\n}\n.table-item_1134-1212[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  height: 68px;\n}\n.text-label_1134-1214[_ngcontent-%COMP%] {\n  color: #17181a;\n  font-size: 12px;\n  font-family: Urbane;\n  line-height: 20px;\n  letter-spacing: 0%;\n  text-decoration: none;\n  font-weight: 500;\n  text-align: left;\n  text-wrap: nowrap;\n}\n.table-item_1134-1215[_ngcontent-%COMP%] {\n  padding: 13px 12px 13px 12px;\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  height: 68px;\n}\n.text-label_1134-1216[_ngcontent-%COMP%] {\n  color: #17181a;\n  font-size: 12px;\n  font-family: Urbane;\n  line-height: 20px;\n  letter-spacing: 0%;\n  text-decoration: none;\n  font-weight: 300;\n  text-align: left;\n  text-wrap: nowrap;\n}\n.column_1235-930[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 0px;\n  box-sizing: border-box;\n  width: 107px;\n}\n.header-item_1235-931[_ngcontent-%COMP%] {\n  padding: 0px 12px 0px 12px;\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 10px;\n  box-sizing: border-box;\n  border-color: #f1f5f7;\n  border-style: solid;\n  border-bottom-width: 1px;\n  width: 100%;\n}\n.table-item_1235-932[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  height: 68px;\n}\n.text-label_1235-934[_ngcontent-%COMP%] {\n  color: #17181a;\n  font-size: 12px;\n  font-family: Urbane;\n  line-height: 20px;\n  letter-spacing: 0%;\n  text-decoration: none;\n  font-weight: 500;\n  text-align: left;\n  text-wrap: nowrap;\n}\n.table-item_1235-939[_ngcontent-%COMP%] {\n  padding: 10px 12px 10px 12px;\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  background: #ffffff;\n  height: 68px;\n}\n.icon-text_1235-940[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  background: #ffffff;\n}\n.text-label_1235-941[_ngcontent-%COMP%] {\n  color: #17181a;\n  font-size: 12px;\n  font-family: Urbane;\n  line-height: 20px;\n  letter-spacing: 0%;\n  text-decoration: none;\n  font-weight: 300;\n  text-align: left;\n  text-wrap: nowrap;\n}\n.column_1134-1235[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 0px;\n  box-sizing: border-box;\n  width: 106px;\n}\n.header-item_1134-1236[_ngcontent-%COMP%] {\n  padding: 0px 12px 0px 12px;\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 10px;\n  box-sizing: border-box;\n  border-color: #f1f5f7;\n  border-style: solid;\n  border-bottom-width: 1px;\n  width: 100%;\n}\n.table-item_1134-1237[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  height: 68px;\n}\n.text-label_1134-1239[_ngcontent-%COMP%] {\n  color: #17181a;\n  font-size: 12px;\n  font-family: Urbane;\n  line-height: 20px;\n  letter-spacing: 0%;\n  text-decoration: none;\n  font-weight: 500;\n  text-align: left;\n  text-wrap: nowrap;\n}\n.table-item_1134-1240[_ngcontent-%COMP%] {\n  padding: 10px 12px 10px 12px;\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  background: #ffffff;\n  height: 68px;\n}\n.icon-text_1134-1241[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  background: #ffffff;\n}\n.text-label_1134-1242[_ngcontent-%COMP%] {\n  color: #17181a;\n  font-size: 12px;\n  font-family: Urbane;\n  line-height: 20px;\n  letter-spacing: 0%;\n  text-decoration: none;\n  font-weight: 300;\n  text-align: left;\n  text-wrap: nowrap;\n}\n.column_1235-969[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 0px;\n  box-sizing: border-box;\n  width: 102px;\n}\n.header-item_1235-970[_ngcontent-%COMP%] {\n  padding: 0px 12px 0px 12px;\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 10px;\n  box-sizing: border-box;\n  border-color: #f1f5f7;\n  border-style: solid;\n  border-bottom-width: 1px;\n  width: 100%;\n}\n.table-item_1235-971[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  height: 68px;\n}\n.text-label_1235-973[_ngcontent-%COMP%] {\n  color: #17181a;\n  font-size: 12px;\n  font-family: Urbane;\n  line-height: 20px;\n  letter-spacing: 0%;\n  text-decoration: none;\n  font-weight: 500;\n  text-align: left;\n  text-wrap: nowrap;\n}\n.table-item_1235-974[_ngcontent-%COMP%] {\n  padding: 10px 12px 10px 12px;\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  background: #ffffff;\n  height: 68px;\n}\n.icon-text_1235-975[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  background: #ffffff;\n}\n.text-label_1235-979[_ngcontent-%COMP%] {\n  color: #17181a;\n  font-size: 12px;\n  font-family: Urbane;\n  line-height: 20px;\n  letter-spacing: 0%;\n  text-decoration: none;\n  font-weight: 300;\n  text-align: left;\n  text-wrap: wrap;\n  width: 78px;\n}\n.column_1134-1270[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 0px;\n  box-sizing: border-box;\n  width: 99px;\n}\n.header-item_1134-1271[_ngcontent-%COMP%] {\n  padding: 0px 12px 0px 12px;\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 10px;\n  box-sizing: border-box;\n  border-color: #f1f5f7;\n  border-style: solid;\n  border-bottom-width: 1px;\n  width: 100%;\n}\n.table-item_1134-1272[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  height: 68px;\n}\n.text-label_1134-1274[_ngcontent-%COMP%] {\n  color: #17181a;\n  font-size: 12px;\n  font-family: Urbane;\n  line-height: 20px;\n  letter-spacing: 0%;\n  text-decoration: none;\n  font-weight: 500;\n  text-align: left;\n  text-wrap: nowrap;\n}\n.table-item_1134-1275[_ngcontent-%COMP%] {\n  padding: 10px 12px 10px 12px;\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  background: #ffffff;\n  height: 68px;\n}\n.icon-text_1134-1276[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  background: #ffffff;\n}\n.text-label_1134-1277[_ngcontent-%COMP%] {\n  color: #17181a;\n  font-size: 12px;\n  font-family: Urbane;\n  line-height: 20px;\n  letter-spacing: 0%;\n  text-decoration: none;\n  font-weight: 300;\n  text-align: left;\n  text-wrap: nowrap;\n}\n.no-charts-message[_ngcontent-%COMP%] {\n  padding: 40px 20px;\n  text-align: center;\n  font-size: 14px;\n  font-family: "Urbane", sans-serif;\n  color: #757575;\n  font-style: italic;\n  background: #ffffff;\n}\n/*# sourceMappingURL=assigned-table.component.css.map */'] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AssignedTableComponent, [{
    type: Component,
    args: [{ selector: "app-assigned-table", standalone: true, imports: [CommonModule, RouterModule, SubmitButtonComponent], template: `<div class="assigned-table_1134-1191">
  <div class="table_1134-1192">
    <div class="table_1134-1208">
      <div class="columns_1134-1209">
        <!-- Member ID Column -->
        <div class="column_1134-1210">
          <div class="header-item_1134-1211">
            <div class="table-item_1134-1212">
              <span class="text-label_1134-1214">Member ID</span>
            </div>
          </div>
          <ng-container *ngFor="let chart of filteredCharts">
            <div class="table-item_1134-1215">
              <span class="text-label_1134-1216">{{ chart.memberId }}</span>
            </div>
          </ng-container>
        </div>

        <!-- First Name Column -->
        <div class="column_1235-930">
          <div class="header-item_1235-931">
            <div class="table-item_1235-932">
              <span class="text-label_1235-934">First name</span>
            </div>
          </div>
          <ng-container *ngFor="let chart of filteredCharts">
            <div class="table-item_1235-939">
              <div class="icon-text_1235-940">
                <span class="text-label_1235-941">{{ chart.firstName }}</span>
              </div>
            </div>
          </ng-container>
        </div>

        <!-- Last Name Column -->
        <div class="column_1134-1235">
          <div class="header-item_1134-1236">
            <div class="table-item_1134-1237">
              <span class="text-label_1134-1239">Last name</span>
            </div>
          </div>
          <ng-container *ngFor="let chart of filteredCharts">
            <div class="table-item_1134-1240">
              <div class="icon-text_1134-1241">
                <span class="text-label_1134-1242">{{ chart.lastName }}</span>
              </div>
            </div>
          </ng-container>
        </div>

        <!-- Middle Name Column -->
        <div class="column_1235-969">
          <div class="header-item_1235-970">
            <div class="table-item_1235-971">
              <span class="text-label_1235-973">Middle name</span>
            </div>
          </div>
          <ng-container *ngFor="let chart of filteredCharts">
            <div class="table-item_1235-974">
              <div class="icon-text_1235-975">
                <span *ngIf="chart.middleName" class="text-label_1235-979">{{ chart.middleName }}</span>
              </div>
            </div>
          </ng-container>
        </div>

        <!-- DOB Column -->
        <div class="column_1134-1270">
          <div class="header-item_1134-1271">
            <div class="table-item_1134-1272">
              <span class="text-label_1134-1274">DOB</span>
            </div>
          </div>
          <ng-container *ngFor="let chart of filteredCharts">
            <div class="table-item_1134-1275">
              <div class="icon-text_1134-1276">
                <span class="text-label_1134-1277">{{ chart.dob }}</span>
              </div>
            </div>
          </ng-container>
        </div>

        <!-- LOB Column -->
        <div class="column_1134-1305">
          <div class="header-item_1134-1306">
            <div class="table-item_1134-1307">
              <span class="text-label_1134-1309">LOB</span>
            </div>
          </div>
          <ng-container *ngFor="let chart of filteredCharts">
            <div class="table-item_1134-1310">
              <div class="icon-text_1134-1311">
                <span class="text-label_1134-1312">{{ chart.lob }}</span>
              </div>
            </div>
          </ng-container>
        </div>

        <!-- Measure Column -->
        <div class="column_1134-1340">
          <div class="header-item_1134-1341">
            <div class="table-item_1134-1342">
              <span class="text-label_1134-1344">Measure</span>
            </div>
          </div>
          <ng-container *ngFor="let chart of filteredCharts">
            <div class="table-item_1134-1345">
              <div class="icon-text_1134-1346">
                <span class="text-label_1134-1347">{{ chart.measure }}</span>
              </div>
            </div>
          </ng-container>
        </div>

        <!-- Review 1 Column -->
        <div class="column_1134-1375">
          <div class="header-item_1134-1376">
            <div class="table-item_1134-1377">
              <span class="text-label_1134-1379">Review 1</span>
            </div>
          </div>
          <ng-container *ngFor="let chart of filteredCharts">
            <div class="table-item_1134-1380">
              <span class="text-label_1134-1381">{{ chart.review1 }}</span>
            </div>
          </ng-container>
        </div>

        <!-- Review 2 Column -->
        <div class="column_1134-1400">
          <div class="header-item_1134-1401">
            <div class="table-item_1134-1402">
              <span class="text-label_1134-1404">Review 2</span>
            </div>
          </div>
          <ng-container *ngFor="let chart of filteredCharts">
            <div class="table-item_1134-1405">
              <span class="text-label_1134-1406">{{ chart.review2 }}</span>
            </div>
          </ng-container>
        </div>

        <!-- Assigned Column -->
        <div class="column_1134-1425">
          <div class="header-item_1134-1426">
            <div class="table-item_1134-1427">
              <span class="text-label_1134-1429">Assigned</span>
            </div>
          </div>
          <ng-container *ngFor="let chart of filteredCharts">
            <div class="table-item_1134-1430">
              <span class="text-label_1134-1431">{{ chart.assigned }}</span>
            </div>
          </ng-container>
        </div>

        <!-- Status Column -->
        <div class="column_1134-1450">
          <div class="header-item_1134-1451">
            <div class="table-item_1134-1452">
              <span class="text-label_1134-1454">Status</span>
            </div>
          </div>
          <ng-container *ngFor="let chart of filteredCharts">
            <div class="table-item_1134-1455">
              <app-sumbit-button
                [property1]="chart.status === 'Review' ? 'Default' : 'Inactive'"
                class="sumbit-button_1134-1462"
                (click)="navigateToChartReview(chart)">
              </app-sumbit-button>
            </div>
          </ng-container>
        </div>
      </div>
    </div>

    <!-- No Charts Message -->
    <div *ngIf="filteredCharts.length === 0" class="no-charts-message">
      No charts found matching your criteria.
    </div>
  </div>
</div>
`, styles: ['/* src/app/features/dashboard/components/assigned-table/assigned-table.component.scss */\n.assigned-table_1134-1191 {\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 20px;\n  box-sizing: border-box;\n  width: 1380px;\n}\n.table_1134-1192 {\n  padding: 20px;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 0px;\n  box-sizing: border-box;\n  border-radius: 8px;\n  border-color: #f1f5f7;\n  border-style: solid;\n  border-width: 1px;\n  background: #ffffff;\n  width: 100%;\n}\n.table_1134-1208 {\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 0px;\n  box-sizing: border-box;\n  width: 100%;\n}\n.columns_1134-1209 {\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 0px;\n  box-sizing: border-box;\n  width: 100%;\n}\n.column_1134-1210 {\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 0px;\n  box-sizing: border-box;\n  width: 100%;\n}\n.header-item_1134-1211 {\n  padding: 0px 12px 0px 12px;\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 10px;\n  box-sizing: border-box;\n  border-color: #f1f5f7;\n  border-style: solid;\n  border-bottom-width: 1px;\n  width: 100%;\n}\n.table-item_1134-1212 {\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  height: 68px;\n}\n.text-label_1134-1214 {\n  color: #17181a;\n  font-size: 12px;\n  font-family: Urbane;\n  line-height: 20px;\n  letter-spacing: 0%;\n  text-decoration: none;\n  font-weight: 500;\n  text-align: left;\n  text-wrap: nowrap;\n}\n.table-item_1134-1215 {\n  padding: 13px 12px 13px 12px;\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  height: 68px;\n}\n.text-label_1134-1216 {\n  color: #17181a;\n  font-size: 12px;\n  font-family: Urbane;\n  line-height: 20px;\n  letter-spacing: 0%;\n  text-decoration: none;\n  font-weight: 300;\n  text-align: left;\n  text-wrap: nowrap;\n}\n.column_1235-930 {\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 0px;\n  box-sizing: border-box;\n  width: 107px;\n}\n.header-item_1235-931 {\n  padding: 0px 12px 0px 12px;\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 10px;\n  box-sizing: border-box;\n  border-color: #f1f5f7;\n  border-style: solid;\n  border-bottom-width: 1px;\n  width: 100%;\n}\n.table-item_1235-932 {\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  height: 68px;\n}\n.text-label_1235-934 {\n  color: #17181a;\n  font-size: 12px;\n  font-family: Urbane;\n  line-height: 20px;\n  letter-spacing: 0%;\n  text-decoration: none;\n  font-weight: 500;\n  text-align: left;\n  text-wrap: nowrap;\n}\n.table-item_1235-939 {\n  padding: 10px 12px 10px 12px;\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  background: #ffffff;\n  height: 68px;\n}\n.icon-text_1235-940 {\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  background: #ffffff;\n}\n.text-label_1235-941 {\n  color: #17181a;\n  font-size: 12px;\n  font-family: Urbane;\n  line-height: 20px;\n  letter-spacing: 0%;\n  text-decoration: none;\n  font-weight: 300;\n  text-align: left;\n  text-wrap: nowrap;\n}\n.column_1134-1235 {\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 0px;\n  box-sizing: border-box;\n  width: 106px;\n}\n.header-item_1134-1236 {\n  padding: 0px 12px 0px 12px;\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 10px;\n  box-sizing: border-box;\n  border-color: #f1f5f7;\n  border-style: solid;\n  border-bottom-width: 1px;\n  width: 100%;\n}\n.table-item_1134-1237 {\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  height: 68px;\n}\n.text-label_1134-1239 {\n  color: #17181a;\n  font-size: 12px;\n  font-family: Urbane;\n  line-height: 20px;\n  letter-spacing: 0%;\n  text-decoration: none;\n  font-weight: 500;\n  text-align: left;\n  text-wrap: nowrap;\n}\n.table-item_1134-1240 {\n  padding: 10px 12px 10px 12px;\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  background: #ffffff;\n  height: 68px;\n}\n.icon-text_1134-1241 {\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  background: #ffffff;\n}\n.text-label_1134-1242 {\n  color: #17181a;\n  font-size: 12px;\n  font-family: Urbane;\n  line-height: 20px;\n  letter-spacing: 0%;\n  text-decoration: none;\n  font-weight: 300;\n  text-align: left;\n  text-wrap: nowrap;\n}\n.column_1235-969 {\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 0px;\n  box-sizing: border-box;\n  width: 102px;\n}\n.header-item_1235-970 {\n  padding: 0px 12px 0px 12px;\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 10px;\n  box-sizing: border-box;\n  border-color: #f1f5f7;\n  border-style: solid;\n  border-bottom-width: 1px;\n  width: 100%;\n}\n.table-item_1235-971 {\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  height: 68px;\n}\n.text-label_1235-973 {\n  color: #17181a;\n  font-size: 12px;\n  font-family: Urbane;\n  line-height: 20px;\n  letter-spacing: 0%;\n  text-decoration: none;\n  font-weight: 500;\n  text-align: left;\n  text-wrap: nowrap;\n}\n.table-item_1235-974 {\n  padding: 10px 12px 10px 12px;\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  background: #ffffff;\n  height: 68px;\n}\n.icon-text_1235-975 {\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  background: #ffffff;\n}\n.text-label_1235-979 {\n  color: #17181a;\n  font-size: 12px;\n  font-family: Urbane;\n  line-height: 20px;\n  letter-spacing: 0%;\n  text-decoration: none;\n  font-weight: 300;\n  text-align: left;\n  text-wrap: wrap;\n  width: 78px;\n}\n.column_1134-1270 {\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 0px;\n  box-sizing: border-box;\n  width: 99px;\n}\n.header-item_1134-1271 {\n  padding: 0px 12px 0px 12px;\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 10px;\n  box-sizing: border-box;\n  border-color: #f1f5f7;\n  border-style: solid;\n  border-bottom-width: 1px;\n  width: 100%;\n}\n.table-item_1134-1272 {\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  height: 68px;\n}\n.text-label_1134-1274 {\n  color: #17181a;\n  font-size: 12px;\n  font-family: Urbane;\n  line-height: 20px;\n  letter-spacing: 0%;\n  text-decoration: none;\n  font-weight: 500;\n  text-align: left;\n  text-wrap: nowrap;\n}\n.table-item_1134-1275 {\n  padding: 10px 12px 10px 12px;\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  background: #ffffff;\n  height: 68px;\n}\n.icon-text_1134-1276 {\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  background: #ffffff;\n}\n.text-label_1134-1277 {\n  color: #17181a;\n  font-size: 12px;\n  font-family: Urbane;\n  line-height: 20px;\n  letter-spacing: 0%;\n  text-decoration: none;\n  font-weight: 300;\n  text-align: left;\n  text-wrap: nowrap;\n}\n.no-charts-message {\n  padding: 40px 20px;\n  text-align: center;\n  font-size: 14px;\n  font-family: "Urbane", sans-serif;\n  color: #757575;\n  font-style: italic;\n  background: #ffffff;\n}\n/*# sourceMappingURL=assigned-table.component.css.map */\n'] }]
  }], () => [{ type: Router }], { charts: [{
    type: Input
  }], searchText: [{
    type: Input
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(AssignedTableComponent, { className: "AssignedTableComponent", filePath: "src/app/features/dashboard/components/assigned-table/assigned-table.component.ts", lineNumber: 14 });
})();

// src/app/shared/components/icons/refresh-icon.component.ts
var RefreshIconComponent = class _RefreshIconComponent {
  color = "default";
  static \u0275fac = function RefreshIconComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _RefreshIconComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _RefreshIconComponent, selectors: [["app-refresh-icon"]], inputs: { color: "color" }, decls: 6, vars: 1, consts: [["width", "16", "height", "16", "viewBox", "0 0 20 20", "fill", "none", "xmlns", "http://www.w3.org/2000/svg", 3, "ngClass"], ["d", "M9.9993 18.3332C14.6017 18.3332 18.3327 14.6022 18.3327 9.9998C18.3327 5.3975 14.6017 1.6665 9.9993 1.6665C5.397 1.6665 1.666 5.3975 1.666 9.9998C1.666 14.6022 5.397 18.3332 9.9993 18.3332Z", "stroke", "currentColor", "stroke-width", "1.5", "stroke-linecap", "round", "stroke-linejoin", "round"], ["d", "M6.6758 12.0917C6.8258 12.3417 7.0091 12.5751 7.2174 12.7834C8.7508 14.3167 11.2424 14.3167 12.7841 12.7834C13.4091 12.1584 13.7674 11.3667 13.8841 10.5583", "stroke", "currentColor", "stroke-width", "1.5", "stroke-linecap", "round", "stroke-linejoin", "round"], ["d", "M6.1172 9.4417C6.2339 8.625 6.5922 7.8417 7.2172 7.2167C8.7505 5.6833 11.2422 5.6833 12.7839 7.2167C13.0005 7.4333 13.1755 7.6667 13.3255 7.9083", "stroke", "currentColor", "stroke-width", "1.5", "stroke-linecap", "round", "stroke-linejoin", "round"], ["d", "M6.5176 14.3165V12.0916H8.7426", "stroke", "currentColor", "stroke-width", "1.5", "stroke-linecap", "round", "stroke-linejoin", "round"], ["d", "M13.4848 5.6831V7.9081H11.2598", "stroke", "currentColor", "stroke-width", "1.5", "stroke-linecap", "round", "stroke-linejoin", "round"]], template: function RefreshIconComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(0, "svg", 0);
      \u0275\u0275element(1, "path", 1)(2, "path", 2)(3, "path", 3)(4, "path", 4)(5, "path", 5);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275property("ngClass", "refresh-icon-" + ctx.color);
    }
  }, dependencies: [CommonModule, NgClass], styles: ["\n\n[_nghost-%COMP%] {\n  display: inline-block;\n  width: 16px;\n  height: 16px;\n}\n.refresh-icon-default[_ngcontent-%COMP%] {\n  color: #17181A;\n}\n.refresh-icon-hover[_ngcontent-%COMP%] {\n  color: #17181A;\n}\n.refresh-icon-click[_ngcontent-%COMP%] {\n  color: #FFFFFF;\n}\nsvg[_ngcontent-%COMP%] {\n  width: 16px;\n  height: 16px;\n  display: block;\n}\n/*# sourceMappingURL=refresh-icon.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RefreshIconComponent, [{
    type: Component,
    args: [{ selector: "app-refresh-icon", template: `
    <svg
      width="16"
      height="16"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      [ngClass]="'refresh-icon-' + color"
    >
      <path d="M9.9993 18.3332C14.6017 18.3332 18.3327 14.6022 18.3327 9.9998C18.3327 5.3975 14.6017 1.6665 9.9993 1.6665C5.397 1.6665 1.666 5.3975 1.666 9.9998C1.666 14.6022 5.397 18.3332 9.9993 18.3332Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M6.6758 12.0917C6.8258 12.3417 7.0091 12.5751 7.2174 12.7834C8.7508 14.3167 11.2424 14.3167 12.7841 12.7834C13.4091 12.1584 13.7674 11.3667 13.8841 10.5583" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M6.1172 9.4417C6.2339 8.625 6.5922 7.8417 7.2172 7.2167C8.7505 5.6833 11.2422 5.6833 12.7839 7.2167C13.0005 7.4333 13.1755 7.6667 13.3255 7.9083" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M6.5176 14.3165V12.0916H8.7426" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M13.4848 5.6831V7.9081H11.2598" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
  `, standalone: true, imports: [CommonModule], styles: ["/* src/app/shared/components/icons/refresh-icon.component.scss */\n:host {\n  display: inline-block;\n  width: 16px;\n  height: 16px;\n}\n.refresh-icon-default {\n  color: #17181A;\n}\n.refresh-icon-hover {\n  color: #17181A;\n}\n.refresh-icon-click {\n  color: #FFFFFF;\n}\nsvg {\n  width: 16px;\n  height: 16px;\n  display: block;\n}\n/*# sourceMappingURL=refresh-icon.component.css.map */\n"] }]
  }], null, { color: [{
    type: Input
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(RefreshIconComponent, { className: "RefreshIconComponent", filePath: "src/app/shared/components/icons/refresh-icon.component.ts", lineNumber: 26 });
})();

export {
  AssignedTableComponent,
  RefreshIconComponent
};
//# sourceMappingURL=chunk-YLWVG4A5.js.map

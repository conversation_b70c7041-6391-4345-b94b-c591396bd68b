{"version": 3, "sources": ["src/app/features/dashboard/pages/dashboard-page/dashboard-page.component.scss"], "sourcesContent": ["@use 'variables' as vars;\r\n@use 'mixins' as mix;\r\n\r\n.dashboard-container {\r\n  padding: 20px;\r\n  background-color: var(--light-background, #F9FBFC);\r\n  min-height: calc(100vh - 80px); // Subtract header height\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20px;\r\n}\r\n\r\n.dashboard-section {\r\n  background-color: var(--white, white);\r\n  border-radius: 8px;\r\n  border: 1px solid var(--light-borders, #F1F5F7);\r\n  padding: 20px;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding-bottom: 20px;\r\n\r\n  h2 {\r\n    font-size: 20px;\r\n    font-weight: 600;\r\n    line-height: 32px;\r\n    color: var(--text-black, #17181A);\r\n    margin: 0;\r\n  }\r\n}\r\n\r\n.section-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  flex: 1;\r\n  justify-content: flex-end;\r\n}\r\n\r\n"], "mappings": ";AAGA,CAAA;AACE,WAAA;AACA,oBAAA,IAAA,kBAAA,EAAA;AACA,cAAA,KAAA,MAAA,EAAA;AACA,WAAA;AACA,kBAAA;AACA,OAAA;;AAGF,CAAA;AACE,oBAAA,IAAA,OAAA,EAAA;AACA,iBAAA;AACA,UAAA,IAAA,MAAA,IAAA,eAAA,EAAA;AACA,WAAA;AACA,WAAA;AACA,kBAAA;;AAGF,CAAA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,kBAAA;;AAEA,CANF,eAME;AACE,aAAA;AACA,eAAA;AACA,eAAA;AACA,SAAA,IAAA,YAAA,EAAA;AACA,UAAA;;AAIJ,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;AACA,QAAA;AACA,mBAAA;;", "names": []}
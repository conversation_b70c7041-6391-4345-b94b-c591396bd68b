{"name": "minipass-pipeline", "version": "1.2.4", "description": "create a pipeline of streams using Minipass", "author": "<PERSON> <<EMAIL>> (https://izs.me)", "license": "ISC", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "tap": {"check-coverage": true}, "devDependencies": {"tap": "^14.6.9"}, "dependencies": {"minipass": "^3.0.0"}, "files": ["index.js"], "engines": {"node": ">=8"}}
{"version": 3, "sources": ["src/app/shared/components/form-controls/comment-box/comment-box.component.scss", "src/styles/_variables.scss"], "sourcesContent": ["@use 'variables' as variables;\n\n// Comment Box Component - Based on Figma comment-box.html specifications\n.comment-box {\n  flex: 1 1 0; // Exact Figma flex\n  height: 30px; // Exact Figma height\n  padding: 4px; // Exact Figma padding\n  background: white !important; // Exact Figma background - CRITICAL for visibility\n  overflow: hidden; // Exact Figma overflow\n  border-radius: 10px; // Exact Figma border radius\n  outline-offset: -1px; // Exact Figma outline offset\n  flex-direction: column; // Exact Figma flex-direction\n  justify-content: center; // Exact Figma justify\n  align-items: center; // Exact Figma align\n  display: inline-flex; // Exact Figma display\n  box-sizing: border-box;\n\n  // Default state - gray border\n  &.default {\n    outline: 1px #D9E1E7 solid; // Exact Figma outline (var(--gray-2))\n  }\n\n  // Active state - darker gray border when focused\n  &.active {\n    outline: 1px #547996 solid; // Exact Figma outline (var(--gray-3))\n  }\n\n  // Entered state - gray border when has content\n  &.entered {\n    outline: 1px #D9E1E7 solid; // Exact Figma outline (var(--gray-2))\n  }\n\n  // Disabled state\n  &.disabled {\n    background-color: variables.$gray-1;\n    cursor: not-allowed;\n  }\n}\n\n.row {\n  align-self: stretch; // Exact Figma align-self\n  padding-left: 12px; // Exact Figma padding\n  padding-right: 12px; // Exact Figma padding\n  background: transparent; // Ensure no background override\n  border-radius: 6px; // Exact Figma border radius\n  justify-content: flex-start; // Exact Figma justify\n  align-items: center; // Exact Figma align\n  gap: 12px; // Exact Figma gap\n  display: inline-flex; // Exact Figma display\n}\n\n.comment-input {\n  flex: 1 1 0; // Exact Figma flex\n  border: none !important;\n  outline: none !important;\n  background: transparent !important;\n  color: #17181A !important; // Ensure text is visible\n  font-size: 10px; // Exact Figma font size\n  font-family: 'Urbane', sans-serif; // Exact Figma font family\n  font-weight: 300; // Exact Figma font weight\n  line-height: 12px; // Exact Figma line height\n  word-wrap: break-word; // Exact Figma word wrap\n  padding: 0;\n  width: 100%;\n  height: 100%;\n\n  // Default state - gray placeholder\n  &::placeholder {\n    color: #547996; // Exact Figma color (var(--gray-3))\n  }\n\n  // Active and Entered states - black text\n  .active &,\n  .entered & {\n    color: #17181A; // Exact Figma color (var(--text-black))\n  }\n\n  // Default state - gray text\n  .default & {\n    color: #547996; // Exact Figma color (var(--gray-3))\n  }\n\n  &:disabled {\n    cursor: not-allowed;\n    color: variables.$gray-3;\n  }\n\n  &:focus {\n    outline: none;\n  }\n}\n", "// Color Variables\r\n$text-black: #17181A;\r\n$primary-blue: #3870B8;\r\n$hover-blue: #468CE7;\r\n$click-blue: #285082;\r\n$light-blue: #BFD0EE;\r\n$link: #0071BC;\r\n$gray-1: #F1F5F7;\r\n$gray-2: #D9E1E7;\r\n$gray-3: #547996;\r\n$gray-4: #384455;\r\n$success-green: #1AD598;\r\n$white: #FFFFFF;\r\n$background-gray: #F6F6F6;\r\n$light-background: #F9FBFC;\r\n$light-primary: #809FB8;\r\n\r\n// Opacity Variables\r\n$primary-blue-opacity-20: rgba(56, 112, 184, 0.20);\r\n$success-green-opacity-10: rgba(26, 213, 152, 0.10);\r\n$success-green-opacity-40: rgba(26, 213, 152, 0.40);\r\n\r\n// Spacing Variables\r\n$spacing-xs: 4px;\r\n$spacing-sm: 8px;\r\n$spacing-md: 12px;\r\n$spacing-lg: 16px;\r\n$spacing-xl: 20px;\r\n$spacing-xxl: 24px;\r\n$spacing-xxxl: 30px;\r\n$spacing-huge: 40px;\r\n$spacing-giant: 48px;\r\n$spacing-mega: 55px;\r\n\r\n// Border Radius\r\n$border-radius-sm: 5px;\r\n$border-radius-md: 6px;\r\n$border-radius-lg: 8px;\r\n$border-radius-xl: 10px;\r\n$border-radius-round: 90px;\r\n$border-radius-circle: 120px;\r\n\r\n// Box Shadow\r\n$box-shadow-sm: 0px 1px 2px rgba(16, 24, 40, 0.05);\r\n\r\n// Border Width\r\n$border-width-default: 1px;\r\n$border-width-thick: 1.5px;\r\n\r\n// Z-index\r\n$z-index-default: 1;\r\n$z-index-dropdown: 1000;\r\n$z-index-sticky: 1020;\r\n$z-index-fixed: 1030;\r\n$z-index-modal-backdrop: 1040;\r\n$z-index-modal: 1050;\r\n$z-index-popover: 1060;\r\n$z-index-tooltip: 1070;"], "mappings": ";AAGA,CAAA;AACE,QAAA,EAAA,EAAA;AACA,UAAA;AACA,WAAA;AACA,cAAA;AACA,YAAA;AACA,iBAAA;AACA,kBAAA;AACA,kBAAA;AACA,mBAAA;AACA,eAAA;AACA,WAAA;AACA,cAAA;;AAGA,CAfF,WAeE,CAAA;AACE,WAAA,IAAA,QAAA;;AAIF,CApBF,WAoBE,CAAA;AACE,WAAA,IAAA,QAAA;;AAIF,CAzBF,WAyBE,CAAA;AACE,WAAA,IAAA,QAAA;;AAIF,CA9BF,WA8BE,CAAA;AACE,oBC3BK;AD4BL,UAAA;;AAIJ,CAAA;AACE,cAAA;AACA,gBAAA;AACA,iBAAA;AACA,cAAA;AACA,iBAAA;AACA,mBAAA;AACA,eAAA;AACA,OAAA;AACA,WAAA;;AAGF,CAAA;AACE,QAAA,EAAA,EAAA;AACA,UAAA;AACA,WAAA;AACA,cAAA;AACA,SAAA;AACA,aAAA;AACA,eAAA,QAAA,EAAA;AACA,eAAA;AACA,eAAA;AACA,aAAA;AACA,WAAA;AACA,SAAA;AACA,UAAA;;AAGA,CAhBF,aAgBE;AACE,SAAA;;AAIF,CAjDA,OAiDA,CArBF;AAqBE,CA5CA,QA4CA,CArBF;AAuBI,SAAA;;AAIF,CA5DA,QA4DA,CA3BF;AA4BI,SAAA;;AAGF,CA/BF,aA+BE;AACE,UAAA;AACA,SC3EK;;AD8EP,CApCF,aAoCE;AACE,WAAA;;", "names": []}
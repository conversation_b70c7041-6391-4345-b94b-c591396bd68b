import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError, of } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import * as <PERSON> from 'papaparse';
import {
  RawCsvChartData,
  AssignedChart,
  ChartDataResponse,
  DataLoadingState,
  CsvParseResult,
  ProviderInfo,
  ChartStatus
} from '../models/chart-data.models';

@Injectable({
  providedIn: 'root'
})
export class CsvDataService {
  private readonly CSV_FILE_PATH = 'assets/CSVs/MRSDS_Template_CQ(Template).csv';
  
  // BehaviorSubjects for reactive data management
  private chartsSubject = new BehaviorSubject<AssignedChart[]>([]);
  private loadingStateSubject = new BehaviorSubject<DataLoadingState>({
    loading: false,
    error: null,
    lastUpdated: null
  });

  // Public observables
  public charts$ = this.chartsSubject.asObservable();
  public loadingState$ = this.loadingStateSubject.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Load and parse CSV data from assets
   */
  loadChartData(): Observable<ChartDataResponse> {
    this.setLoadingState({ loading: true, error: null, lastUpdated: null });

    return this.http.get(this.CSV_FILE_PATH, { responseType: 'text' }).pipe(
      map(csvText => this.parseCsvData(csvText)),
      map(parseResult => this.transformToChartData(parseResult)),
      tap(response => {
        this.chartsSubject.next(response.charts);
        this.setLoadingState({
          loading: false,
          error: null,
          lastUpdated: new Date()
        });
      }),
      catchError(error => {
        const errorMessage = `Failed to load chart data: ${error.message || error}`;
        this.setLoadingState({
          loading: false,
          error: errorMessage,
          lastUpdated: null
        });
        return throwError(() => new Error(errorMessage));
      })
    );
  }

  /**
   * Refresh chart data by reloading from CSV
   */
  refreshChartData(): Observable<ChartDataResponse> {
    return this.loadChartData();
  }

  /**
   * Get current chart data synchronously
   */
  getCurrentCharts(): AssignedChart[] {
    return this.chartsSubject.value;
  }

  /**
   * Get current loading state synchronously
   */
  getCurrentLoadingState(): DataLoadingState {
    return this.loadingStateSubject.value;
  }

  /**
   * Parse CSV text using PapaParse
   */
  private parseCsvData(csvText: string): CsvParseResult {
    const parseResult = Papa.parse<RawCsvChartData>(csvText, {
      header: true,
      skipEmptyLines: true,
      transform: (value: string) => value.trim()
    });

    return {
      data: this.transformRawDataToCharts(parseResult.data),
      errors: parseResult.errors.map((error: any) => error.message),
      meta: parseResult.meta
    };
  }

  /**
   * Transform raw CSV data to AssignedChart objects
   */
  private transformRawDataToCharts(rawData: RawCsvChartData[]): AssignedChart[] {
    return rawData.map(raw => this.transformSingleChart(raw));
  }

  /**
   * Transform a single raw CSV record to AssignedChart
   */
  private transformSingleChart(raw: RawCsvChartData): AssignedChart {
    const provider: ProviderInfo = {
      npi: raw.PRVR_NPI || '',
      firstName: raw.PRVR_FNAME || '',
      lastName: raw.PRVR_LNAME || '',
      fullName: `${raw.PRVR_FNAME || ''} ${raw.PRVR_LNAME || ''}`.trim()
    };

    const fullName = `${raw.MBR_FNAME || ''} ${raw.MBR_MNAME || ''} ${raw.MBR_LNAME || ''}`.trim();

    return {
      memberId: raw.BSC_MBR_ID || '',
      firstName: raw.MBR_FNAME || '',
      middleName: raw.MBR_MNAME || '',
      lastName: raw.MBR_LNAME || '',
      fullName: fullName,
      dob: this.formatDate(raw.MBR_DOB),
      lob: raw.LOB || '',
      measure: raw.MeasureKey || '',
      measureKey: raw.MeasureKey || '',
      gender: raw.MBR_GENDER || '',
      filename: raw.FILENAME || '',
      provider: provider,
      review1: 'Jane Chu', // Default reviewer from mockup
      review2: '-', // Default empty second reviewer
      assigned: '04/15/25 1:30pm', // Default assignment time from mockup
      status: 'Review' as ChartStatus // Default status for new charts
    };
  }

  /**
   * Transform parse result to service response format
   */
  private transformToChartData(parseResult: CsvParseResult): ChartDataResponse {
    if (parseResult.errors.length > 0) {
      console.warn('CSV parsing warnings:', parseResult.errors);
    }

    return {
      charts: parseResult.data,
      loadingState: {
        loading: false,
        error: null,
        lastUpdated: new Date()
      },
      totalCount: parseResult.data.length
    };
  }

  /**
   * Format date string to match expected format
   */
  private formatDate(dateString: string): string {
    if (!dateString) return '';
    
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return dateString; // Return original if invalid
      
      // Format as MM/DD/YYYY to match mockup format
      return date.toLocaleDateString('en-US', {
        month: '2-digit',
        day: '2-digit',
        year: 'numeric'
      });
    } catch {
      return dateString; // Return original if parsing fails
    }
  }

  /**
   * Update loading state and notify subscribers
   */
  private setLoadingState(state: DataLoadingState): void {
    this.loadingStateSubject.next(state);
  }
}

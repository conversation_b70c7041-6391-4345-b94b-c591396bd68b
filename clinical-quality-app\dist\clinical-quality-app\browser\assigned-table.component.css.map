{"version": 3, "sources": ["src/app/features/dashboard/components/assigned-table/assigned-table.component.scss"], "sourcesContent": ["// Figma-aligned styles for assigned table component\n.assigned-table_1134-1191 {\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 20px;\n  box-sizing: border-box;\n  width: 1380px;\n}\n\n.table_1134-1192 {\n  padding: 20px;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 0px;\n  box-sizing: border-box;\n  border-radius: 8px;\n  border-color: #f1f5f7;\n  border-style: solid;\n  border-width: 1px;\n  background: #ffffff;\n  width: 100%;\n}\n\n.table_1134-1208 {\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 0px;\n  box-sizing: border-box;\n  width: 100%;\n}\n\n.columns_1134-1209 {\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 0px;\n  box-sizing: border-box;\n  width: 100%;\n}\n\n// Member ID Column\n.column_1134-1210 {\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 0px;\n  box-sizing: border-box;\n  width: 100%;\n}\n\n.header-item_1134-1211 {\n  padding: 0px 12px 0px 12px;\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 10px;\n  box-sizing: border-box;\n  border-color: #f1f5f7;\n  border-style: solid;\n  border-bottom-width: 1px;\n  width: 100%;\n}\n\n.table-item_1134-1212 {\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  height: 68px;\n}\n\n.text-label_1134-1214 {\n  color: #17181a;\n  font-size: 12px;\n  font-family: Urbane;\n  line-height: 20px;\n  letter-spacing: 0%;\n  text-decoration: none;\n  font-weight: 500;\n  text-align: left;\n  text-wrap: nowrap;\n}\n\n.table-item_1134-1215 {\n  padding: 13px 12px 13px 12px;\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  height: 68px;\n}\n\n.text-label_1134-1216 {\n  color: #17181a;\n  font-size: 12px;\n  font-family: Urbane;\n  line-height: 20px;\n  letter-spacing: 0%;\n  text-decoration: none;\n  font-weight: 300;\n  text-align: left;\n  text-wrap: nowrap;\n}\n\n// First Name Column\n.column_1235-930 {\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 0px;\n  box-sizing: border-box;\n  width: 107px;\n}\n\n.header-item_1235-931 {\n  padding: 0px 12px 0px 12px;\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 10px;\n  box-sizing: border-box;\n  border-color: #f1f5f7;\n  border-style: solid;\n  border-bottom-width: 1px;\n  width: 100%;\n}\n\n.table-item_1235-932 {\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  height: 68px;\n}\n\n.text-label_1235-934 {\n  color: #17181a;\n  font-size: 12px;\n  font-family: Urbane;\n  line-height: 20px;\n  letter-spacing: 0%;\n  text-decoration: none;\n  font-weight: 500;\n  text-align: left;\n  text-wrap: nowrap;\n}\n\n.table-item_1235-939 {\n  padding: 10px 12px 10px 12px;\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  background: #ffffff;\n  height: 68px;\n}\n\n.icon-text_1235-940 {\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  background: #ffffff;\n}\n\n.text-label_1235-941 {\n  color: #17181a;\n  font-size: 12px;\n  font-family: Urbane;\n  line-height: 20px;\n  letter-spacing: 0%;\n  text-decoration: none;\n  font-weight: 300;\n  text-align: left;\n  text-wrap: nowrap;\n}\n\n// Last Name Column\n.column_1134-1235 {\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 0px;\n  box-sizing: border-box;\n  width: 106px;\n}\n\n.header-item_1134-1236 {\n  padding: 0px 12px 0px 12px;\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 10px;\n  box-sizing: border-box;\n  border-color: #f1f5f7;\n  border-style: solid;\n  border-bottom-width: 1px;\n  width: 100%;\n}\n\n.table-item_1134-1237 {\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  height: 68px;\n}\n\n.text-label_1134-1239 {\n  color: #17181a;\n  font-size: 12px;\n  font-family: Urbane;\n  line-height: 20px;\n  letter-spacing: 0%;\n  text-decoration: none;\n  font-weight: 500;\n  text-align: left;\n  text-wrap: nowrap;\n}\n\n.table-item_1134-1240 {\n  padding: 10px 12px 10px 12px;\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  background: #ffffff;\n  height: 68px;\n}\n\n.icon-text_1134-1241 {\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  background: #ffffff;\n}\n\n.text-label_1134-1242 {\n  color: #17181a;\n  font-size: 12px;\n  font-family: Urbane;\n  line-height: 20px;\n  letter-spacing: 0%;\n  text-decoration: none;\n  font-weight: 300;\n  text-align: left;\n  text-wrap: nowrap;\n}\n\n// Middle Name Column\n.column_1235-969 {\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 0px;\n  box-sizing: border-box;\n  width: 102px;\n}\n\n.header-item_1235-970 {\n  padding: 0px 12px 0px 12px;\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 10px;\n  box-sizing: border-box;\n  border-color: #f1f5f7;\n  border-style: solid;\n  border-bottom-width: 1px;\n  width: 100%;\n}\n\n.table-item_1235-971 {\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  height: 68px;\n}\n\n.text-label_1235-973 {\n  color: #17181a;\n  font-size: 12px;\n  font-family: Urbane;\n  line-height: 20px;\n  letter-spacing: 0%;\n  text-decoration: none;\n  font-weight: 500;\n  text-align: left;\n  text-wrap: nowrap;\n}\n\n.table-item_1235-974 {\n  padding: 10px 12px 10px 12px;\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  background: #ffffff;\n  height: 68px;\n}\n\n.icon-text_1235-975 {\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  background: #ffffff;\n}\n\n.text-label_1235-979 {\n  color: #17181a;\n  font-size: 12px;\n  font-family: Urbane;\n  line-height: 20px;\n  letter-spacing: 0%;\n  text-decoration: none;\n  font-weight: 300;\n  text-align: left;\n  text-wrap: wrap;\n  width: 78px;\n}\n\n// DOB Column\n.column_1134-1270 {\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 0px;\n  box-sizing: border-box;\n  width: 99px;\n}\n\n.header-item_1134-1271 {\n  padding: 0px 12px 0px 12px;\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 10px;\n  box-sizing: border-box;\n  border-color: #f1f5f7;\n  border-style: solid;\n  border-bottom-width: 1px;\n  width: 100%;\n}\n\n.table-item_1134-1272 {\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  height: 68px;\n}\n\n.text-label_1134-1274 {\n  color: #17181a;\n  font-size: 12px;\n  font-family: Urbane;\n  line-height: 20px;\n  letter-spacing: 0%;\n  text-decoration: none;\n  font-weight: 500;\n  text-align: left;\n  text-wrap: nowrap;\n}\n\n.table-item_1134-1275 {\n  padding: 10px 12px 10px 12px;\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  background: #ffffff;\n  height: 68px;\n}\n\n.icon-text_1134-1276 {\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 12px;\n  box-sizing: border-box;\n  background: #ffffff;\n}\n\n.text-label_1134-1277 {\n  color: #17181a;\n  font-size: 12px;\n  font-family: Urbane;\n  line-height: 20px;\n  letter-spacing: 0%;\n  text-decoration: none;\n  font-weight: 300;\n  text-align: left;\n  text-wrap: nowrap;\n}\n\n.no-charts-message {\n  padding: 40px 20px;\n  text-align: center;\n  font-size: 14px;\n  font-family: 'Urbane', sans-serif;\n  color: #757575;\n  font-style: italic;\n  background: #ffffff;\n}\n"], "mappings": ";AACA,CAAA;AACE,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;AACA,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;AACA,iBAAA;AACA,gBAAA;AACA,gBAAA;AACA,gBAAA;AACA,cAAA;AACA,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;AACA,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;AACA,SAAA;;AAIF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;AACA,SAAA;;AAGF,CAAA;AACE,WAAA,IAAA,KAAA,IAAA;AACA,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;AACA,gBAAA;AACA,gBAAA;AACA,uBAAA;AACA,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;AACA,UAAA;;AAGF,CAAA;AACE,SAAA;AACA,aAAA;AACA,eAAA;AACA,eAAA;AACA,kBAAA;AACA,mBAAA;AACA,eAAA;AACA,cAAA;AACA,aAAA;;AAGF,CAAA;AACE,WAAA,KAAA,KAAA,KAAA;AACA,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;AACA,UAAA;;AAGF,CAAA;AACE,SAAA;AACA,aAAA;AACA,eAAA;AACA,eAAA;AACA,kBAAA;AACA,mBAAA;AACA,eAAA;AACA,cAAA;AACA,aAAA;;AAIF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;AACA,SAAA;;AAGF,CAAA;AACE,WAAA,IAAA,KAAA,IAAA;AACA,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;AACA,gBAAA;AACA,gBAAA;AACA,uBAAA;AACA,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;AACA,UAAA;;AAGF,CAAA;AACE,SAAA;AACA,aAAA;AACA,eAAA;AACA,eAAA;AACA,kBAAA;AACA,mBAAA;AACA,eAAA;AACA,cAAA;AACA,aAAA;;AAGF,CAAA;AACE,WAAA,KAAA,KAAA,KAAA;AACA,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;AACA,cAAA;AACA,UAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;AACA,cAAA;;AAGF,CAAA;AACE,SAAA;AACA,aAAA;AACA,eAAA;AACA,eAAA;AACA,kBAAA;AACA,mBAAA;AACA,eAAA;AACA,cAAA;AACA,aAAA;;AAIF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;AACA,SAAA;;AAGF,CAAA;AACE,WAAA,IAAA,KAAA,IAAA;AACA,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;AACA,gBAAA;AACA,gBAAA;AACA,uBAAA;AACA,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;AACA,UAAA;;AAGF,CAAA;AACE,SAAA;AACA,aAAA;AACA,eAAA;AACA,eAAA;AACA,kBAAA;AACA,mBAAA;AACA,eAAA;AACA,cAAA;AACA,aAAA;;AAGF,CAAA;AACE,WAAA,KAAA,KAAA,KAAA;AACA,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;AACA,cAAA;AACA,UAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;AACA,cAAA;;AAGF,CAAA;AACE,SAAA;AACA,aAAA;AACA,eAAA;AACA,eAAA;AACA,kBAAA;AACA,mBAAA;AACA,eAAA;AACA,cAAA;AACA,aAAA;;AAIF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;AACA,SAAA;;AAGF,CAAA;AACE,WAAA,IAAA,KAAA,IAAA;AACA,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;AACA,gBAAA;AACA,gBAAA;AACA,uBAAA;AACA,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;AACA,UAAA;;AAGF,CAAA;AACE,SAAA;AACA,aAAA;AACA,eAAA;AACA,eAAA;AACA,kBAAA;AACA,mBAAA;AACA,eAAA;AACA,cAAA;AACA,aAAA;;AAGF,CAAA;AACE,WAAA,KAAA,KAAA,KAAA;AACA,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;AACA,cAAA;AACA,UAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;AACA,cAAA;;AAGF,CAAA;AACE,SAAA;AACA,aAAA;AACA,eAAA;AACA,eAAA;AACA,kBAAA;AACA,mBAAA;AACA,eAAA;AACA,cAAA;AACA,aAAA;AACA,SAAA;;AAIF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;AACA,SAAA;;AAGF,CAAA;AACE,WAAA,IAAA,KAAA,IAAA;AACA,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;AACA,gBAAA;AACA,gBAAA;AACA,uBAAA;AACA,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;AACA,UAAA;;AAGF,CAAA;AACE,SAAA;AACA,aAAA;AACA,eAAA;AACA,eAAA;AACA,kBAAA;AACA,mBAAA;AACA,eAAA;AACA,cAAA;AACA,aAAA;;AAGF,CAAA;AACE,WAAA,KAAA,KAAA,KAAA;AACA,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;AACA,cAAA;AACA,UAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;AACA,cAAA;;AAGF,CAAA;AACE,SAAA;AACA,aAAA;AACA,eAAA;AACA,eAAA;AACA,kBAAA;AACA,mBAAA;AACA,eAAA;AACA,cAAA;AACA,aAAA;;AAGF,CAAA;AACE,WAAA,KAAA;AACA,cAAA;AACA,aAAA;AACA,eAAA,QAAA,EAAA;AACA,SAAA;AACA,cAAA;AACA,cAAA;;", "names": []}
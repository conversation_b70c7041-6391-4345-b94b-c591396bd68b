import {
  AssignedTableComponent,
  RefreshIconComponent
} from "./chunk-YLWVG4A5.js";
import {
  CalendarComponent,
  CheckboxComponent,
  DemographicsComponent,
  DropdownComponent,
  HitsComponent,
  NotesComponent,
  PdfViewerTestComponent,
  ResultsComponent
} from "./chunk-FVKW5FZS.js";
import {
  ButtonComponent,
  FormsModule,
  MenuComponent,
  NgControlStatus,
  NgModel,
  RequiredValidator
} from "./chunk-JRLQF6CE.js";
import {
  CommonModule,
  Component,
  ElementRef,
  NavigationEnd,
  Renderer2,
  Router,
  RouterOutlet,
  ViewChild,
  bootstrapApplication,
  filter,
  provideClientHydration,
  provideHttpClient,
  provideRouter,
  provideZoneChangeDetection,
  setClassMetadata,
  withEventReplay,
  withFetch,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵloadQuery,
  ɵɵproperty,
  ɵɵqueryRefresh,
  ɵɵtext,
  ɵɵtwoWayBindingSet,
  ɵɵtwoWayListener,
  ɵɵtwoWayProperty,
  ɵɵviewQuery
} from "./chunk-F5HTA5WY.js";
import {
  __spreadValues
} from "./chunk-PC6IZSQ2.js";

// src/app/shared/components/component-test/component-test.component.ts
var ComponentTestComponent = class _ComponentTestComponent {
  // Button test properties
  buttonClicked(type) {
    console.log(`${type} button clicked`);
  }
  // Form control test properties
  isChecked = false;
  selectedValue = null;
  selectedMultipleValues = [];
  selectedDate = "";
  notesText = "";
  resultsData = {
    category: "inclusions",
    telehealth: false,
    sys: "",
    dias: "",
    dateOfService: "",
    notes: ""
  };
  exclusionsData = {
    category: "exclusions",
    telehealth: true,
    sys: "",
    dias: "",
    dateOfService: "",
    notes: "Sample exclusion note"
  };
  // Demographics data
  demographicsData = {
    measureTitle: "Controlling Blood Pressure (CBP)",
    measureSubtitle: "Measure",
    memberId: "55820474",
    memberName: "John Dey",
    dateOfBirth: "01/05/1972",
    gender: "M",
    lob: "MAHMO",
    providerName: "Nicolas Dejong PA",
    npi: "882716229"
  };
  // Hits data
  hitsData = [
    {
      id: "hit-1",
      dateOfService: "07/21/24",
      systolic: 136,
      diastolic: 82,
      page: 2,
      comment: "",
      include: false
    },
    {
      id: "hit-2",
      dateOfService: "07/21/24",
      systolic: 140,
      diastolic: 82,
      page: 2,
      comment: "",
      include: false
    },
    {
      id: "hit-3",
      dateOfService: "05/21/24",
      systolic: 150,
      diastolic: 90,
      page: 7,
      comment: "",
      include: false
    }
  ];
  // Dropdown options
  reasoningOptions = [
    { value: "acute-inpatient", label: "Acute inpatient and ED visit" },
    { value: "end-stage-renal", label: "End-stage renal disease" },
    { value: "frailty", label: "Frailty: Member 81+ years as of 12/31 of the MY" },
    { value: "lidocaine", label: "Lidocaine and Epinephrine given to patient" },
    { value: "medicare-isnp", label: "Medicare member in an Institutional SNP (I-SNP)" },
    { value: "medicare-ltc", label: "Medicare member living in long-term care" },
    { value: "member-66-80", label: "Member 66-80 years as of 12/31 of the MY" },
    { value: "member-died", label: "Member died during the MY" },
    { value: "hospice", label: "Member in hospice anytime during the MY" },
    { value: "non-acute", label: "Non-acute inpatient admission" },
    { value: "palliative", label: "Palliative Care" },
    { value: "pregnancy", label: "Pregnancy" },
    { value: "other", label: "Other" }
  ];
  // Navigation data
  userProfile = {
    name: "Jane Chu",
    avatar: ""
  };
  menuItems = [
    { label: "Profile", route: "/profile", icon: "\u{1F464}" },
    { label: "Settings", route: "/settings", icon: "\u2699\uFE0F" },
    { label: "Help", route: "/help", icon: "\u2753" },
    { label: "Logout", action: () => this.logout(), icon: "\u{1F6AA}" }
  ];
  // Icon names for testing
  // iconNames: IconName[] = ['refresh', 'back', 'user', 'arrow-down', 'arrow-up', 'arrow-left', 'arrow-right', 'check', 'close', 'search', 'filter', 'sort']; // Temporarily disabled
  onCheckboxChange(checked) {
    this.isChecked = checked;
  }
  onDropdownChange(value) {
    this.selectedValue = value;
    console.log("Dropdown selection changed:", value);
  }
  onMultiDropdownChange(values) {
    this.selectedMultipleValues = values;
    console.log("Multi-select dropdown changed:", values);
  }
  onDateChange(date) {
    this.selectedDate = date;
    console.log("Date changed:", date);
  }
  onNotesChange(notes) {
    this.notesText = notes;
    console.log("Notes changed:", notes);
  }
  onResultsDataChange(data) {
    this.resultsData = data;
    console.log("Results data changed:", data);
  }
  onResultsTabChange(tab) {
    console.log("Results tab changed:", tab);
  }
  onDemographicsBackClick() {
    console.log("Demographics back button clicked");
  }
  onHitsDataChange(data) {
    this.hitsData = data;
    console.log("Hits data changed:", data);
  }
  onHitsPageClick(event) {
    console.log("Hits page clicked:", event);
  }
  onHitsCommentChange(event) {
    console.log("Hits comment changed:", event);
  }
  onHitsIncludeChange(event) {
    console.log("Hits include changed:", event);
  }
  // Sample data for assigned table
  sampleCharts = [
    {
      memberId: "55820474",
      firstName: "John",
      middleName: "",
      lastName: "Dey",
      fullName: "John Dey",
      dob: "01/05/1972",
      lob: "MA HMO",
      measure: "CBP",
      measureKey: "CBP",
      gender: "M",
      filename: "CBP_Redacted_John_Dey",
      provider: {
        npi: "882716229",
        firstName: "Nicolas",
        lastName: "DeJong",
        fullName: "Nicolas DeJong"
      },
      review1: "Jane Chu",
      review2: "-",
      assigned: "04/15/25 1:30pm",
      status: "Review"
    },
    {
      memberId: "302274401",
      firstName: "Alma",
      middleName: "G",
      lastName: "Anders",
      fullName: "Alma G Anders",
      dob: "12/15/1953",
      lob: "MA HMO",
      measure: "CBP",
      measureKey: "CBP",
      gender: "F",
      filename: "CBP_Redacted_Alma_Anders",
      provider: {
        npi: "771552845",
        firstName: "Thomas",
        lastName: "Ramos",
        fullName: "Thomas Ramos"
      },
      review1: "Jane Chu",
      review2: "-",
      assigned: "04/15/25 1:30pm",
      status: "Inactive"
    },
    {
      memberId: "**********",
      firstName: "Joanne",
      middleName: "",
      lastName: "Smith",
      fullName: "Joanne Smith",
      dob: "06/30/1951",
      lob: "MA HMO",
      measure: "CBP",
      measureKey: "CBP",
      gender: "F",
      filename: "CBP_Redacted_Joanne_Smith",
      provider: {
        npi: "104297254",
        firstName: "Samantha",
        lastName: "Peterson",
        fullName: "Samantha Peterson"
      },
      review1: "Jane Chu",
      review2: "-",
      assigned: "04/15/25 1:30pm",
      status: "Inactive"
    }
  ];
  // Navigation event handlers
  onLogoClick() {
    console.log("Logo clicked");
  }
  onUserClick() {
    console.log("User clicked");
  }
  onDropdownToggle(isOpen) {
    console.log("Dropdown toggled:", isOpen);
  }
  onMenuItemClick(item) {
    console.log("Menu item clicked:", item);
  }
  logout() {
    console.log("Logout clicked");
  }
  static \u0275fac = function ComponentTestComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ComponentTestComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _ComponentTestComponent, selectors: [["app-component-test"]], decls: 288, vars: 28, consts: [[1, "component-test-container"], [1, "page-title"], [1, "page-description"], [1, "component-section"], [1, "section-title"], [1, "section-description"], [1, "component-row"], [1, "component-item"], [1, "component-title"], [1, "component-subtitle"], [1, "component-demo", "figma-sized", "button-demo"], ["variant", "primary", "figmaState", "inactive", 3, "figmaExact"], [1, "component-code"], ["variant", "primary", "figmaState", "default", 3, "click", "figmaExact"], ["variant", "primary", "figmaState", "hover", 3, "click", "figmaExact"], ["variant", "primary", "figmaState", "click", 3, "click", "figmaExact"], ["variant", "secondary", "figmaState", "default", 3, "click", "figmaExact"], ["color", "default"], ["variant", "secondary", "figmaState", "hover", 3, "click", "figmaExact"], ["color", "hover"], ["variant", "secondary", "figmaState", "click", 3, "click", "figmaExact"], ["color", "click"], [1, "subsection-title"], [1, "subsection-description"], ["variant", "primary", 3, "click", "figmaExact"], ["variant", "secondary", 3, "click", "figmaExact"], [1, "component-demo", "figma-sized", "checkbox-demo"], ["label", "Checkbox", 3, "ngModelChange", "ngModel"], [1, "component-demo", "figma-sized", "form-demo"], ["label", "Reasoning", "placeholder", "Select reasoning", 3, "ngModelChange", "selectionChange", "options", "ngModel"], ["label", "Multiple Reasoning", "placeholder", "Select multiple", 3, "ngModelChange", "selectionChange", "options", "multiSelect", "ngModel"], ["label", "Date of Service", "placeholder", "Date of Service", 3, "ngModelChange", "dateChange", "ngModel"], ["label", "Required Date", "placeholder", "Select date", 3, "ngModelChange", "dateChange", "required", "ngModel"], [1, "component-demo", "figma-sized", "results-demo"], ["title", "Results", 3, "ngModelChange", "dataChange", "tabChange", "ngModel"], ["title", "Findings", 3, "ngModelChange", "ngModel"], [1, "component-row", "full-width"], [1, "component-item", "full-width"], [1, "component-demo", "table-demo"], [3, "charts"], ["label", "Notes", "placeholder", "Notes", 3, "ngModelChange", "notesChange", "ngModel"], ["label", "Notes", "placeholder", "Notes", 3, "ngModelChange", "maxLength", "ngModel"], [1, "component-row", "demographics-row", "full-width"], [1, "component-demo", "figma-sized", "demographics-demo"], [3, "backClick", "data"], [1, "component-demo", "figma-sized", "hits-demo"], ["title", "Hits", 3, "dataChange", "pageClick", "commentChange", "includeChange", "data"], [1, "component-demo", "figma-sized", "menu-demo"], ["logoSrc", "assets/logos/Stellarus_logo_2C_blacktype.png?v=2024", "logoAlt", "Stellarus Logo", 3, "logoClick", "userClick", "dropdownToggle", "menuItemClick", "user", "menuItems"]], template: function ComponentTestComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "h1", 1);
      \u0275\u0275text(2, "Component Test Page");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(3, "p", 2);
      \u0275\u0275text(4, "This page showcases all implemented components with their different states and variations.");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(5, "section", 3)(6, "h2", 4);
      \u0275\u0275text(7, "Buttons");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(8, "p", 5);
      \u0275\u0275text(9, " The following demos show the four button states from the Figma style guide: ");
      \u0275\u0275elementStart(10, "strong");
      \u0275\u0275text(11, "Inactive");
      \u0275\u0275elementEnd();
      \u0275\u0275text(12, ", ");
      \u0275\u0275elementStart(13, "strong");
      \u0275\u0275text(14, "Default");
      \u0275\u0275elementEnd();
      \u0275\u0275text(15, ", ");
      \u0275\u0275elementStart(16, "strong");
      \u0275\u0275text(17, "Hover");
      \u0275\u0275elementEnd();
      \u0275\u0275text(18, ", and ");
      \u0275\u0275elementStart(19, "strong");
      \u0275\u0275text(20, "Click");
      \u0275\u0275elementEnd();
      \u0275\u0275text(21, ". Static state demos show each state individually, while interactive demos show natural state transitions on hover and click. ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(22, "div", 6)(23, "div", 7)(24, "h3", 8);
      \u0275\u0275text(25, "Primary Button - Inactive");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(26, "div", 9);
      \u0275\u0275text(27, 'Style Guide: "Buttons, selectors, and icons" | Implementation: "ButtonComponent"');
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(28, "div", 10)(29, "app-button", 11);
      \u0275\u0275text(30, "Submit");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(31, "div", 12)(32, "pre")(33, "code");
      \u0275\u0275text(34, '<app-button variant="primary" figmaState="inactive">Submit</app-button>');
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(35, "div", 7)(36, "h3", 8);
      \u0275\u0275text(37, "Primary Button - Default");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(38, "div", 9);
      \u0275\u0275text(39, 'Style Guide: "Buttons, selectors, and icons" | Implementation: "ButtonComponent"');
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(40, "div", 10)(41, "app-button", 13);
      \u0275\u0275listener("click", function ComponentTestComponent_Template_app_button_click_41_listener() {
        return ctx.buttonClicked("primary-default");
      });
      \u0275\u0275text(42, "Submit");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(43, "div", 12)(44, "pre")(45, "code");
      \u0275\u0275text(46, '<app-button variant="primary" figmaState="default">Submit</app-button>');
      \u0275\u0275elementEnd()()()()();
      \u0275\u0275elementStart(47, "div", 6)(48, "div", 7)(49, "h3", 8);
      \u0275\u0275text(50, "Primary Button - Hover");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(51, "div", 9);
      \u0275\u0275text(52, 'Style Guide: "Buttons, selectors, and icons" | Implementation: "ButtonComponent"');
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(53, "div", 10)(54, "app-button", 14);
      \u0275\u0275listener("click", function ComponentTestComponent_Template_app_button_click_54_listener() {
        return ctx.buttonClicked("primary-hover");
      });
      \u0275\u0275text(55, "Submit");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(56, "div", 12)(57, "pre")(58, "code");
      \u0275\u0275text(59, '<app-button variant="primary" figmaState="hover">Submit</app-button>');
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(60, "div", 7)(61, "h3", 8);
      \u0275\u0275text(62, "Primary Button - Click");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(63, "div", 9);
      \u0275\u0275text(64, 'Style Guide: "Buttons, selectors, and icons" | Implementation: "ButtonComponent"');
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(65, "div", 10)(66, "app-button", 15);
      \u0275\u0275listener("click", function ComponentTestComponent_Template_app_button_click_66_listener() {
        return ctx.buttonClicked("primary-click");
      });
      \u0275\u0275text(67, "Submit");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(68, "div", 12)(69, "pre")(70, "code");
      \u0275\u0275text(71, '<app-button variant="primary" figmaState="click">Submit</app-button>');
      \u0275\u0275elementEnd()()()()();
      \u0275\u0275elementStart(72, "div", 6)(73, "div", 7)(74, "h3", 8);
      \u0275\u0275text(75, "Secondary Button - Default");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(76, "div", 9);
      \u0275\u0275text(77, 'Style Guide: "Buttons, selectors, and icons" | Implementation: "ButtonComponent"');
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(78, "div", 10)(79, "app-button", 16);
      \u0275\u0275listener("click", function ComponentTestComponent_Template_app_button_click_79_listener() {
        return ctx.buttonClicked("secondary-default");
      });
      \u0275\u0275element(80, "app-refresh-icon", 17);
      \u0275\u0275text(81, "Refresh charts ");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(82, "div", 12)(83, "pre")(84, "code");
      \u0275\u0275text(85, '<app-button variant="secondary" figmaState="default">Refresh charts</app-button>');
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(86, "div", 7)(87, "h3", 8);
      \u0275\u0275text(88, "Secondary Button - Hover");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(89, "div", 9);
      \u0275\u0275text(90, 'Style Guide: "Buttons, selectors, and icons" | Implementation: "ButtonComponent"');
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(91, "div", 10)(92, "app-button", 18);
      \u0275\u0275listener("click", function ComponentTestComponent_Template_app_button_click_92_listener() {
        return ctx.buttonClicked("secondary-hover");
      });
      \u0275\u0275element(93, "app-refresh-icon", 19);
      \u0275\u0275text(94, "Refresh charts ");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(95, "div", 12)(96, "pre")(97, "code");
      \u0275\u0275text(98, '<app-button variant="secondary" figmaState="hover">Refresh charts</app-button>');
      \u0275\u0275elementEnd()()()()();
      \u0275\u0275elementStart(99, "div", 6)(100, "div", 7)(101, "h3", 8);
      \u0275\u0275text(102, "Secondary Button - Click");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(103, "div", 9);
      \u0275\u0275text(104, 'Style Guide: "Buttons, selectors, and icons" | Implementation: "ButtonComponent"');
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(105, "div", 10)(106, "app-button", 20);
      \u0275\u0275listener("click", function ComponentTestComponent_Template_app_button_click_106_listener() {
        return ctx.buttonClicked("secondary-click");
      });
      \u0275\u0275element(107, "app-refresh-icon", 21);
      \u0275\u0275text(108, "Refresh charts ");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(109, "div", 12)(110, "pre")(111, "code");
      \u0275\u0275text(112, '<app-button variant="secondary" figmaState="click">Refresh charts</app-button>');
      \u0275\u0275elementEnd()()()()();
      \u0275\u0275elementStart(113, "h3", 22);
      \u0275\u0275text(114, "Interactive Button Demos");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(115, "p", 23);
      \u0275\u0275text(116, "These buttons demonstrate natural state transitions. Hover and click to see the Figma-specified color changes.");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(117, "div", 6)(118, "div", 7)(119, "h3", 8);
      \u0275\u0275text(120, "Interactive Primary Button Demo");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(121, "div", 9);
      \u0275\u0275text(122, "Hover and click to see natural state transitions");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(123, "div", 10)(124, "app-button", 24);
      \u0275\u0275listener("click", function ComponentTestComponent_Template_app_button_click_124_listener() {
        return ctx.buttonClicked("interactive-primary");
      });
      \u0275\u0275text(125, "Submit");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(126, "div", 12)(127, "pre")(128, "code");
      \u0275\u0275text(129, '<app-button variant="primary" [figmaExact]="true">Submit</app-button>');
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(130, "div", 7)(131, "h3", 8);
      \u0275\u0275text(132, "Interactive Secondary Button Demo");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(133, "div", 9);
      \u0275\u0275text(134, "Hover and click to see natural state transitions");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(135, "div", 10)(136, "app-button", 25);
      \u0275\u0275listener("click", function ComponentTestComponent_Template_app_button_click_136_listener() {
        return ctx.buttonClicked("interactive-secondary");
      });
      \u0275\u0275element(137, "app-refresh-icon");
      \u0275\u0275text(138, "Refresh charts ");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(139, "div", 12)(140, "pre")(141, "code");
      \u0275\u0275text(142, '<app-button variant="secondary" [figmaExact]="true">Refresh charts</app-button>');
      \u0275\u0275elementEnd()()()()()();
      \u0275\u0275elementStart(143, "section", 3)(144, "h2", 4);
      \u0275\u0275text(145, "Form Controls");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(146, "div", 6)(147, "div", 7)(148, "h3", 8);
      \u0275\u0275text(149, "Checkbox");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(150, "div", 9);
      \u0275\u0275text(151, 'Style Guide: "Buttons, selectors, and icons" | Implementation: "CheckboxComponent"');
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(152, "div", 26)(153, "app-checkbox", 27);
      \u0275\u0275twoWayListener("ngModelChange", function ComponentTestComponent_Template_app_checkbox_ngModelChange_153_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.isChecked, $event) || (ctx.isChecked = $event);
        return $event;
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(154, "div", 12)(155, "pre")(156, "code");
      \u0275\u0275text(157, '<app-checkbox label="Checkbox"></app-checkbox>');
      \u0275\u0275elementEnd()()()()();
      \u0275\u0275elementStart(158, "h3", 22);
      \u0275\u0275text(159, "Advanced Form Controls");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(160, "div", 6)(161, "div", 7)(162, "h3", 8);
      \u0275\u0275text(163, "Dropdown (Single Select)");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(164, "div", 28)(165, "app-dropdown", 29);
      \u0275\u0275twoWayListener("ngModelChange", function ComponentTestComponent_Template_app_dropdown_ngModelChange_165_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.selectedValue, $event) || (ctx.selectedValue = $event);
        return $event;
      });
      \u0275\u0275listener("selectionChange", function ComponentTestComponent_Template_app_dropdown_selectionChange_165_listener($event) {
        return ctx.onDropdownChange($event);
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(166, "div", 12)(167, "pre")(168, "code");
      \u0275\u0275text(169, '<app-dropdown label="Reasoning" [options]="options"></app-dropdown>');
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(170, "div", 7)(171, "h3", 8);
      \u0275\u0275text(172, "Dropdown (Multi Select)");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(173, "div", 28)(174, "app-dropdown", 30);
      \u0275\u0275twoWayListener("ngModelChange", function ComponentTestComponent_Template_app_dropdown_ngModelChange_174_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.selectedMultipleValues, $event) || (ctx.selectedMultipleValues = $event);
        return $event;
      });
      \u0275\u0275listener("selectionChange", function ComponentTestComponent_Template_app_dropdown_selectionChange_174_listener($event) {
        return ctx.onMultiDropdownChange($event);
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(175, "div", 12)(176, "pre")(177, "code");
      \u0275\u0275text(178, '<app-dropdown [multiSelect]="true" [options]="options"></app-dropdown>');
      \u0275\u0275elementEnd()()()()();
      \u0275\u0275elementStart(179, "div", 6)(180, "div", 7)(181, "h3", 8);
      \u0275\u0275text(182, "Calendar");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(183, "div", 28)(184, "app-calendar", 31);
      \u0275\u0275twoWayListener("ngModelChange", function ComponentTestComponent_Template_app_calendar_ngModelChange_184_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.selectedDate, $event) || (ctx.selectedDate = $event);
        return $event;
      });
      \u0275\u0275listener("dateChange", function ComponentTestComponent_Template_app_calendar_dateChange_184_listener($event) {
        return ctx.onDateChange($event);
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(185, "div", 12)(186, "pre")(187, "code");
      \u0275\u0275text(188, '<app-calendar label="Date of Service"></app-calendar>');
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(189, "div", 7)(190, "h3", 8);
      \u0275\u0275text(191, "Calendar (Required)");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(192, "div", 28)(193, "app-calendar", 32);
      \u0275\u0275twoWayListener("ngModelChange", function ComponentTestComponent_Template_app_calendar_ngModelChange_193_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.selectedDate, $event) || (ctx.selectedDate = $event);
        return $event;
      });
      \u0275\u0275listener("dateChange", function ComponentTestComponent_Template_app_calendar_dateChange_193_listener($event) {
        return ctx.onDateChange($event);
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(194, "div", 12)(195, "pre")(196, "code");
      \u0275\u0275text(197, '<app-calendar [required]="true"></app-calendar>');
      \u0275\u0275elementEnd()()()()();
      \u0275\u0275elementStart(198, "div", 6)(199, "div", 7)(200, "h3", 8);
      \u0275\u0275text(201, "Results");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(202, "div", 33)(203, "app-results", 34);
      \u0275\u0275twoWayListener("ngModelChange", function ComponentTestComponent_Template_app_results_ngModelChange_203_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.resultsData, $event) || (ctx.resultsData = $event);
        return $event;
      });
      \u0275\u0275listener("dataChange", function ComponentTestComponent_Template_app_results_dataChange_203_listener($event) {
        return ctx.onResultsDataChange($event);
      })("tabChange", function ComponentTestComponent_Template_app_results_tabChange_203_listener($event) {
        return ctx.onResultsTabChange($event);
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(204, "div", 12)(205, "pre")(206, "code");
      \u0275\u0275text(207, '<app-results title="Results"></app-results>');
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(208, "div", 7)(209, "h3", 8);
      \u0275\u0275text(210, "Results (Exclusions Tab)");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(211, "div", 33)(212, "app-results", 35);
      \u0275\u0275twoWayListener("ngModelChange", function ComponentTestComponent_Template_app_results_ngModelChange_212_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.exclusionsData, $event) || (ctx.exclusionsData = $event);
        return $event;
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(213, "div", 12)(214, "pre")(215, "code");
      \u0275\u0275text(216, '<app-results title="Findings" [ngModel]="exclusionsData"></app-results>');
      \u0275\u0275elementEnd()()()()()();
      \u0275\u0275elementStart(217, "section", 3)(218, "h2", 4);
      \u0275\u0275text(219, "Tables");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(220, "div", 36)(221, "div", 37)(222, "h3", 8);
      \u0275\u0275text(223, "Assigned Table (Dashboard Table)");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(224, "div", 38);
      \u0275\u0275element(225, "app-assigned-table", 39);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(226, "div", 12)(227, "pre")(228, "code");
      \u0275\u0275text(229, '<app-assigned-table [charts]="charts"></app-assigned-table>');
      \u0275\u0275elementEnd()()()()()();
      \u0275\u0275elementStart(230, "section", 3)(231, "h2", 4);
      \u0275\u0275text(232, "Phase 2 Components");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(233, "div", 6)(234, "div", 7)(235, "h3", 8);
      \u0275\u0275text(236, "Notes");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(237, "div", 9);
      \u0275\u0275text(238, 'Style Guide: "Notes" | Implementation: "NotesComponent"');
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(239, "div", 28)(240, "app-notes", 40);
      \u0275\u0275twoWayListener("ngModelChange", function ComponentTestComponent_Template_app_notes_ngModelChange_240_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.notesText, $event) || (ctx.notesText = $event);
        return $event;
      });
      \u0275\u0275listener("notesChange", function ComponentTestComponent_Template_app_notes_notesChange_240_listener($event) {
        return ctx.onNotesChange($event);
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(241, "div", 12)(242, "pre")(243, "code");
      \u0275\u0275text(244, '<app-notes label="Notes" [(ngModel)]="notesText"></app-notes>');
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(245, "div", 7)(246, "h3", 8);
      \u0275\u0275text(247, "Notes (Limited)");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(248, "div", 9);
      \u0275\u0275text(249, 'Style Guide: "Notes" | Implementation: "NotesComponent"');
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(250, "div", 28)(251, "app-notes", 41);
      \u0275\u0275twoWayListener("ngModelChange", function ComponentTestComponent_Template_app_notes_ngModelChange_251_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.notesText, $event) || (ctx.notesText = $event);
        return $event;
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(252, "div", 12)(253, "pre")(254, "code");
      \u0275\u0275text(255, '<app-notes [maxLength]="100"></app-notes>');
      \u0275\u0275elementEnd()()()()();
      \u0275\u0275elementStart(256, "div", 42)(257, "div", 37)(258, "h3", 8);
      \u0275\u0275text(259, "Demographics");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(260, "div", 43)(261, "app-demographics", 44);
      \u0275\u0275listener("backClick", function ComponentTestComponent_Template_app_demographics_backClick_261_listener() {
        return ctx.onDemographicsBackClick();
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(262, "div", 12)(263, "pre")(264, "code");
      \u0275\u0275text(265, '<app-demographics [data]="demographicsData"></app-demographics>');
      \u0275\u0275elementEnd()()()()();
      \u0275\u0275elementStart(266, "div", 36)(267, "div", 37)(268, "h3", 8);
      \u0275\u0275text(269, "Hits");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(270, "div", 9);
      \u0275\u0275text(271, 'Style Guide: "Hits" | Implementation: "HitsComponent"');
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(272, "div", 45)(273, "app-hits", 46);
      \u0275\u0275listener("dataChange", function ComponentTestComponent_Template_app_hits_dataChange_273_listener($event) {
        return ctx.onHitsDataChange($event);
      })("pageClick", function ComponentTestComponent_Template_app_hits_pageClick_273_listener($event) {
        return ctx.onHitsPageClick($event);
      })("commentChange", function ComponentTestComponent_Template_app_hits_commentChange_273_listener($event) {
        return ctx.onHitsCommentChange($event);
      })("includeChange", function ComponentTestComponent_Template_app_hits_includeChange_273_listener($event) {
        return ctx.onHitsIncludeChange($event);
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(274, "div", 12)(275, "pre")(276, "code");
      \u0275\u0275text(277, '<app-hits title="Hits" [data]="hitsData"></app-hits>');
      \u0275\u0275elementEnd()()()()();
      \u0275\u0275elementStart(278, "div", 36)(279, "div", 37)(280, "h3", 8);
      \u0275\u0275text(281, "Menu");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(282, "div", 47)(283, "app-menu", 48);
      \u0275\u0275listener("logoClick", function ComponentTestComponent_Template_app_menu_logoClick_283_listener() {
        return ctx.onLogoClick();
      })("userClick", function ComponentTestComponent_Template_app_menu_userClick_283_listener() {
        return ctx.onUserClick();
      })("dropdownToggle", function ComponentTestComponent_Template_app_menu_dropdownToggle_283_listener($event) {
        return ctx.onDropdownToggle($event);
      })("menuItemClick", function ComponentTestComponent_Template_app_menu_menuItemClick_283_listener($event) {
        return ctx.onMenuItemClick($event);
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(284, "div", 12)(285, "pre")(286, "code");
      \u0275\u0275text(287, '<app-menu [user]="userProfile" [menuItems]="menuItems"></app-menu>');
      \u0275\u0275elementEnd()()()()()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(29);
      \u0275\u0275property("figmaExact", true);
      \u0275\u0275advance(12);
      \u0275\u0275property("figmaExact", true);
      \u0275\u0275advance(13);
      \u0275\u0275property("figmaExact", true);
      \u0275\u0275advance(12);
      \u0275\u0275property("figmaExact", true);
      \u0275\u0275advance(13);
      \u0275\u0275property("figmaExact", true);
      \u0275\u0275advance(13);
      \u0275\u0275property("figmaExact", true);
      \u0275\u0275advance(14);
      \u0275\u0275property("figmaExact", true);
      \u0275\u0275advance(18);
      \u0275\u0275property("figmaExact", true);
      \u0275\u0275advance(12);
      \u0275\u0275property("figmaExact", true);
      \u0275\u0275advance(17);
      \u0275\u0275twoWayProperty("ngModel", ctx.isChecked);
      \u0275\u0275advance(12);
      \u0275\u0275property("options", ctx.reasoningOptions);
      \u0275\u0275twoWayProperty("ngModel", ctx.selectedValue);
      \u0275\u0275advance(9);
      \u0275\u0275property("options", ctx.reasoningOptions)("multiSelect", true);
      \u0275\u0275twoWayProperty("ngModel", ctx.selectedMultipleValues);
      \u0275\u0275advance(10);
      \u0275\u0275twoWayProperty("ngModel", ctx.selectedDate);
      \u0275\u0275advance(9);
      \u0275\u0275property("required", true);
      \u0275\u0275twoWayProperty("ngModel", ctx.selectedDate);
      \u0275\u0275advance(10);
      \u0275\u0275twoWayProperty("ngModel", ctx.resultsData);
      \u0275\u0275advance(9);
      \u0275\u0275twoWayProperty("ngModel", ctx.exclusionsData);
      \u0275\u0275advance(13);
      \u0275\u0275property("charts", ctx.sampleCharts);
      \u0275\u0275advance(15);
      \u0275\u0275twoWayProperty("ngModel", ctx.notesText);
      \u0275\u0275advance(11);
      \u0275\u0275property("maxLength", 100);
      \u0275\u0275twoWayProperty("ngModel", ctx.notesText);
      \u0275\u0275advance(10);
      \u0275\u0275property("data", ctx.demographicsData);
      \u0275\u0275advance(12);
      \u0275\u0275property("data", ctx.hitsData);
      \u0275\u0275advance(10);
      \u0275\u0275property("user", ctx.userProfile)("menuItems", ctx.menuItems);
    }
  }, dependencies: [
    CommonModule,
    FormsModule,
    NgControlStatus,
    RequiredValidator,
    NgModel,
    ButtonComponent,
    CheckboxComponent,
    DropdownComponent,
    CalendarComponent,
    ResultsComponent,
    AssignedTableComponent,
    NotesComponent,
    DemographicsComponent,
    HitsComponent,
    MenuComponent,
    // IconComponent, // Temporarily disabled
    RefreshIconComponent
  ], styles: ['\n\n.component-test-container[_ngcontent-%COMP%] {\n  max-width: 1600px;\n  margin: 0 auto;\n  padding: 30px;\n  background-color: #F6F6F6;\n}\n.page-title[_ngcontent-%COMP%] {\n  font-size: 24px;\n  font-weight: 600;\n  line-height: 32px;\n  color: #17181A;\n  margin-bottom: 16px;\n}\n.page-description[_ngcontent-%COMP%] {\n  font-size: 14px;\n  font-weight: 300;\n  line-height: 20px;\n  color: #17181A;\n  margin-bottom: 30px;\n}\n.component-section[_ngcontent-%COMP%] {\n  background: #FFFFFF;\n  border-radius: 8px;\n  outline: 1px #F1F5F7 solid;\n  outline-offset: -1px;\n  padding: 20px;\n  margin-bottom: 20px;\n  margin-bottom: 30px;\n}\n.section-title[_ngcontent-%COMP%] {\n  font-size: 20px;\n  font-weight: 600;\n  line-height: 32px;\n  color: #17181A;\n  margin-bottom: 20px;\n}\n.section-description[_ngcontent-%COMP%] {\n  font-size: 14px;\n  color: #547996;\n  margin-bottom: 24px;\n  line-height: 1.5;\n}\n.subsection-description[_ngcontent-%COMP%] {\n  font-size: 14px;\n  color: #547996;\n  margin-bottom: 16px;\n  line-height: 1.5;\n}\n.subsection-title[_ngcontent-%COMP%] {\n  font-size: 16px;\n  font-weight: 600;\n  line-height: 24px;\n  color: #17181A;\n  margin: 24px 0 16px 0;\n  padding-left: 12px;\n  border-left: 3px solid #3870B8;\n}\n.component-subtitle[_ngcontent-%COMP%] {\n  font-size: 11px;\n  font-weight: 300;\n  line-height: 16px;\n  color: #547996;\n  margin-bottom: 12px;\n  font-style: italic;\n}\n.component-row[_ngcontent-%COMP%] {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 20px;\n  margin-bottom: 40px;\n  min-width: 100%;\n}\n.component-row[_ngcontent-%COMP%]:last-child {\n  margin-bottom: 0;\n}\n.component-row.demographics-row[_ngcontent-%COMP%] {\n  flex-wrap: nowrap;\n  overflow-x: visible;\n}\n.component-item[_ngcontent-%COMP%] {\n  flex: 1;\n  min-width: 300px;\n}\n.component-item.full-width[_ngcontent-%COMP%] {\n  flex: 0 0 100%;\n}\n.component-title[_ngcontent-%COMP%] {\n  font-size: 14px;\n  font-weight: 500;\n  line-height: 20px;\n  color: #17181A;\n  margin-bottom: 12px;\n}\n.component-demo[_ngcontent-%COMP%] {\n  padding: 20px;\n  background-color: #FFFFFF;\n  border-radius: 8px;\n  outline: 1px #F1F5F7 solid;\n  outline-offset: -1px;\n  margin-bottom: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 80px;\n  overflow-x: visible;\n}\n.component-demo.table-demo[_ngcontent-%COMP%] {\n  justify-content: flex-start;\n  align-items: flex-start;\n  padding: 0;\n  display: block;\n}\n.component-demo.figma-sized[_ngcontent-%COMP%] {\n  justify-content: flex-start;\n  align-items: flex-start;\n}\n.component-demo.figma-sized.button-demo[_ngcontent-%COMP%] {\n  width: fit-content;\n  min-width: auto;\n  padding: 20px;\n}\n.component-demo.figma-sized.form-demo[_ngcontent-%COMP%] {\n  width: 300px;\n  min-width: 300px;\n  min-height: 400px;\n  padding: 20px 20px 60px 20px;\n  overflow: visible;\n}\n.component-demo.figma-sized.form-demo[_ngcontent-%COMP%]   app-dropdown[_ngcontent-%COMP%], \n.component-demo.figma-sized.form-demo[_ngcontent-%COMP%]   app-date-picker[_ngcontent-%COMP%], \n.component-demo.figma-sized.form-demo[_ngcontent-%COMP%]   app-notes[_ngcontent-%COMP%] {\n  width: 100%;\n}\n.component-demo.figma-sized.form-demo[_ngcontent-%COMP%]   app-dropdown[_ngcontent-%COMP%], \n.component-demo.figma-sized.form-demo[_ngcontent-%COMP%]   app-date-picker[_ngcontent-%COMP%] {\n  position: relative;\n  z-index: 10;\n}\n.component-demo.figma-sized.hits-demo[_ngcontent-%COMP%] {\n  width: 557px;\n  min-width: 557px;\n  padding: 20px;\n}\n.component-demo.figma-sized.hits-demo[_ngcontent-%COMP%]   app-hits[_ngcontent-%COMP%] {\n  width: 517px;\n}\n.component-demo.figma-sized.demographics-demo[_ngcontent-%COMP%] {\n  width: 100%;\n  min-width: 1420px;\n  padding: 20px;\n  overflow-x: visible;\n}\n.component-demo.figma-sized.results-demo[_ngcontent-%COMP%] {\n  width: 400px;\n  min-width: 400px;\n  padding: 20px;\n}\n.component-demo.figma-sized.menu-demo[_ngcontent-%COMP%] {\n  width: 100%;\n  padding: 0;\n}\n.component-demo.figma-sized.status-demo[_ngcontent-%COMP%] {\n  width: fit-content;\n  min-width: auto;\n  padding: 20px;\n}\n.component-demo.figma-sized.checkbox-demo[_ngcontent-%COMP%] {\n  width: fit-content;\n  min-width: auto;\n  padding: 20px;\n}\n.component-code[_ngcontent-%COMP%] {\n  background-color: #F9FBFC;\n  border-radius: 8px;\n  padding: 12px;\n  overflow: auto;\n}\n.component-code[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\n  margin: 0;\n  font-family: monospace;\n  font-size: 12px;\n  line-height: 1.5;\n  color: #384455;\n}\n.full-width[_ngcontent-%COMP%] {\n  width: 100%;\n}\n@media (min-width: 600px) {\n  .component-row[_ngcontent-%COMP%] {\n    flex-wrap: nowrap;\n  }\n}\n@media (max-width: 599px) {\n  .component-item[_ngcontent-%COMP%] {\n    flex: 0 0 100%;\n  }\n}\n.icons-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\n  gap: 16px;\n  padding: 16px;\n}\n.icon-demo-item[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 8px;\n  padding: 12px;\n  border: 1px solid #F1F5F7;\n  border-radius: 8px;\n  transition: background-color 0.2s ease;\n}\n.icon-demo-item[_ngcontent-%COMP%]:hover {\n  background-color: #F1F5F7;\n}\n.icon-label[_ngcontent-%COMP%] {\n  font-size: 10px;\n  font-family: "Urbane", sans-serif;\n  font-weight: 300;\n  color: #547996;\n  text-align: center;\n}\n/*# sourceMappingURL=component-test.component.css.map */'] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ComponentTestComponent, [{
    type: Component,
    args: [{ selector: "app-component-test", standalone: true, imports: [
      CommonModule,
      FormsModule,
      ButtonComponent,
      CheckboxComponent,
      DropdownComponent,
      CalendarComponent,
      ResultsComponent,
      AssignedTableComponent,
      NotesComponent,
      DemographicsComponent,
      HitsComponent,
      MenuComponent,
      // IconComponent, // Temporarily disabled
      RefreshIconComponent
    ], template: `<div class="component-test-container">\r
  <h1 class="page-title">Component Test Page</h1>\r
  <p class="page-description">This page showcases all implemented components with their different states and variations.</p>\r
\r
  <!-- Buttons Section -->\r
  <section class="component-section">\r
    <h2 class="section-title">Buttons</h2>\r
    <p class="section-description">\r
      The following demos show the four button states from the Figma style guide: <strong>Inactive</strong>, <strong>Default</strong>, <strong>Hover</strong>, and <strong>Click</strong>.\r
      Static state demos show each state individually, while interactive demos show natural state transitions on hover and click.\r
    </p>\r
    <div class="component-row">\r
      <div class="component-item">\r
        <h3 class="component-title">Primary Button - Inactive</h3>\r
        <div class="component-subtitle">Style Guide: "Buttons, selectors, and icons" | Implementation: "ButtonComponent"</div>\r
        <div class="component-demo figma-sized button-demo">\r
          <app-button\r
            variant="primary"\r
            [figmaExact]="true"\r
            figmaState="inactive"\r
          >Submit</app-button>\r
        </div>\r
        <div class="component-code">\r
          <pre><code>&lt;app-button variant="primary" figmaState="inactive"&gt;Submit&lt;/app-button&gt;</code></pre>\r
        </div>\r
      </div>\r
\r
      <div class="component-item">\r
        <h3 class="component-title">Primary Button - Default</h3>\r
        <div class="component-subtitle">Style Guide: "Buttons, selectors, and icons" | Implementation: "ButtonComponent"</div>\r
        <div class="component-demo figma-sized button-demo">\r
          <app-button\r
            variant="primary"\r
            [figmaExact]="true"\r
            figmaState="default"\r
            (click)="buttonClicked('primary-default')"\r
          >Submit</app-button>\r
        </div>\r
        <div class="component-code">\r
          <pre><code>&lt;app-button variant="primary" figmaState="default"&gt;Submit&lt;/app-button&gt;</code></pre>\r
        </div>\r
      </div>\r
    </div>\r
\r
    <div class="component-row">\r
      <div class="component-item">\r
        <h3 class="component-title">Primary Button - Hover</h3>\r
        <div class="component-subtitle">Style Guide: "Buttons, selectors, and icons" | Implementation: "ButtonComponent"</div>\r
        <div class="component-demo figma-sized button-demo">\r
          <app-button\r
            variant="primary"\r
            [figmaExact]="true"\r
            figmaState="hover"\r
            (click)="buttonClicked('primary-hover')"\r
          >Submit</app-button>\r
        </div>\r
        <div class="component-code">\r
          <pre><code>&lt;app-button variant="primary" figmaState="hover"&gt;Submit&lt;/app-button&gt;</code></pre>\r
        </div>\r
      </div>\r
\r
      <div class="component-item">\r
        <h3 class="component-title">Primary Button - Click</h3>\r
        <div class="component-subtitle">Style Guide: "Buttons, selectors, and icons" | Implementation: "ButtonComponent"</div>\r
        <div class="component-demo figma-sized button-demo">\r
          <app-button\r
            variant="primary"\r
            [figmaExact]="true"\r
            figmaState="click"\r
            (click)="buttonClicked('primary-click')"\r
          >Submit</app-button>\r
        </div>\r
        <div class="component-code">\r
          <pre><code>&lt;app-button variant="primary" figmaState="click"&gt;Submit&lt;/app-button&gt;</code></pre>\r
        </div>\r
      </div>\r
    </div>\r
\r
    <div class="component-row">\r
      <div class="component-item">\r
        <h3 class="component-title">Secondary Button - Default</h3>\r
        <div class="component-subtitle">Style Guide: "Buttons, selectors, and icons" | Implementation: "ButtonComponent"</div>\r
        <div class="component-demo figma-sized button-demo">\r
          <app-button\r
            variant="secondary"\r
            [figmaExact]="true"\r
            figmaState="default"\r
            (click)="buttonClicked('secondary-default')"\r
          >\r
            <app-refresh-icon color="default"></app-refresh-icon>Refresh charts\r
          </app-button>\r
        </div>\r
        <div class="component-code">\r
          <pre><code>&lt;app-button variant="secondary" figmaState="default"&gt;Refresh charts&lt;/app-button&gt;</code></pre>\r
        </div>\r
      </div>\r
\r
      <div class="component-item">\r
        <h3 class="component-title">Secondary Button - Hover</h3>\r
        <div class="component-subtitle">Style Guide: "Buttons, selectors, and icons" | Implementation: "ButtonComponent"</div>\r
        <div class="component-demo figma-sized button-demo">\r
          <app-button\r
            variant="secondary"\r
            [figmaExact]="true"\r
            figmaState="hover"\r
            (click)="buttonClicked('secondary-hover')"\r
          >\r
            <app-refresh-icon color="hover"></app-refresh-icon>Refresh charts\r
          </app-button>\r
        </div>\r
        <div class="component-code">\r
          <pre><code>&lt;app-button variant="secondary" figmaState="hover"&gt;Refresh charts&lt;/app-button&gt;</code></pre>\r
        </div>\r
      </div>\r
    </div>\r
\r
    <div class="component-row">\r
      <div class="component-item">\r
        <h3 class="component-title">Secondary Button - Click</h3>\r
        <div class="component-subtitle">Style Guide: "Buttons, selectors, and icons" | Implementation: "ButtonComponent"</div>\r
        <div class="component-demo figma-sized button-demo">\r
          <app-button\r
            variant="secondary"\r
            [figmaExact]="true"\r
            figmaState="click"\r
            (click)="buttonClicked('secondary-click')"\r
          >\r
            <app-refresh-icon color="click"></app-refresh-icon>Refresh charts\r
          </app-button>\r
        </div>\r
        <div class="component-code">\r
          <pre><code>&lt;app-button variant="secondary" figmaState="click"&gt;Refresh charts&lt;/app-button&gt;</code></pre>\r
        </div>\r
      </div>\r
    </div>\r
\r
    <!-- Interactive Demo Buttons -->\r
    <h3 class="subsection-title">Interactive Button Demos</h3>\r
    <p class="subsection-description">These buttons demonstrate natural state transitions. Hover and click to see the Figma-specified color changes.</p>\r
    <div class="component-row">\r
      <div class="component-item">\r
        <h3 class="component-title">Interactive Primary Button Demo</h3>\r
        <div class="component-subtitle">Hover and click to see natural state transitions</div>\r
        <div class="component-demo figma-sized button-demo">\r
          <app-button\r
            variant="primary"\r
            [figmaExact]="true"\r
            (click)="buttonClicked('interactive-primary')"\r
          >Submit</app-button>\r
        </div>\r
        <div class="component-code">\r
          <pre><code>&lt;app-button variant="primary" [figmaExact]="true"&gt;Submit&lt;/app-button&gt;</code></pre>\r
        </div>\r
      </div>\r
\r
      <div class="component-item">\r
        <h3 class="component-title">Interactive Secondary Button Demo</h3>\r
        <div class="component-subtitle">Hover and click to see natural state transitions</div>\r
        <div class="component-demo figma-sized button-demo">\r
          <app-button\r
            variant="secondary"\r
            [figmaExact]="true"\r
            (click)="buttonClicked('interactive-secondary')"\r
          >\r
            <app-refresh-icon></app-refresh-icon>Refresh charts\r
          </app-button>\r
        </div>\r
        <div class="component-code">\r
          <pre><code>&lt;app-button variant="secondary" [figmaExact]="true"&gt;Refresh charts&lt;/app-button&gt;</code></pre>\r
        </div>\r
      </div>\r
    </div>\r
  </section>\r
\r
  <!-- Form Controls Section -->\r
  <section class="component-section">\r
    <h2 class="section-title">Form Controls</h2>\r
    <div class="component-row">\r
      <div class="component-item">\r
        <h3 class="component-title">Checkbox</h3>\r
        <div class="component-subtitle">Style Guide: "Buttons, selectors, and icons" | Implementation: "CheckboxComponent"</div>\r
        <div class="component-demo figma-sized checkbox-demo">\r
          <app-checkbox\r
            label="Checkbox"\r
            [(ngModel)]="isChecked"\r
          ></app-checkbox>\r
        </div>\r
        <div class="component-code">\r
          <pre><code>&lt;app-checkbox label="Checkbox"&gt;&lt;/app-checkbox&gt;</code></pre>\r
        </div>\r
      </div>\r
    </div>\r
\r
    <!-- New Form Controls Section -->\r
    <h3 class="subsection-title">Advanced Form Controls</h3>\r
\r
    <div class="component-row">\r
      <div class="component-item">\r
        <h3 class="component-title">Dropdown (Single Select)</h3>\r
        <div class="component-demo figma-sized form-demo">\r
          <app-dropdown\r
            label="Reasoning"\r
            placeholder="Select reasoning"\r
            [options]="reasoningOptions"\r
            [(ngModel)]="selectedValue"\r
            (selectionChange)="onDropdownChange($event)"\r
          ></app-dropdown>\r
        </div>\r
        <div class="component-code">\r
          <pre><code>&lt;app-dropdown label="Reasoning" [options]="options"&gt;&lt;/app-dropdown&gt;</code></pre>\r
        </div>\r
      </div>\r
\r
      <div class="component-item">\r
        <h3 class="component-title">Dropdown (Multi Select)</h3>\r
        <div class="component-demo figma-sized form-demo">\r
          <app-dropdown\r
            label="Multiple Reasoning"\r
            placeholder="Select multiple"\r
            [options]="reasoningOptions"\r
            [multiSelect]="true"\r
            [(ngModel)]="selectedMultipleValues"\r
            (selectionChange)="onMultiDropdownChange($event)"\r
          ></app-dropdown>\r
        </div>\r
        <div class="component-code">\r
          <pre><code>&lt;app-dropdown [multiSelect]="true" [options]="options"&gt;&lt;/app-dropdown&gt;</code></pre>\r
        </div>\r
      </div>\r
    </div>\r
\r
    <div class="component-row">\r
      <div class="component-item">\r
        <h3 class="component-title">Calendar</h3>\r
        <div class="component-demo figma-sized form-demo">\r
          <app-calendar\r
            label="Date of Service"\r
            placeholder="Date of Service"\r
            [(ngModel)]="selectedDate"\r
            (dateChange)="onDateChange($event)"\r
          ></app-calendar>\r
        </div>\r
        <div class="component-code">\r
          <pre><code>&lt;app-calendar label="Date of Service"&gt;&lt;/app-calendar&gt;</code></pre>\r
        </div>\r
      </div>\r
\r
      <div class="component-item">\r
        <h3 class="component-title">Calendar (Required)</h3>\r
        <div class="component-demo figma-sized form-demo">\r
          <app-calendar\r
            label="Required Date"\r
            placeholder="Select date"\r
            [required]="true"\r
            [(ngModel)]="selectedDate"\r
            (dateChange)="onDateChange($event)"\r
          ></app-calendar>\r
        </div>\r
        <div class="component-code">\r
          <pre><code>&lt;app-calendar [required]="true"&gt;&lt;/app-calendar&gt;</code></pre>\r
        </div>\r
      </div>\r
    </div>\r
\r
\r
\r
    <div class="component-row">\r
      <div class="component-item">\r
        <h3 class="component-title">Results</h3>\r
        <div class="component-demo figma-sized results-demo">\r
          <app-results\r
            title="Results"\r
            [(ngModel)]="resultsData"\r
            (dataChange)="onResultsDataChange($event)"\r
            (tabChange)="onResultsTabChange($event)"\r
          ></app-results>\r
        </div>\r
        <div class="component-code">\r
          <pre><code>&lt;app-results title="Results"&gt;&lt;/app-results&gt;</code></pre>\r
        </div>\r
      </div>\r
\r
      <div class="component-item">\r
        <h3 class="component-title">Results (Exclusions Tab)</h3>\r
        <div class="component-demo figma-sized results-demo">\r
          <app-results\r
            title="Findings"\r
            [(ngModel)]="exclusionsData"\r
          ></app-results>\r
        </div>\r
        <div class="component-code">\r
          <pre><code>&lt;app-results title="Findings" [ngModel]="exclusionsData"&gt;&lt;/app-results&gt;</code></pre>\r
        </div>\r
      </div>\r
    </div>\r
  </section>\r
\r
\r
\r
  <!-- Tables Section -->\r
  <section class="component-section">\r
    <h2 class="section-title">Tables</h2>\r
    <div class="component-row full-width">\r
      <div class="component-item full-width">\r
        <h3 class="component-title">Assigned Table (Dashboard Table)</h3>\r
        <div class="component-demo table-demo">\r
          <app-assigned-table [charts]="sampleCharts"></app-assigned-table>\r
        </div>\r
        <div class="component-code">\r
          <pre><code>&lt;app-assigned-table [charts]="charts"&gt;&lt;/app-assigned-table&gt;</code></pre>\r
        </div>\r
      </div>\r
    </div>\r
  </section>\r
\r
  <!-- Phase 2 Components Section -->\r
  <section class="component-section">\r
    <h2 class="section-title">Phase 2 Components</h2>\r
\r
    <!-- Notes Component -->\r
    <div class="component-row">\r
      <div class="component-item">\r
        <h3 class="component-title">Notes</h3>\r
        <div class="component-subtitle">Style Guide: "Notes" | Implementation: "NotesComponent"</div>\r
        <div class="component-demo figma-sized form-demo">\r
          <app-notes\r
            label="Notes"\r
            placeholder="Notes"\r
            [(ngModel)]="notesText"\r
            (notesChange)="onNotesChange($event)"\r
          ></app-notes>\r
        </div>\r
        <div class="component-code">\r
          <pre><code>&lt;app-notes label="Notes" [(ngModel)]="notesText"&gt;&lt;/app-notes&gt;</code></pre>\r
        </div>\r
      </div>\r
\r
      <div class="component-item">\r
        <h3 class="component-title">Notes (Limited)</h3>\r
        <div class="component-subtitle">Style Guide: "Notes" | Implementation: "NotesComponent"</div>\r
        <div class="component-demo figma-sized form-demo">\r
          <app-notes\r
            label="Notes"\r
            placeholder="Notes"\r
            [maxLength]="100"\r
            [(ngModel)]="notesText"\r
          ></app-notes>\r
        </div>\r
        <div class="component-code">\r
          <pre><code>&lt;app-notes [maxLength]="100"&gt;&lt;/app-notes&gt;</code></pre>\r
        </div>\r
      </div>\r
    </div>\r
\r
    <!-- Demographics Component -->\r
    <div class="component-row demographics-row full-width">\r
      <div class="component-item full-width">\r
        <h3 class="component-title">Demographics</h3>\r
        <div class="component-demo figma-sized demographics-demo">\r
          <app-demographics\r
            [data]="demographicsData"\r
            (backClick)="onDemographicsBackClick()"\r
          ></app-demographics>\r
        </div>\r
        <div class="component-code">\r
          <pre><code>&lt;app-demographics [data]="demographicsData"&gt;&lt;/app-demographics&gt;</code></pre>\r
        </div>\r
      </div>\r
    </div>\r
\r
    <!-- Hits Component -->\r
    <div class="component-row full-width">\r
      <div class="component-item full-width">\r
        <h3 class="component-title">Hits</h3>\r
        <div class="component-subtitle">Style Guide: "Hits" | Implementation: "HitsComponent"</div>\r
        <div class="component-demo figma-sized hits-demo">\r
          <app-hits\r
            title="Hits"\r
            [data]="hitsData"\r
            (dataChange)="onHitsDataChange($event)"\r
            (pageClick)="onHitsPageClick($event)"\r
            (commentChange)="onHitsCommentChange($event)"\r
            (includeChange)="onHitsIncludeChange($event)"\r
          ></app-hits>\r
        </div>\r
        <div class="component-code">\r
          <pre><code>&lt;app-hits title="Hits" [data]="hitsData"&gt;&lt;/app-hits&gt;</code></pre>\r
        </div>\r
      </div>\r
    </div>\r
\r
    <!-- Menu Component -->\r
    <div class="component-row full-width">\r
      <div class="component-item full-width">\r
        <h3 class="component-title">Menu</h3>\r
        <div class="component-demo figma-sized menu-demo">\r
          <app-menu\r
            logoSrc="assets/logos/Stellarus_logo_2C_blacktype.png?v=2024"\r
            logoAlt="Stellarus Logo"\r
            [user]="userProfile"\r
            [menuItems]="menuItems"\r
            (logoClick)="onLogoClick()"\r
            (userClick)="onUserClick()"\r
            (dropdownToggle)="onDropdownToggle($event)"\r
            (menuItemClick)="onMenuItemClick($event)"\r
          ></app-menu>\r
        </div>\r
        <div class="component-code">\r
          <pre><code>&lt;app-menu [user]="userProfile" [menuItems]="menuItems"&gt;&lt;/app-menu&gt;</code></pre>\r
        </div>\r
      </div>\r
    </div>\r
\r
    <!-- Icons Component - Temporarily disabled due to SVG binding issues -->\r
    <!--\r
    <div class="component-row full-width">\r
      <div class="component-item full-width">\r
        <h3 class="component-title">Icons</h3>\r
        <div class="component-subtitle">Style Guide: "Buttons, selectors, and icons" | Implementation: "IconComponent"</div>\r
        <div class="component-demo">\r
          <div class="icons-grid">\r
            <div *ngFor="let iconName of iconNames" class="icon-demo-item">\r
              <app-icon [name]="iconName" size="md" color="primary" [clickable]="true"></app-icon>\r
              <span class="icon-label">{{ iconName }}</span>\r
            </div>\r
          </div>\r
        </div>\r
        <div class="component-code">\r
          <pre><code>&lt;app-icon name="refresh" size="md" color="primary" [clickable]="true"&gt;&lt;/app-icon&gt;</code></pre>\r
        </div>\r
      </div>\r
    </div>\r
    -->\r
\r
\r
  </section>\r
</div>`, styles: ['/* src/app/shared/components/component-test/component-test.component.scss */\n.component-test-container {\n  max-width: 1600px;\n  margin: 0 auto;\n  padding: 30px;\n  background-color: #F6F6F6;\n}\n.page-title {\n  font-size: 24px;\n  font-weight: 600;\n  line-height: 32px;\n  color: #17181A;\n  margin-bottom: 16px;\n}\n.page-description {\n  font-size: 14px;\n  font-weight: 300;\n  line-height: 20px;\n  color: #17181A;\n  margin-bottom: 30px;\n}\n.component-section {\n  background: #FFFFFF;\n  border-radius: 8px;\n  outline: 1px #F1F5F7 solid;\n  outline-offset: -1px;\n  padding: 20px;\n  margin-bottom: 20px;\n  margin-bottom: 30px;\n}\n.section-title {\n  font-size: 20px;\n  font-weight: 600;\n  line-height: 32px;\n  color: #17181A;\n  margin-bottom: 20px;\n}\n.section-description {\n  font-size: 14px;\n  color: #547996;\n  margin-bottom: 24px;\n  line-height: 1.5;\n}\n.subsection-description {\n  font-size: 14px;\n  color: #547996;\n  margin-bottom: 16px;\n  line-height: 1.5;\n}\n.subsection-title {\n  font-size: 16px;\n  font-weight: 600;\n  line-height: 24px;\n  color: #17181A;\n  margin: 24px 0 16px 0;\n  padding-left: 12px;\n  border-left: 3px solid #3870B8;\n}\n.component-subtitle {\n  font-size: 11px;\n  font-weight: 300;\n  line-height: 16px;\n  color: #547996;\n  margin-bottom: 12px;\n  font-style: italic;\n}\n.component-row {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 20px;\n  margin-bottom: 40px;\n  min-width: 100%;\n}\n.component-row:last-child {\n  margin-bottom: 0;\n}\n.component-row.demographics-row {\n  flex-wrap: nowrap;\n  overflow-x: visible;\n}\n.component-item {\n  flex: 1;\n  min-width: 300px;\n}\n.component-item.full-width {\n  flex: 0 0 100%;\n}\n.component-title {\n  font-size: 14px;\n  font-weight: 500;\n  line-height: 20px;\n  color: #17181A;\n  margin-bottom: 12px;\n}\n.component-demo {\n  padding: 20px;\n  background-color: #FFFFFF;\n  border-radius: 8px;\n  outline: 1px #F1F5F7 solid;\n  outline-offset: -1px;\n  margin-bottom: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 80px;\n  overflow-x: visible;\n}\n.component-demo.table-demo {\n  justify-content: flex-start;\n  align-items: flex-start;\n  padding: 0;\n  display: block;\n}\n.component-demo.figma-sized {\n  justify-content: flex-start;\n  align-items: flex-start;\n}\n.component-demo.figma-sized.button-demo {\n  width: fit-content;\n  min-width: auto;\n  padding: 20px;\n}\n.component-demo.figma-sized.form-demo {\n  width: 300px;\n  min-width: 300px;\n  min-height: 400px;\n  padding: 20px 20px 60px 20px;\n  overflow: visible;\n}\n.component-demo.figma-sized.form-demo app-dropdown,\n.component-demo.figma-sized.form-demo app-date-picker,\n.component-demo.figma-sized.form-demo app-notes {\n  width: 100%;\n}\n.component-demo.figma-sized.form-demo app-dropdown,\n.component-demo.figma-sized.form-demo app-date-picker {\n  position: relative;\n  z-index: 10;\n}\n.component-demo.figma-sized.hits-demo {\n  width: 557px;\n  min-width: 557px;\n  padding: 20px;\n}\n.component-demo.figma-sized.hits-demo app-hits {\n  width: 517px;\n}\n.component-demo.figma-sized.demographics-demo {\n  width: 100%;\n  min-width: 1420px;\n  padding: 20px;\n  overflow-x: visible;\n}\n.component-demo.figma-sized.results-demo {\n  width: 400px;\n  min-width: 400px;\n  padding: 20px;\n}\n.component-demo.figma-sized.menu-demo {\n  width: 100%;\n  padding: 0;\n}\n.component-demo.figma-sized.status-demo {\n  width: fit-content;\n  min-width: auto;\n  padding: 20px;\n}\n.component-demo.figma-sized.checkbox-demo {\n  width: fit-content;\n  min-width: auto;\n  padding: 20px;\n}\n.component-code {\n  background-color: #F9FBFC;\n  border-radius: 8px;\n  padding: 12px;\n  overflow: auto;\n}\n.component-code pre {\n  margin: 0;\n  font-family: monospace;\n  font-size: 12px;\n  line-height: 1.5;\n  color: #384455;\n}\n.full-width {\n  width: 100%;\n}\n@media (min-width: 600px) {\n  .component-row {\n    flex-wrap: nowrap;\n  }\n}\n@media (max-width: 599px) {\n  .component-item {\n    flex: 0 0 100%;\n  }\n}\n.icons-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\n  gap: 16px;\n  padding: 16px;\n}\n.icon-demo-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 8px;\n  padding: 12px;\n  border: 1px solid #F1F5F7;\n  border-radius: 8px;\n  transition: background-color 0.2s ease;\n}\n.icon-demo-item:hover {\n  background-color: #F1F5F7;\n}\n.icon-label {\n  font-size: 10px;\n  font-family: "Urbane", sans-serif;\n  font-weight: 300;\n  color: #547996;\n  text-align: center;\n}\n/*# sourceMappingURL=component-test.component.css.map */\n'] }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(ComponentTestComponent, { className: "ComponentTestComponent", filePath: "src/app/shared/components/component-test/component-test.component.ts", lineNumber: 42 });
})();

// src/app/app.routes.ts
var routes = [
  {
    path: "",
    redirectTo: "dashboard",
    pathMatch: "full"
  },
  __spreadValues({
    path: "dashboard",
    loadChildren: () => import("./chunk-QO3WNN6R.js").then((m) => m.DashboardModule)
  }, false ? { \u0275entryName: "src/app/features/dashboard/dashboard.module.ts" } : {}),
  __spreadValues({
    path: "chart-review",
    loadChildren: () => import("./chunk-JQPK26PE.js").then((m) => m.ChartReviewModule)
  }, false ? { \u0275entryName: "src/app/features/chart-review/chart-review.module.ts" } : {}),
  __spreadValues({
    path: "auth",
    loadChildren: () => import("./chunk-WX24KWY2.js").then((m) => m.AuthModule)
  }, false ? { \u0275entryName: "src/app/features/auth/auth.module.ts" } : {}),
  {
    path: "pdf-test",
    component: PdfViewerTestComponent
  },
  {
    path: "component-test",
    component: ComponentTestComponent
  }
];

// src/app/app.config.ts
var appConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes),
    provideClientHydration(withEventReplay()),
    provideHttpClient(withFetch())
  ]
};

// src/app/app.component.ts
var AppComponent = class _AppComponent {
  router;
  renderer;
  el;
  title = "clinical-quality-app";
  routerOutlet;
  constructor(router, renderer, el) {
    this.router = router;
    this.renderer = renderer;
    this.el = el;
    console.log("AppComponent constructor");
  }
  ngOnInit() {
    console.log("AppComponent ngOnInit");
    this.router.events.pipe(filter((event) => event instanceof NavigationEnd)).subscribe((event) => {
      console.log("Navigation event:", event.url);
      console.log("Current router config:", JSON.stringify(this.router.config, (key, value) => {
        if (key === "component" && typeof value === "function") {
          return value.name;
        }
        if (key === "_loadedConfig") {
          return {
            routes: value.routes.map((r) => ({
              path: r.path,
              component: r.component?.name
            }))
          };
        }
        return value;
      }, 2));
      if (typeof document !== "undefined") {
        const routerOutlets = document.querySelectorAll("router-outlet");
        console.log(`Found ${routerOutlets.length} router-outlet elements after navigation to ${event.url}`);
        routerOutlets.forEach((outlet, index) => {
          console.log(`Router outlet #${index + 1} parent:`, outlet.parentElement?.tagName);
          console.log(`Router outlet #${index + 1} siblings:`, outlet.parentElement?.children.length);
        });
      }
    });
  }
  ngAfterViewInit() {
    console.log("AppComponent ngAfterViewInit");
    if (this.routerOutlet && this.routerOutlet.isActivated) {
      console.log("Router outlet component:", this.routerOutlet.component?.constructor.name);
    } else {
      console.log("Router outlet is not yet activated");
    }
    if (typeof document !== "undefined") {
      const routerOutlets = document.querySelectorAll("router-outlet");
      console.log(`Found ${routerOutlets.length} router-outlet elements in ngAfterViewInit`);
      if (typeof MutationObserver !== "undefined") {
        const observer = new MutationObserver((mutations) => {
          const currentOutlets = document.querySelectorAll("router-outlet");
          if (currentOutlets.length > 1) {
            console.log(`MutationObserver detected ${currentOutlets.length} router outlets`);
            console.log("DOM mutation detected that might have added router outlets");
          }
        });
        observer.observe(document.body, {
          childList: true,
          subtree: true
        });
      }
    }
  }
  static \u0275fac = function AppComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _AppComponent)(\u0275\u0275directiveInject(Router), \u0275\u0275directiveInject(Renderer2), \u0275\u0275directiveInject(ElementRef));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _AppComponent, selectors: [["app-root"]], viewQuery: function AppComponent_Query(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275viewQuery(RouterOutlet, 5);
    }
    if (rf & 2) {
      let _t;
      \u0275\u0275queryRefresh(_t = \u0275\u0275loadQuery()) && (ctx.routerOutlet = _t.first);
    }
  }, decls: 1, vars: 0, template: function AppComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275element(0, "router-outlet");
    }
  }, dependencies: [RouterOutlet], encapsulation: 2 });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AppComponent, [{
    type: Component,
    args: [{ selector: "app-root", imports: [RouterOutlet], template: "<router-outlet></router-outlet>\r\n" }]
  }], () => [{ type: Router }, { type: Renderer2 }, { type: ElementRef }], { routerOutlet: [{
    type: ViewChild,
    args: [RouterOutlet]
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(AppComponent, { className: "AppComponent", filePath: "src/app/app.component.ts", lineNumber: 11 });
})();

// src/main.ts
if (typeof window !== "undefined") {
  console.log("Setting PDF worker source path to: /assets/pdf.worker.mjs");
  window.pdfWorkerSrc = "/assets/pdf.worker.mjs";
  setTimeout(() => {
    console.log("Verifying PDF worker configuration:");
    console.log("window.pdfWorkerSrc =", window.pdfWorkerSrc);
    const testRequest = new XMLHttpRequest();
    testRequest.open("HEAD", "/assets/pdf.worker.mjs", true);
    testRequest.onreadystatechange = function() {
      if (this.readyState === this.DONE) {
        console.log("PDF worker file check:", this.status);
        if (this.status === 200) {
          console.log("PDF worker file exists and is accessible");
        } else {
          console.error("PDF worker file not found or not accessible!");
        }
      }
    };
    testRequest.send();
  }, 1e3);
}
bootstrapApplication(AppComponent, appConfig).catch((err) => console.error(err));
//# sourceMappingURL=main.js.map

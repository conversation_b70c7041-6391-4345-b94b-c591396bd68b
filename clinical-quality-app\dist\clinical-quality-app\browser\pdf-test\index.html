<!DOCTYPE html><html lang="en"><head>
  <meta charset="utf-8">
  <title>ClinicalQualityApp</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/png" href="assets/logos/Stellarus-Favicon-red.png">
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&amp;display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
<link rel="stylesheet" href="styles.css"><style ng-app-id="ng">

.pdf-test-container[_ngcontent-ng-c2150817084] {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background-color: #f5f5f5;
  padding: 20px;
  box-sizing: border-box;
}
.test-controls[_ngcontent-ng-c2150817084] {
  background-color: #ffffff;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}
h2[_ngcontent-ng-c2150817084] {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 20px;
  color: #333;
}
.file-input-container[_ngcontent-ng-c2150817084] {
  margin-bottom: 16px;
}
.file-input-label[_ngcontent-ng-c2150817084] {
  display: inline-block;
  padding: 8px 16px;
  background-color: #2196f3;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}
.file-input-label[_ngcontent-ng-c2150817084]:hover {
  background-color: #1976d2;
}
.file-input[_ngcontent-ng-c2150817084] {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}
.config-controls[_ngcontent-ng-c2150817084] {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
}
.control-group[_ngcontent-ng-c2150817084] {
  display: flex;
  align-items: center;
  gap: 8px;
}
.control-group[_ngcontent-ng-c2150817084]   label[_ngcontent-ng-c2150817084] {
  font-size: 14px;
  color: #333;
}
.control-group[_ngcontent-ng-c2150817084]   button[_ngcontent-ng-c2150817084], 
.control-group[_ngcontent-ng-c2150817084]   select[_ngcontent-ng-c2150817084] {
  padding: 4px 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: #fff;
  color: #17181A;
  font-size: 14px;
  cursor: pointer;
}
.control-group[_ngcontent-ng-c2150817084]   button[_ngcontent-ng-c2150817084]:hover, 
.control-group[_ngcontent-ng-c2150817084]   select[_ngcontent-ng-c2150817084]:hover {
  background-color: #f0f0f0;
}
.debug-info[_ngcontent-ng-c2150817084] {
  background-color: #f0f0f0;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  font-family: monospace;
}
.debug-info[_ngcontent-ng-c2150817084]   h3[_ngcontent-ng-c2150817084] {
  margin-top: 0;
  margin-bottom: 8px;
  font-size: 14px;
}
.debug-info[_ngcontent-ng-c2150817084]   div[_ngcontent-ng-c2150817084] {
  margin-bottom: 4px;
}
.pdf-viewer-wrapper[_ngcontent-ng-c2150817084] {
  flex: 1;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 300px);
  min-height: 500px;
}
[_nghost-ng-c2150817084]     ngx-extended-pdf-viewer {
  display: block;
  width: 100%;
  height: 100%;
}
[_nghost-ng-c2150817084]     .viewer-container {
  overflow: auto !important;
}
[_nghost-ng-c2150817084]     .page {
  margin: 8px auto !important;
}
.no-pdf-message[_ngcontent-ng-c2150817084], 
.ssr-placeholder[_ngcontent-ng-c2150817084] {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-size: 16px;
  color: #666;
  text-align: center;
  padding: 20px;
}
@media (max-width: 768px) {
  .config-controls[_ngcontent-ng-c2150817084] {
    flex-direction: column;
    align-items: flex-start;
  }
  .pdf-viewer-wrapper[_ngcontent-ng-c2150817084] {
    height: calc(100vh - 400px);
  }
}
/*# sourceMappingURL=/pdf-viewer-test.component.css.map */</style></head>
<body class="mat-typography"><!--nghm--><script type="text/javascript" id="ng-event-dispatch-contract">(()=>{function p(t,n,r,o,e,i,f,m){return{eventType:t,event:n,targetElement:r,eic:o,timeStamp:e,eia:i,eirp:f,eiack:m}}function u(t){let n=[],r=e=>{n.push(e)};return{c:t,q:n,et:[],etc:[],d:r,h:e=>{r(p(e.type,e,e.target,t,Date.now()))}}}function s(t,n,r){for(let o=0;o<n.length;o++){let e=n[o];(r?t.etc:t.et).push(e),t.c.addEventListener(e,t.h,r)}}function c(t,n,r,o,e=window){let i=u(t);e._ejsas||(e._ejsas={}),e._ejsas[n]=i,s(i,r),s(i,o,!0)}window.__jsaction_bootstrap=c;})();
</script><script>window.__jsaction_bootstrap(document.body,"ng",["change","click"],[]);</script>
  <app-root ng-version="19.2.9" ngh="1" ng-server-context="ssg"><router-outlet></router-outlet><app-pdf-viewer-test _nghost-ng-c2150817084="" ngh="0"><div _ngcontent-ng-c2150817084="" class="pdf-test-container"><div _ngcontent-ng-c2150817084="" class="test-controls"><h2 _ngcontent-ng-c2150817084="">PDF Viewer Test Component</h2><div _ngcontent-ng-c2150817084="" class="file-input-container"><label _ngcontent-ng-c2150817084="" for="pdf-test-file-input" class="file-input-label"><span _ngcontent-ng-c2150817084="">Load PDF from local file</span></label><input _ngcontent-ng-c2150817084="" type="file" id="pdf-test-file-input" accept=".pdf" class="file-input" jsaction="change:;"></div><div _ngcontent-ng-c2150817084="" class="config-controls"><div _ngcontent-ng-c2150817084="" class="control-group"><label _ngcontent-ng-c2150817084="">Range Requests:</label><button _ngcontent-ng-c2150817084="" jsaction="click:;"> Enabled </button></div><div _ngcontent-ng-c2150817084="" class="control-group"><label _ngcontent-ng-c2150817084="">Fit to Page:</label><button _ngcontent-ng-c2150817084="" jsaction="click:;"> Enabled </button></div><div _ngcontent-ng-c2150817084="" class="control-group"><label _ngcontent-ng-c2150817084="">Scroll Mode:</label><select _ngcontent-ng-c2150817084="" jsaction="change:;"><option _ngcontent-ng-c2150817084="" value="0" ng-reflect-value="0">Vertical</option><option _ngcontent-ng-c2150817084="" value="1" ng-reflect-value="1">Horizontal</option><option _ngcontent-ng-c2150817084="" value="2" ng-reflect-value="2">Wrapped</option></select></div><div _ngcontent-ng-c2150817084="" class="control-group"><label _ngcontent-ng-c2150817084="">Spread Mode:</label><select _ngcontent-ng-c2150817084="" jsaction="change:;"><option _ngcontent-ng-c2150817084="" value="0" ng-reflect-value="0">None</option><option _ngcontent-ng-c2150817084="" value="1" ng-reflect-value="1">Odd</option><option _ngcontent-ng-c2150817084="" value="2" ng-reflect-value="2">Even</option></select></div></div><!--bindings={
  "ng-reflect-ng-if": "false"
}--></div><div _ngcontent-ng-c2150817084="" class="pdf-viewer-wrapper"><!--bindings={
  "ng-reflect-ng-if": "false"
}--><!--bindings={
  "ng-reflect-ng-if": "false"
}--><div _ngcontent-ng-c2150817084="" class="ssr-placeholder"> PDF Viewer is not available during server-side rendering. </div><!--bindings={
  "ng-reflect-ng-if": "true"
}--></div></div></app-pdf-viewer-test><!--container--></app-root>
<link rel="modulepreload" href="chunk-YLWVG4A5.js"><link rel="modulepreload" href="chunk-FVKW5FZS.js"><link rel="modulepreload" href="chunk-JRLQF6CE.js"><link rel="modulepreload" href="chunk-F5HTA5WY.js"><link rel="modulepreload" href="chunk-PC6IZSQ2.js"><script src="polyfills.js" type="module"></script><script src="main.js" type="module"></script>

<script id="ng-state" type="application/json">{"__nghData__":[{"t":{"42":"t20","44":"t21","45":"t22","46":"t23"},"c":{"42":[],"44":[],"45":[],"46":[{"i":"t23","r":1}]}},{"c":{"0":[{"i":"c2150817084","r":1}]}}]}</script></body></html>
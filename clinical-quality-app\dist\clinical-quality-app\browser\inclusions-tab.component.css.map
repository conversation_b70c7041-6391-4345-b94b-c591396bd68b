{"version": 3, "sources": ["src/app/shared/components/form-controls/results/tabs/inclusions-tab.component.scss", "src/styles/_variables.scss"], "sourcesContent": ["@use 'variables' as variables;\n\n// Exact Figma specifications for inclusions tab\n.inclusions-tab-content {\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 12px; // Exact Figma gap from frame-916\n  box-sizing: border-box;\n  width: 100%;\n}\n\n// Telehealth section - exact Figma frame-942\n.frame-942 {\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 8px; // Exact Figma gap\n  box-sizing: border-box;\n}\n\n// Form fields container - exact Figma frame-936\n.frame-936 {\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 4px; // Exact Figma gap\n  box-sizing: border-box;\n  width: 100%;\n}\n\n// Row containing Sys, Dias, Date of Service - exact Figma frame-939\n.frame-939 {\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 4px; // Exact Figma gap\n  box-sizing: border-box;\n  width: 100%;\n}\n\n// Sys field container - exact Figma frame-940\n.frame-940 {\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 4px; // Exact Figma gap\n  box-sizing: border-box;\n  width: 127px; // Exact Figma width\n}\n\n// Dias field container - exact Figma frame-941\n.frame-941 {\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 4px; // Exact Figma gap\n  box-sizing: border-box;\n  width: 127px; // Exact Figma width\n}\n\n// Date of Service field container - exact Figma frame-937\n.frame-937 {\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 4px; // Exact Figma gap\n  box-sizing: border-box;\n  width: 215px; // Exact Figma width\n}\n\n// Notes section - exact Figma frame-938\n.frame-938 {\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  align-items: flex-start;\n  gap: 4px; // Exact Figma gap\n  box-sizing: border-box;\n  width: 100%;\n}\n\n// Label styling - exact Figma specifications\n.text-sys,\n.text-dias,\n.text-date-of-service,\n.text-notes {\n  color: variables.$gray-3; // Exact Figma color\n  font-size: 10px; // Exact Figma font size\n  font-family: Urbane; // Exact Figma font family\n  line-height: 20px; // Exact Figma line height\n  letter-spacing: 0%;\n  text-decoration: none;\n  font-weight: 300; // Exact Figma font weight\n  text-align: left;\n  text-wrap: nowrap;\n}\n\n// Input styling - exact Figma dropdown-inclusion specifications\n.dropdown-inclusion {\n  padding: 12px; // Based on row padding 8px + 4px\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  flex-wrap: nowrap;\n  align-items: center;\n  gap: 0px;\n  box-sizing: border-box;\n  overflow: hidden;\n  border-radius: 10px; // Exact Figma border radius\n  border-color: variables.$gray-2; // Exact Figma border color\n  border-style: solid;\n  border-width: 1px;\n  background: #ffffff; // Exact Figma background\n  height: 48px; // Exact Figma height\n  width: 100%;\n  outline: none;\n  color: variables.$gray-3; // Exact Figma text color\n  font-size: 12px; // Exact Figma font size\n  font-family: Urbane; // Exact Figma font family\n  line-height: 20px; // Exact Figma line height\n  font-weight: 300; // Exact Figma font weight\n  text-align: left;\n\n  &::placeholder {\n    color: variables.$gray-3; // Exact Figma placeholder color\n  }\n\n  &:focus {\n    border-color: variables.$gray-3;\n  }\n\n  &:disabled {\n    background-color: variables.$gray-1;\n    cursor: not-allowed;\n    color: variables.$gray-3;\n  }\n}\n\n// Calendar component styling - exact Figma specifications\n.calendar {\n  height: 48px; // Exact Figma height\n  width: 100%; // Exact Figma width\n}\n\n// Notes component styling - exact Figma specifications\n.notes {\n  width: 100%; // Exact Figma width\n}\n\n// Responsive design\n@media (max-width: 768px) {\n  .frame-939 {\n    flex-direction: column;\n    gap: 8px;\n  }\n\n  .frame-940,\n  .frame-941,\n  .frame-937 {\n    width: 100%;\n  }\n}\n", "// Color Variables\r\n$text-black: #17181A;\r\n$primary-blue: #3870B8;\r\n$hover-blue: #468CE7;\r\n$click-blue: #285082;\r\n$light-blue: #BFD0EE;\r\n$link: #0071BC;\r\n$gray-1: #F1F5F7;\r\n$gray-2: #D9E1E7;\r\n$gray-3: #547996;\r\n$gray-4: #384455;\r\n$success-green: #1AD598;\r\n$white: #FFFFFF;\r\n$background-gray: #F6F6F6;\r\n$light-background: #F9FBFC;\r\n$light-primary: #809FB8;\r\n\r\n// Opacity Variables\r\n$primary-blue-opacity-20: rgba(56, 112, 184, 0.20);\r\n$success-green-opacity-10: rgba(26, 213, 152, 0.10);\r\n$success-green-opacity-40: rgba(26, 213, 152, 0.40);\r\n\r\n// Spacing Variables\r\n$spacing-xs: 4px;\r\n$spacing-sm: 8px;\r\n$spacing-md: 12px;\r\n$spacing-lg: 16px;\r\n$spacing-xl: 20px;\r\n$spacing-xxl: 24px;\r\n$spacing-xxxl: 30px;\r\n$spacing-huge: 40px;\r\n$spacing-giant: 48px;\r\n$spacing-mega: 55px;\r\n\r\n// Border Radius\r\n$border-radius-sm: 5px;\r\n$border-radius-md: 6px;\r\n$border-radius-lg: 8px;\r\n$border-radius-xl: 10px;\r\n$border-radius-round: 90px;\r\n$border-radius-circle: 120px;\r\n\r\n// Box Shadow\r\n$box-shadow-sm: 0px 1px 2px rgba(16, 24, 40, 0.05);\r\n\r\n// Border Width\r\n$border-width-default: 1px;\r\n$border-width-thick: 1.5px;\r\n\r\n// Z-index\r\n$z-index-default: 1;\r\n$z-index-dropdown: 1000;\r\n$z-index-sticky: 1020;\r\n$z-index-fixed: 1030;\r\n$z-index-modal-backdrop: 1040;\r\n$z-index-modal: 1050;\r\n$z-index-popover: 1060;\r\n$z-index-tooltip: 1070;"], "mappings": ";AAGA,CAAA;AACE,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;AACA,SAAA;;AAIF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;;AAIF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;AACA,SAAA;;AAIF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;AACA,SAAA;;AAIF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;AACA,SAAA;;AAIF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;AACA,SAAA;;AAIF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;AACA,SAAA;;AAIF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;AACA,SAAA;;AAIF,CAAA;AAAA,CAAA;AAAA,CAAA;AAAA,CAAA;AAIE,SC7FO;AD8FP,aAAA;AACA,eAAA;AACA,eAAA;AACA,kBAAA;AACA,mBAAA;AACA,eAAA;AACA,cAAA;AACA,aAAA;;AAIF,CAAA;AACE,WAAA;AACA,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;AACA,YAAA;AACA,iBAAA;AACA,gBCrHO;ADsHP,gBAAA;AACA,gBAAA;AACA,cAAA;AACA,UAAA;AACA,SAAA;AACA,WAAA;AACA,SC3HO;AD4HP,aAAA;AACA,eAAA;AACA,eAAA;AACA,eAAA;AACA,cAAA;;AAEA,CAzBF,kBAyBE;AACE,SCnIK;;ADsIP,CA7BF,kBA6BE;AACE,gBCvIK;;AD0IP,CAjCF,kBAiCE;AACE,oBC7IK;AD8IL,UAAA;AACA,SC7IK;;ADkJT,CAAA;AACE,UAAA;AACA,SAAA;;AAIF,CAAA;AACE,SAAA;;AAIF,OAAA,CAAA,SAAA,EAAA;AACE,GAjIF;AAkII,oBAAA;AACA,SAAA;;AAGF,GA1HF;EA0HE,CA9GF;EA8GE,CAlGF;AAqGI,WAAA;;;", "names": []}
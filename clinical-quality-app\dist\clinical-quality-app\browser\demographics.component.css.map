{"version": 3, "sources": ["src/app/shared/components/demographics/demographics.component.scss", "src/styles/_variables.scss", "src/styles/_mixins.scss"], "sourcesContent": ["@use 'variables' as variables;\n@use 'mixins' as mix;\n\n.demographics-container {\n  width: 100%;\n  padding: 20px;\n  background: variables.$white;\n  border-radius: 8px;\n  border: 1px solid variables.$gray-1;\n}\n\n.demographics-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  gap: 48px;\n  width: 100%;\n}\n\n.left-section {\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-end;\n  gap: 48px;\n}\n\n.header-section {\n  display: flex;\n  align-items: flex-start;\n  gap: 36px;\n}\n\n.back-button {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  cursor: pointer;\n  transition: opacity 0.2s ease;\n\n  &:hover {\n    opacity: 0.8;\n  }\n}\n\n.back-icon {\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: variables.$link;\n}\n\n.back-text {\n  color: variables.$link;\n  font-size: 12px;\n  font-family: 'Urbane', sans-serif;\n  font-weight: 500;\n  line-height: 20px;\n}\n\n.measure-info {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n}\n\n.measure-title {\n  color: variables.$text-black;\n  font-size: 20px;\n  font-family: 'Urbane', sans-serif;\n  font-weight: 600;\n  line-height: 32px;\n}\n\n.measure-subtitle {\n  color: variables.$gray-3;\n  font-size: 12px;\n  font-family: 'Urbane', sans-serif;\n  font-weight: 500;\n  line-height: 20px;\n}\n\n.right-section {\n  display: flex;\n  align-items: center;\n  gap: 60px;\n}\n\n.demographics-group {\n  display: flex;\n  align-items: center;\n\n  &.primary-group {\n    gap: 40px;\n  }\n\n  &.provider-group {\n    gap: 24px;\n  }\n}\n\n.demographic-item {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n  min-width: fit-content;\n\n  // Specific widths based on style guide measurements\n  &:nth-child(1) { // Member ID\n    width: 68px;\n  }\n\n  &:nth-child(2) { // Member Name\n    width: 57px;\n    min-width: 57px;\n  }\n\n  &:nth-child(3) { // DOB\n    width: 69px;\n  }\n\n  &:nth-child(4) { // Gender\n    min-width: auto;\n  }\n\n  &:nth-child(5) { // LOB\n    width: 51px;\n  }\n\n  // Provider group specific widths\n  .provider-group &:nth-child(1) { // Provider Name\n    width: 115px;\n    min-width: 115px;\n  }\n\n  .provider-group &:nth-child(2) { // NPI\n    width: 68px;\n  }\n}\n\n.demographic-value {\n  color: variables.$text-black;\n  font-size: 12px;\n  font-family: 'Urbane', sans-serif;\n  font-weight: 600;\n  line-height: 20px;\n  word-wrap: break-word;\n}\n\n.demographic-label {\n  color: variables.$gray-3;\n  font-size: 12px;\n  font-family: 'Urbane', sans-serif;\n  font-weight: 500;\n  line-height: 20px;\n  word-wrap: break-word;\n}\n\n// Responsive design\n@include mix.for-phone-only {\n  .demographics-content {\n    flex-direction: column;\n    gap: 24px;\n  }\n\n  .right-section {\n    flex-direction: column;\n    gap: 24px;\n    align-items: flex-start;\n  }\n\n  .demographics-group {\n    flex-wrap: wrap;\n    gap: 16px !important;\n  }\n\n  .demographic-item {\n    min-width: auto;\n    width: auto !important;\n  }\n\n  .header-section {\n    gap: 20px;\n  }\n}\n\n@include mix.for-tablet-portrait-up {\n  .demographics-content {\n    flex-direction: row;\n  }\n\n  .right-section {\n    flex-direction: row;\n  }\n}\n", "// Color Variables\r\n$text-black: #17181A;\r\n$primary-blue: #3870B8;\r\n$hover-blue: #468CE7;\r\n$click-blue: #285082;\r\n$light-blue: #BFD0EE;\r\n$link: #0071BC;\r\n$gray-1: #F1F5F7;\r\n$gray-2: #D9E1E7;\r\n$gray-3: #547996;\r\n$gray-4: #384455;\r\n$success-green: #1AD598;\r\n$white: #FFFFFF;\r\n$background-gray: #F6F6F6;\r\n$light-background: #F9FBFC;\r\n$light-primary: #809FB8;\r\n\r\n// Opacity Variables\r\n$primary-blue-opacity-20: rgba(56, 112, 184, 0.20);\r\n$success-green-opacity-10: rgba(26, 213, 152, 0.10);\r\n$success-green-opacity-40: rgba(26, 213, 152, 0.40);\r\n\r\n// Spacing Variables\r\n$spacing-xs: 4px;\r\n$spacing-sm: 8px;\r\n$spacing-md: 12px;\r\n$spacing-lg: 16px;\r\n$spacing-xl: 20px;\r\n$spacing-xxl: 24px;\r\n$spacing-xxxl: 30px;\r\n$spacing-huge: 40px;\r\n$spacing-giant: 48px;\r\n$spacing-mega: 55px;\r\n\r\n// Border Radius\r\n$border-radius-sm: 5px;\r\n$border-radius-md: 6px;\r\n$border-radius-lg: 8px;\r\n$border-radius-xl: 10px;\r\n$border-radius-round: 90px;\r\n$border-radius-circle: 120px;\r\n\r\n// Box Shadow\r\n$box-shadow-sm: 0px 1px 2px rgba(16, 24, 40, 0.05);\r\n\r\n// Border Width\r\n$border-width-default: 1px;\r\n$border-width-thick: 1.5px;\r\n\r\n// Z-index\r\n$z-index-default: 1;\r\n$z-index-dropdown: 1000;\r\n$z-index-sticky: 1020;\r\n$z-index-fixed: 1030;\r\n$z-index-modal-backdrop: 1040;\r\n$z-index-modal: 1050;\r\n$z-index-popover: 1060;\r\n$z-index-tooltip: 1070;", "// Import variables\r\n@use 'variables' as variables;\r\n\r\n// Flexbox Mixins\r\n@mixin flex-row {\r\n  display: flex;\r\n  flex-direction: row;\r\n}\r\n\r\n@mixin flex-column {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n@mixin flex-center {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n@mixin flex-between {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n@mixin flex-start {\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  align-items: center;\r\n}\r\n\r\n@mixin flex-end {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  align-items: center;\r\n}\r\n\r\n// Layout Mixins\r\n@mixin container {\r\n  width: 100%;\r\n  padding-left: variables.$spacing-xxxl;\r\n  padding-right: variables.$spacing-xxxl;\r\n}\r\n\r\n@mixin card {\r\n  background: variables.$white;\r\n  border-radius: variables.$border-radius-lg;\r\n  outline: variables.$border-width-default variables.$gray-1 solid;\r\n  outline-offset: -(variables.$border-width-default);\r\n  padding: variables.$spacing-xl;\r\n  margin-bottom: variables.$spacing-xl;\r\n}\r\n\r\n// Button Mixins\r\n@mixin button-base {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  gap: variables.$spacing-sm;\r\n  border-radius: variables.$border-radius-lg;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  border: none;\r\n  outline: none;\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n@mixin button-primary {\r\n  @include button-base;\r\n  background: variables.$primary-blue;\r\n  color: variables.$white;\r\n  padding: variables.$spacing-sm variables.$spacing-lg;\r\n\r\n  &:hover {\r\n    background: variables.$hover-blue;\r\n  }\r\n\r\n  &:active {\r\n    background: variables.$click-blue;\r\n  }\r\n\r\n  &:disabled {\r\n    background: variables.$light-blue;\r\n    cursor: not-allowed;\r\n  }\r\n}\r\n\r\n@mixin button-secondary {\r\n  @include button-base;\r\n  background: variables.$white;\r\n  outline: variables.$border-width-default variables.$gray-2 solid;\r\n  outline-offset: -(variables.$border-width-default);\r\n  color: variables.$text-black;\r\n  padding: variables.$spacing-sm variables.$spacing-md;\r\n\r\n  &:hover {\r\n    background: variables.$gray-1;\r\n  }\r\n\r\n  &:active {\r\n    background: variables.$text-black;\r\n    color: variables.$white;\r\n  }\r\n}\r\n\r\n@mixin button-icon {\r\n  @include button-base;\r\n  gap: variables.$spacing-sm;\r\n\r\n  .icon {\r\n    width: 20px;\r\n    height: 20px;\r\n  }\r\n}\r\n\r\n// Form Element Mixins\r\n@mixin input-field {\r\n  padding: variables.$spacing-md;\r\n  border-radius: variables.$border-radius-xl;\r\n  outline: variables.$border-width-default variables.$gray-2 solid;\r\n  outline-offset: -(variables.$border-width-default);\r\n  font-size: 12px;\r\n  font-family: 'Urbane', sans-serif;\r\n  width: 100%;\r\n\r\n  &:focus {\r\n    outline-color: variables.$gray-3;\r\n  }\r\n}\r\n\r\n@mixin textarea-field {\r\n  padding: variables.$spacing-md;\r\n  border-radius: variables.$border-radius-xl;\r\n  outline: variables.$border-width-default variables.$gray-2 solid;\r\n  outline-offset: -(variables.$border-width-default);\r\n  font-size: 12px;\r\n  font-family: 'Urbane', sans-serif;\r\n  width: 100%;\r\n  min-height: 88px;\r\n\r\n  &:focus {\r\n    outline-color: variables.$gray-3;\r\n  }\r\n}\r\n\r\n@mixin checkbox {\r\n  width: 16px;\r\n  height: 16px;\r\n  border-radius: 5px; // Figma specifies 5px border radius\r\n  border: 1px solid variables.$gray-2;\r\n  background-color: variables.$white;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  appearance: none;\r\n  -webkit-appearance: none;\r\n  -moz-appearance: none;\r\n  position: relative;\r\n\r\n  &:checked {\r\n    background-color: variables.$text-black;\r\n    border-color: variables.$text-black;\r\n\r\n    &::after {\r\n      content: '';\r\n      position: absolute;\r\n      left: 4px;\r\n      top: 4px;\r\n      width: 8.33px;\r\n      height: 7.5px;\r\n      background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8.33' height='7.5' viewBox='0 0 8.33 7.5'%3E%3Cpath d='M1 3.75L3.5 6.25L7.33 1.25' stroke='white' stroke-width='1.5' fill='none' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E\");\r\n      background-repeat: no-repeat;\r\n      background-position: center;\r\n      background-size: contain;\r\n    }\r\n  }\r\n\r\n  &:hover:not(:disabled) {\r\n    border-color: variables.$gray-3;\r\n  }\r\n\r\n  &:focus {\r\n    outline: 2px solid variables.$primary-blue;\r\n    outline-offset: 2px;\r\n  }\r\n\r\n  &:disabled {\r\n    opacity: 0.5;\r\n    cursor: not-allowed;\r\n  }\r\n}\r\n\r\n// Table Mixins\r\n@mixin table-header {\r\n  padding: variables.$spacing-md;\r\n  border-bottom: variables.$border-width-default variables.$gray-1 solid;\r\n  font-weight: 500;\r\n  color: variables.$text-black;\r\n  height: 68px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n@mixin table-cell {\r\n  padding: variables.$spacing-md;\r\n  font-weight: 300;\r\n  height: 68px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-start;\r\n}\r\n\r\n\r\n\r\n// Icon Mixins\r\n@mixin icon-container {\r\n  width: 20px;\r\n  height: 20px;\r\n  position: relative;\r\n}\r\n\r\n// Status Indicators\r\n@mixin status-badge($color, $bg-color) {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: variables.$spacing-xs variables.$spacing-sm;\r\n  border-radius: variables.$border-radius-round;\r\n  background-color: $bg-color;\r\n  color: $color;\r\n  font-size: 11px;\r\n  font-weight: 300;\r\n}\r\n\r\n@mixin success-badge {\r\n  @include status-badge(variables.$success-green, variables.$success-green-opacity-10);\r\n  outline: variables.$border-width-default variables.$success-green-opacity-40 solid;\r\n  outline-offset: -(variables.$border-width-default);\r\n}\r\n\r\n// Responsive Mixins\r\n@mixin for-phone-only {\r\n  @media (max-width: 599px) { @content; }\r\n}\r\n\r\n@mixin for-tablet-portrait-up {\r\n  @media (min-width: 600px) { @content; }\r\n}\r\n\r\n@mixin for-tablet-landscape-up {\r\n  @media (min-width: 900px) { @content; }\r\n}\r\n\r\n@mixin for-desktop-up {\r\n  @media (min-width: 1200px) { @content; }\r\n}\r\n\r\n@mixin for-big-desktop-up {\r\n  @media (min-width: 1800px) { @content; }\r\n}"], "mappings": ";AAGA,CAAA;AACE,SAAA;AACA,WAAA;AACA,cCMM;ADLN,iBAAA;AACA,UAAA,IAAA,MAAA;;AAGF,CAAA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,OAAA;AACA,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,OAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;AACA,UAAA;AACA,cAAA,QAAA,KAAA;;AAEA,CAPF,WAOE;AACE,WAAA;;AAIJ,CAAA;AACE,SAAA;AACA,UAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,SC5CK;;AD+CP,CAAA;AACE,SChDK;ADiDL,aAAA;AACA,eAAA,QAAA,EAAA;AACA,eAAA;AACA,eAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;;AAGF,CAAA;AACE,SCnEW;ADoEX,aAAA;AACA,eAAA,QAAA,EAAA;AACA,eAAA;AACA,eAAA;;AAGF,CAAA;AACE,SCnEO;ADoEP,aAAA;AACA,eAAA,QAAA,EAAA;AACA,eAAA;AACA,eAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;;AAEA,CAJF,kBAIE,CAAA;AACE,OAAA;;AAGF,CARF,kBAQE,CAAA;AACE,OAAA;;AAIJ,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,aAAA;;AAGA,CAPF,gBAOE;AACE,SAAA;;AAGF,CAXF,gBAWE;AACE,SAAA;AACA,aAAA;;AAGF,CAhBF,gBAgBE;AACE,SAAA;;AAGF,CApBF,gBAoBE;AACE,aAAA;;AAGF,CAxBF,gBAwBE;AACE,SAAA;;AAIF,CAlCA,eAkCA,CA7BF,gBA6BE;AACE,SAAA;AACA,aAAA;;AAGF,CAvCA,eAuCA,CAlCF,gBAkCE;AACE,SAAA;;AAIJ,CAAA;AACE,SC7IW;AD8IX,aAAA;AACA,eAAA,QAAA,EAAA;AACA,eAAA;AACA,eAAA;AACA,aAAA;;AAGF,CAAA;AACE,SC9IO;AD+IP,aAAA;AACA,eAAA,QAAA,EAAA;AACA,eAAA;AACA,eAAA;AACA,aAAA;;AEsFA,OAAA,CAAA,SAAA,EAAA;AFjFA,GAtJF;AAuJI,oBAAA;AACA,SAAA;;AAGF,GAnFF;AAoFI,oBAAA;AACA,SAAA;AACA,iBAAA;;AAGF,GAnFF;AAoFI,eAAA;AACA,SAAA;;AAGF,GA3EF;AA4EI,eAAA;AACA,WAAA;;AAGF,GA5JF;AA6JI,SAAA;;;AE+DF,OAAA,CAAA,SAAA,EAAA;AF1DA,GAjLF;AAkLI,oBAAA;;AAGF,GA7GF;AA8GI,oBAAA;;;", "names": []}
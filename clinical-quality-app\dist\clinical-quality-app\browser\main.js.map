{"version": 3, "sources": ["src/app/shared/components/component-test/component-test.component.ts", "src/app/shared/components/component-test/component-test.component.html", "src/app/app.routes.ts", "src/app/app.config.ts", "src/app/app.component.ts", "src/app/app.component.html", "src/main.ts"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { ButtonComponent } from '../buttons/button.component';\r\nimport { CheckboxComponent } from '../form-controls/checkbox/checkbox.component';\r\nimport { DropdownComponent, DropdownOption } from '../form-controls/dropdown/dropdown.component';\r\nimport { CalendarComponent } from '../form-controls/calendar/calendar.component';\r\nimport { ResultsComponent, ResultsData } from '../form-controls/results/results.component';\r\n\r\nimport { AssignedTableComponent } from '../../../features/dashboard/components/assigned-table/assigned-table.component';\r\nimport { AssignedChart } from '../../../core/data/models/chart-data.models';\r\nimport { NotesComponent } from '../notes/notes.component';\r\nimport { DemographicsComponent, DemographicsData } from '../demographics/demographics.component';\r\nimport { HitsComponent, HitData } from '../hits/hits.component';\r\nimport { MenuComponent, UserProfile, MenuItem } from '../menu/menu.component';\r\n// import { IconComponent, IconName } from '../icons/icon.component'; // Temporarily disabled\r\nimport { RefreshIconComponent } from '../icons/refresh-icon.component';\r\n\r\n@Component({\r\n  selector: 'app-component-test',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    ButtonComponent,\r\n    CheckboxComponent,\r\n    DropdownComponent,\r\n    CalendarComponent,\r\n    ResultsComponent,\r\n\r\n    AssignedTableComponent,\r\n    NotesComponent,\r\n    DemographicsComponent,\r\n    HitsComponent,\r\n    MenuComponent,\r\n    // IconComponent, // Temporarily disabled\r\n    RefreshIconComponent\r\n  ],\r\n  templateUrl: './component-test.component.html',\r\n  styleUrl: './component-test.component.scss'\r\n})\r\nexport class ComponentTestComponent {\r\n  // Button test properties\r\n  buttonClicked(type: string): void {\r\n    console.log(`${type} button clicked`);\r\n  }\r\n\r\n  // Form control test properties\r\n  isChecked = false;\r\n  selectedValue: any = null;\r\n  selectedMultipleValues: any[] = [];\r\n  selectedDate = '';\r\n  notesText = '';\r\n  resultsData: ResultsData = {\r\n    category: 'inclusions',\r\n    telehealth: false,\r\n    sys: '',\r\n    dias: '',\r\n    dateOfService: '',\r\n    notes: ''\r\n  };\r\n\r\n  exclusionsData: ResultsData = {\r\n    category: 'exclusions',\r\n    telehealth: true,\r\n    sys: '',\r\n    dias: '',\r\n    dateOfService: '',\r\n    notes: 'Sample exclusion note'\r\n  };\r\n\r\n  // Demographics data\r\n  demographicsData: DemographicsData = {\r\n    measureTitle: 'Controlling Blood Pressure (CBP)',\r\n    measureSubtitle: 'Measure',\r\n    memberId: '55820474',\r\n    memberName: 'John Dey',\r\n    dateOfBirth: '01/05/1972',\r\n    gender: 'M',\r\n    lob: 'MAHMO',\r\n    providerName: 'Nicolas Dejong PA',\r\n    npi: '882716229'\r\n  };\r\n\r\n  // Hits data\r\n  hitsData: HitData[] = [\r\n    {\r\n      id: 'hit-1',\r\n      dateOfService: '07/21/24',\r\n      systolic: 136,\r\n      diastolic: 82,\r\n      page: 2,\r\n      comment: '',\r\n      include: false\r\n    },\r\n    {\r\n      id: 'hit-2',\r\n      dateOfService: '07/21/24',\r\n      systolic: 140,\r\n      diastolic: 82,\r\n      page: 2,\r\n      comment: '',\r\n      include: false\r\n    },\r\n    {\r\n      id: 'hit-3',\r\n      dateOfService: '05/21/24',\r\n      systolic: 150,\r\n      diastolic: 90,\r\n      page: 7,\r\n      comment: '',\r\n      include: false\r\n    }\r\n  ];\r\n\r\n  // Dropdown options\r\n  reasoningOptions: DropdownOption[] = [\r\n    { value: 'acute-inpatient', label: 'Acute inpatient and ED visit' },\r\n    { value: 'end-stage-renal', label: 'End-stage renal disease' },\r\n    { value: 'frailty', label: 'Frailty: Member 81+ years as of 12/31 of the MY' },\r\n    { value: 'lidocaine', label: 'Lidocaine and Epinephrine given to patient' },\r\n    { value: 'medicare-isnp', label: 'Medicare member in an Institutional SNP (I-SNP)' },\r\n    { value: 'medicare-ltc', label: 'Medicare member living in long-term care' },\r\n    { value: 'member-66-80', label: 'Member 66-80 years as of 12/31 of the MY' },\r\n    { value: 'member-died', label: 'Member died during the MY' },\r\n    { value: 'hospice', label: 'Member in hospice anytime during the MY' },\r\n    { value: 'non-acute', label: 'Non-acute inpatient admission' },\r\n    { value: 'palliative', label: 'Palliative Care' },\r\n    { value: 'pregnancy', label: 'Pregnancy' },\r\n    { value: 'other', label: 'Other' }\r\n  ];\r\n\r\n  // Navigation data\r\n  userProfile: UserProfile = {\r\n    name: 'Jane Chu',\r\n    avatar: ''\r\n  };\r\n\r\n  menuItems: MenuItem[] = [\r\n    { label: 'Profile', route: '/profile', icon: '👤' },\r\n    { label: 'Settings', route: '/settings', icon: '⚙️' },\r\n    { label: 'Help', route: '/help', icon: '❓' },\r\n    { label: 'Logout', action: () => this.logout(), icon: '🚪' }\r\n  ];\r\n\r\n  // Icon names for testing\r\n  // iconNames: IconName[] = ['refresh', 'back', 'user', 'arrow-down', 'arrow-up', 'arrow-left', 'arrow-right', 'check', 'close', 'search', 'filter', 'sort']; // Temporarily disabled\r\n\r\n  onCheckboxChange(checked: boolean): void {\r\n    this.isChecked = checked;\r\n  }\r\n\r\n  onDropdownChange(value: any): void {\r\n    this.selectedValue = value;\r\n    console.log('Dropdown selection changed:', value);\r\n  }\r\n\r\n  onMultiDropdownChange(values: any[]): void {\r\n    this.selectedMultipleValues = values;\r\n    console.log('Multi-select dropdown changed:', values);\r\n  }\r\n\r\n  onDateChange(date: string): void {\r\n    this.selectedDate = date;\r\n    console.log('Date changed:', date);\r\n  }\r\n\r\n\r\n\r\n  onNotesChange(notes: string): void {\r\n    this.notesText = notes;\r\n    console.log('Notes changed:', notes);\r\n  }\r\n\r\n  onResultsDataChange(data: ResultsData): void {\r\n    this.resultsData = data;\r\n    console.log('Results data changed:', data);\r\n  }\r\n\r\n  onResultsTabChange(tab: string): void {\r\n    console.log('Results tab changed:', tab);\r\n  }\r\n\r\n  onDemographicsBackClick(): void {\r\n    console.log('Demographics back button clicked');\r\n  }\r\n\r\n  onHitsDataChange(data: HitData[]): void {\r\n    this.hitsData = data;\r\n    console.log('Hits data changed:', data);\r\n  }\r\n\r\n  onHitsPageClick(event: { hit: HitData, page: number }): void {\r\n    console.log('Hits page clicked:', event);\r\n  }\r\n\r\n  onHitsCommentChange(event: { hit: HitData, comment: string }): void {\r\n    console.log('Hits comment changed:', event);\r\n  }\r\n\r\n  onHitsIncludeChange(event: { hit: HitData, include: boolean }): void {\r\n    console.log('Hits include changed:', event);\r\n  }\r\n\r\n  // Sample data for assigned table\r\n  sampleCharts: AssignedChart[] = [\r\n    {\r\n      memberId: '55820474',\r\n      firstName: 'John',\r\n      middleName: '',\r\n      lastName: 'Dey',\r\n      fullName: 'John Dey',\r\n      dob: '01/05/1972',\r\n      lob: 'MA HMO',\r\n      measure: 'CBP',\r\n      measureKey: 'CBP',\r\n      gender: 'M',\r\n      filename: 'CBP_Redacted_John_Dey',\r\n      provider: {\r\n        npi: '882716229',\r\n        firstName: 'Nicolas',\r\n        lastName: 'DeJong',\r\n        fullName: 'Nicolas DeJong'\r\n      },\r\n      review1: 'Jane Chu',\r\n      review2: '-',\r\n      assigned: '04/15/25 1:30pm',\r\n      status: 'Review' as const\r\n    },\r\n    {\r\n      memberId: '302274401',\r\n      firstName: 'Alma',\r\n      middleName: 'G',\r\n      lastName: 'Anders',\r\n      fullName: 'Alma G Anders',\r\n      dob: '12/15/1953',\r\n      lob: 'MA HMO',\r\n      measure: 'CBP',\r\n      measureKey: 'CBP',\r\n      gender: 'F',\r\n      filename: 'CBP_Redacted_Alma_Anders',\r\n      provider: {\r\n        npi: '771552845',\r\n        firstName: 'Thomas',\r\n        lastName: 'Ramos',\r\n        fullName: 'Thomas Ramos'\r\n      },\r\n      review1: 'Jane Chu',\r\n      review2: '-',\r\n      assigned: '04/15/25 1:30pm',\r\n      status: 'Inactive' as const\r\n    },\r\n    {\r\n      memberId: '**********',\r\n      firstName: 'Joanne',\r\n      middleName: '',\r\n      lastName: 'Smith',\r\n      fullName: 'Joanne Smith',\r\n      dob: '06/30/1951',\r\n      lob: 'MA HMO',\r\n      measure: 'CBP',\r\n      measureKey: 'CBP',\r\n      gender: 'F',\r\n      filename: 'CBP_Redacted_Joanne_Smith',\r\n      provider: {\r\n        npi: '104297254',\r\n        firstName: 'Samantha',\r\n        lastName: 'Peterson',\r\n        fullName: 'Samantha Peterson'\r\n      },\r\n      review1: 'Jane Chu',\r\n      review2: '-',\r\n      assigned: '04/15/25 1:30pm',\r\n      status: 'Inactive' as const\r\n    }\r\n  ];\r\n\r\n  // Navigation event handlers\r\n  onLogoClick(): void {\r\n    console.log('Logo clicked');\r\n  }\r\n\r\n  onUserClick(): void {\r\n    console.log('User clicked');\r\n  }\r\n\r\n  onDropdownToggle(isOpen: boolean): void {\r\n    console.log('Dropdown toggled:', isOpen);\r\n  }\r\n\r\n  onMenuItemClick(item: MenuItem): void {\r\n    console.log('Menu item clicked:', item);\r\n  }\r\n\r\n  logout(): void {\r\n    console.log('Logout clicked');\r\n    // Add logout logic here\r\n  }\r\n}", "<div class=\"component-test-container\">\r\n  <h1 class=\"page-title\">Component Test Page</h1>\r\n  <p class=\"page-description\">This page showcases all implemented components with their different states and variations.</p>\r\n\r\n  <!-- Buttons Section -->\r\n  <section class=\"component-section\">\r\n    <h2 class=\"section-title\">Buttons</h2>\r\n    <p class=\"section-description\">\r\n      The following demos show the four button states from the Figma style guide: <strong>Inactive</strong>, <strong>Default</strong>, <strong>Hover</strong>, and <strong>Click</strong>.\r\n      Static state demos show each state individually, while interactive demos show natural state transitions on hover and click.\r\n    </p>\r\n    <div class=\"component-row\">\r\n      <div class=\"component-item\">\r\n        <h3 class=\"component-title\">Primary Button - Inactive</h3>\r\n        <div class=\"component-subtitle\">Style Guide: \"Buttons, selectors, and icons\" | Implementation: \"ButtonComponent\"</div>\r\n        <div class=\"component-demo figma-sized button-demo\">\r\n          <app-button\r\n            variant=\"primary\"\r\n            [figmaExact]=\"true\"\r\n            figmaState=\"inactive\"\r\n          >Submit</app-button>\r\n        </div>\r\n        <div class=\"component-code\">\r\n          <pre><code>&lt;app-button variant=\"primary\" figmaState=\"inactive\"&gt;Submit&lt;/app-button&gt;</code></pre>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"component-item\">\r\n        <h3 class=\"component-title\">Primary Button - Default</h3>\r\n        <div class=\"component-subtitle\">Style Guide: \"Buttons, selectors, and icons\" | Implementation: \"ButtonComponent\"</div>\r\n        <div class=\"component-demo figma-sized button-demo\">\r\n          <app-button\r\n            variant=\"primary\"\r\n            [figmaExact]=\"true\"\r\n            figmaState=\"default\"\r\n            (click)=\"buttonClicked('primary-default')\"\r\n          >Submit</app-button>\r\n        </div>\r\n        <div class=\"component-code\">\r\n          <pre><code>&lt;app-button variant=\"primary\" figmaState=\"default\"&gt;Submit&lt;/app-button&gt;</code></pre>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"component-row\">\r\n      <div class=\"component-item\">\r\n        <h3 class=\"component-title\">Primary Button - Hover</h3>\r\n        <div class=\"component-subtitle\">Style Guide: \"Buttons, selectors, and icons\" | Implementation: \"ButtonComponent\"</div>\r\n        <div class=\"component-demo figma-sized button-demo\">\r\n          <app-button\r\n            variant=\"primary\"\r\n            [figmaExact]=\"true\"\r\n            figmaState=\"hover\"\r\n            (click)=\"buttonClicked('primary-hover')\"\r\n          >Submit</app-button>\r\n        </div>\r\n        <div class=\"component-code\">\r\n          <pre><code>&lt;app-button variant=\"primary\" figmaState=\"hover\"&gt;Submit&lt;/app-button&gt;</code></pre>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"component-item\">\r\n        <h3 class=\"component-title\">Primary Button - Click</h3>\r\n        <div class=\"component-subtitle\">Style Guide: \"Buttons, selectors, and icons\" | Implementation: \"ButtonComponent\"</div>\r\n        <div class=\"component-demo figma-sized button-demo\">\r\n          <app-button\r\n            variant=\"primary\"\r\n            [figmaExact]=\"true\"\r\n            figmaState=\"click\"\r\n            (click)=\"buttonClicked('primary-click')\"\r\n          >Submit</app-button>\r\n        </div>\r\n        <div class=\"component-code\">\r\n          <pre><code>&lt;app-button variant=\"primary\" figmaState=\"click\"&gt;Submit&lt;/app-button&gt;</code></pre>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"component-row\">\r\n      <div class=\"component-item\">\r\n        <h3 class=\"component-title\">Secondary Button - Default</h3>\r\n        <div class=\"component-subtitle\">Style Guide: \"Buttons, selectors, and icons\" | Implementation: \"ButtonComponent\"</div>\r\n        <div class=\"component-demo figma-sized button-demo\">\r\n          <app-button\r\n            variant=\"secondary\"\r\n            [figmaExact]=\"true\"\r\n            figmaState=\"default\"\r\n            (click)=\"buttonClicked('secondary-default')\"\r\n          >\r\n            <app-refresh-icon color=\"default\"></app-refresh-icon>Refresh charts\r\n          </app-button>\r\n        </div>\r\n        <div class=\"component-code\">\r\n          <pre><code>&lt;app-button variant=\"secondary\" figmaState=\"default\"&gt;Refresh charts&lt;/app-button&gt;</code></pre>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"component-item\">\r\n        <h3 class=\"component-title\">Secondary Button - Hover</h3>\r\n        <div class=\"component-subtitle\">Style Guide: \"Buttons, selectors, and icons\" | Implementation: \"ButtonComponent\"</div>\r\n        <div class=\"component-demo figma-sized button-demo\">\r\n          <app-button\r\n            variant=\"secondary\"\r\n            [figmaExact]=\"true\"\r\n            figmaState=\"hover\"\r\n            (click)=\"buttonClicked('secondary-hover')\"\r\n          >\r\n            <app-refresh-icon color=\"hover\"></app-refresh-icon>Refresh charts\r\n          </app-button>\r\n        </div>\r\n        <div class=\"component-code\">\r\n          <pre><code>&lt;app-button variant=\"secondary\" figmaState=\"hover\"&gt;Refresh charts&lt;/app-button&gt;</code></pre>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"component-row\">\r\n      <div class=\"component-item\">\r\n        <h3 class=\"component-title\">Secondary Button - Click</h3>\r\n        <div class=\"component-subtitle\">Style Guide: \"Buttons, selectors, and icons\" | Implementation: \"ButtonComponent\"</div>\r\n        <div class=\"component-demo figma-sized button-demo\">\r\n          <app-button\r\n            variant=\"secondary\"\r\n            [figmaExact]=\"true\"\r\n            figmaState=\"click\"\r\n            (click)=\"buttonClicked('secondary-click')\"\r\n          >\r\n            <app-refresh-icon color=\"click\"></app-refresh-icon>Refresh charts\r\n          </app-button>\r\n        </div>\r\n        <div class=\"component-code\">\r\n          <pre><code>&lt;app-button variant=\"secondary\" figmaState=\"click\"&gt;Refresh charts&lt;/app-button&gt;</code></pre>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Interactive Demo Buttons -->\r\n    <h3 class=\"subsection-title\">Interactive Button Demos</h3>\r\n    <p class=\"subsection-description\">These buttons demonstrate natural state transitions. Hover and click to see the Figma-specified color changes.</p>\r\n    <div class=\"component-row\">\r\n      <div class=\"component-item\">\r\n        <h3 class=\"component-title\">Interactive Primary Button Demo</h3>\r\n        <div class=\"component-subtitle\">Hover and click to see natural state transitions</div>\r\n        <div class=\"component-demo figma-sized button-demo\">\r\n          <app-button\r\n            variant=\"primary\"\r\n            [figmaExact]=\"true\"\r\n            (click)=\"buttonClicked('interactive-primary')\"\r\n          >Submit</app-button>\r\n        </div>\r\n        <div class=\"component-code\">\r\n          <pre><code>&lt;app-button variant=\"primary\" [figmaExact]=\"true\"&gt;Submit&lt;/app-button&gt;</code></pre>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"component-item\">\r\n        <h3 class=\"component-title\">Interactive Secondary Button Demo</h3>\r\n        <div class=\"component-subtitle\">Hover and click to see natural state transitions</div>\r\n        <div class=\"component-demo figma-sized button-demo\">\r\n          <app-button\r\n            variant=\"secondary\"\r\n            [figmaExact]=\"true\"\r\n            (click)=\"buttonClicked('interactive-secondary')\"\r\n          >\r\n            <app-refresh-icon></app-refresh-icon>Refresh charts\r\n          </app-button>\r\n        </div>\r\n        <div class=\"component-code\">\r\n          <pre><code>&lt;app-button variant=\"secondary\" [figmaExact]=\"true\"&gt;Refresh charts&lt;/app-button&gt;</code></pre>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </section>\r\n\r\n  <!-- Form Controls Section -->\r\n  <section class=\"component-section\">\r\n    <h2 class=\"section-title\">Form Controls</h2>\r\n    <div class=\"component-row\">\r\n      <div class=\"component-item\">\r\n        <h3 class=\"component-title\">Checkbox</h3>\r\n        <div class=\"component-subtitle\">Style Guide: \"Buttons, selectors, and icons\" | Implementation: \"CheckboxComponent\"</div>\r\n        <div class=\"component-demo figma-sized checkbox-demo\">\r\n          <app-checkbox\r\n            label=\"Checkbox\"\r\n            [(ngModel)]=\"isChecked\"\r\n          ></app-checkbox>\r\n        </div>\r\n        <div class=\"component-code\">\r\n          <pre><code>&lt;app-checkbox label=\"Checkbox\"&gt;&lt;/app-checkbox&gt;</code></pre>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- New Form Controls Section -->\r\n    <h3 class=\"subsection-title\">Advanced Form Controls</h3>\r\n\r\n    <div class=\"component-row\">\r\n      <div class=\"component-item\">\r\n        <h3 class=\"component-title\">Dropdown (Single Select)</h3>\r\n        <div class=\"component-demo figma-sized form-demo\">\r\n          <app-dropdown\r\n            label=\"Reasoning\"\r\n            placeholder=\"Select reasoning\"\r\n            [options]=\"reasoningOptions\"\r\n            [(ngModel)]=\"selectedValue\"\r\n            (selectionChange)=\"onDropdownChange($event)\"\r\n          ></app-dropdown>\r\n        </div>\r\n        <div class=\"component-code\">\r\n          <pre><code>&lt;app-dropdown label=\"Reasoning\" [options]=\"options\"&gt;&lt;/app-dropdown&gt;</code></pre>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"component-item\">\r\n        <h3 class=\"component-title\">Dropdown (Multi Select)</h3>\r\n        <div class=\"component-demo figma-sized form-demo\">\r\n          <app-dropdown\r\n            label=\"Multiple Reasoning\"\r\n            placeholder=\"Select multiple\"\r\n            [options]=\"reasoningOptions\"\r\n            [multiSelect]=\"true\"\r\n            [(ngModel)]=\"selectedMultipleValues\"\r\n            (selectionChange)=\"onMultiDropdownChange($event)\"\r\n          ></app-dropdown>\r\n        </div>\r\n        <div class=\"component-code\">\r\n          <pre><code>&lt;app-dropdown [multiSelect]=\"true\" [options]=\"options\"&gt;&lt;/app-dropdown&gt;</code></pre>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"component-row\">\r\n      <div class=\"component-item\">\r\n        <h3 class=\"component-title\">Calendar</h3>\r\n        <div class=\"component-demo figma-sized form-demo\">\r\n          <app-calendar\r\n            label=\"Date of Service\"\r\n            placeholder=\"Date of Service\"\r\n            [(ngModel)]=\"selectedDate\"\r\n            (dateChange)=\"onDateChange($event)\"\r\n          ></app-calendar>\r\n        </div>\r\n        <div class=\"component-code\">\r\n          <pre><code>&lt;app-calendar label=\"Date of Service\"&gt;&lt;/app-calendar&gt;</code></pre>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"component-item\">\r\n        <h3 class=\"component-title\">Calendar (Required)</h3>\r\n        <div class=\"component-demo figma-sized form-demo\">\r\n          <app-calendar\r\n            label=\"Required Date\"\r\n            placeholder=\"Select date\"\r\n            [required]=\"true\"\r\n            [(ngModel)]=\"selectedDate\"\r\n            (dateChange)=\"onDateChange($event)\"\r\n          ></app-calendar>\r\n        </div>\r\n        <div class=\"component-code\">\r\n          <pre><code>&lt;app-calendar [required]=\"true\"&gt;&lt;/app-calendar&gt;</code></pre>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n\r\n\r\n    <div class=\"component-row\">\r\n      <div class=\"component-item\">\r\n        <h3 class=\"component-title\">Results</h3>\r\n        <div class=\"component-demo figma-sized results-demo\">\r\n          <app-results\r\n            title=\"Results\"\r\n            [(ngModel)]=\"resultsData\"\r\n            (dataChange)=\"onResultsDataChange($event)\"\r\n            (tabChange)=\"onResultsTabChange($event)\"\r\n          ></app-results>\r\n        </div>\r\n        <div class=\"component-code\">\r\n          <pre><code>&lt;app-results title=\"Results\"&gt;&lt;/app-results&gt;</code></pre>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"component-item\">\r\n        <h3 class=\"component-title\">Results (Exclusions Tab)</h3>\r\n        <div class=\"component-demo figma-sized results-demo\">\r\n          <app-results\r\n            title=\"Findings\"\r\n            [(ngModel)]=\"exclusionsData\"\r\n          ></app-results>\r\n        </div>\r\n        <div class=\"component-code\">\r\n          <pre><code>&lt;app-results title=\"Findings\" [ngModel]=\"exclusionsData\"&gt;&lt;/app-results&gt;</code></pre>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </section>\r\n\r\n\r\n\r\n  <!-- Tables Section -->\r\n  <section class=\"component-section\">\r\n    <h2 class=\"section-title\">Tables</h2>\r\n    <div class=\"component-row full-width\">\r\n      <div class=\"component-item full-width\">\r\n        <h3 class=\"component-title\">Assigned Table (Dashboard Table)</h3>\r\n        <div class=\"component-demo table-demo\">\r\n          <app-assigned-table [charts]=\"sampleCharts\"></app-assigned-table>\r\n        </div>\r\n        <div class=\"component-code\">\r\n          <pre><code>&lt;app-assigned-table [charts]=\"charts\"&gt;&lt;/app-assigned-table&gt;</code></pre>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </section>\r\n\r\n  <!-- Phase 2 Components Section -->\r\n  <section class=\"component-section\">\r\n    <h2 class=\"section-title\">Phase 2 Components</h2>\r\n\r\n    <!-- Notes Component -->\r\n    <div class=\"component-row\">\r\n      <div class=\"component-item\">\r\n        <h3 class=\"component-title\">Notes</h3>\r\n        <div class=\"component-subtitle\">Style Guide: \"Notes\" | Implementation: \"NotesComponent\"</div>\r\n        <div class=\"component-demo figma-sized form-demo\">\r\n          <app-notes\r\n            label=\"Notes\"\r\n            placeholder=\"Notes\"\r\n            [(ngModel)]=\"notesText\"\r\n            (notesChange)=\"onNotesChange($event)\"\r\n          ></app-notes>\r\n        </div>\r\n        <div class=\"component-code\">\r\n          <pre><code>&lt;app-notes label=\"Notes\" [(ngModel)]=\"notesText\"&gt;&lt;/app-notes&gt;</code></pre>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"component-item\">\r\n        <h3 class=\"component-title\">Notes (Limited)</h3>\r\n        <div class=\"component-subtitle\">Style Guide: \"Notes\" | Implementation: \"NotesComponent\"</div>\r\n        <div class=\"component-demo figma-sized form-demo\">\r\n          <app-notes\r\n            label=\"Notes\"\r\n            placeholder=\"Notes\"\r\n            [maxLength]=\"100\"\r\n            [(ngModel)]=\"notesText\"\r\n          ></app-notes>\r\n        </div>\r\n        <div class=\"component-code\">\r\n          <pre><code>&lt;app-notes [maxLength]=\"100\"&gt;&lt;/app-notes&gt;</code></pre>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Demographics Component -->\r\n    <div class=\"component-row demographics-row full-width\">\r\n      <div class=\"component-item full-width\">\r\n        <h3 class=\"component-title\">Demographics</h3>\r\n        <div class=\"component-demo figma-sized demographics-demo\">\r\n          <app-demographics\r\n            [data]=\"demographicsData\"\r\n            (backClick)=\"onDemographicsBackClick()\"\r\n          ></app-demographics>\r\n        </div>\r\n        <div class=\"component-code\">\r\n          <pre><code>&lt;app-demographics [data]=\"demographicsData\"&gt;&lt;/app-demographics&gt;</code></pre>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Hits Component -->\r\n    <div class=\"component-row full-width\">\r\n      <div class=\"component-item full-width\">\r\n        <h3 class=\"component-title\">Hits</h3>\r\n        <div class=\"component-subtitle\">Style Guide: \"Hits\" | Implementation: \"HitsComponent\"</div>\r\n        <div class=\"component-demo figma-sized hits-demo\">\r\n          <app-hits\r\n            title=\"Hits\"\r\n            [data]=\"hitsData\"\r\n            (dataChange)=\"onHitsDataChange($event)\"\r\n            (pageClick)=\"onHitsPageClick($event)\"\r\n            (commentChange)=\"onHitsCommentChange($event)\"\r\n            (includeChange)=\"onHitsIncludeChange($event)\"\r\n          ></app-hits>\r\n        </div>\r\n        <div class=\"component-code\">\r\n          <pre><code>&lt;app-hits title=\"Hits\" [data]=\"hitsData\"&gt;&lt;/app-hits&gt;</code></pre>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Menu Component -->\r\n    <div class=\"component-row full-width\">\r\n      <div class=\"component-item full-width\">\r\n        <h3 class=\"component-title\">Menu</h3>\r\n        <div class=\"component-demo figma-sized menu-demo\">\r\n          <app-menu\r\n            logoSrc=\"assets/logos/Stellarus_logo_2C_blacktype.png?v=2024\"\r\n            logoAlt=\"Stellarus Logo\"\r\n            [user]=\"userProfile\"\r\n            [menuItems]=\"menuItems\"\r\n            (logoClick)=\"onLogoClick()\"\r\n            (userClick)=\"onUserClick()\"\r\n            (dropdownToggle)=\"onDropdownToggle($event)\"\r\n            (menuItemClick)=\"onMenuItemClick($event)\"\r\n          ></app-menu>\r\n        </div>\r\n        <div class=\"component-code\">\r\n          <pre><code>&lt;app-menu [user]=\"userProfile\" [menuItems]=\"menuItems\"&gt;&lt;/app-menu&gt;</code></pre>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Icons Component - Temporarily disabled due to SVG binding issues -->\r\n    <!--\r\n    <div class=\"component-row full-width\">\r\n      <div class=\"component-item full-width\">\r\n        <h3 class=\"component-title\">Icons</h3>\r\n        <div class=\"component-subtitle\">Style Guide: \"Buttons, selectors, and icons\" | Implementation: \"IconComponent\"</div>\r\n        <div class=\"component-demo\">\r\n          <div class=\"icons-grid\">\r\n            <div *ngFor=\"let iconName of iconNames\" class=\"icon-demo-item\">\r\n              <app-icon [name]=\"iconName\" size=\"md\" color=\"primary\" [clickable]=\"true\"></app-icon>\r\n              <span class=\"icon-label\">{{ iconName }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"component-code\">\r\n          <pre><code>&lt;app-icon name=\"refresh\" size=\"md\" color=\"primary\" [clickable]=\"true\"&gt;&lt;/app-icon&gt;</code></pre>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    -->\r\n\r\n\r\n  </section>\r\n</div>", "import { Routes } from '@angular/router';\r\nimport { PdfViewerTestComponent } from '@features/chart-review/components/pdf-viewer-test/pdf-viewer-test.component';\r\nimport { ComponentTestComponent } from '@shared/components/component-test/component-test.component';\r\n\r\nexport const routes: Routes = [\r\n  {\r\n    path: '',\r\n    redirectTo: 'dashboard',\r\n    pathMatch: 'full'\r\n  },\r\n  {\r\n    path: 'dashboard',\r\n    loadChildren: () => import('@features/dashboard/dashboard.module').then(m => m.DashboardModule)\r\n  },\r\n  {\r\n    path: 'chart-review',\r\n    loadChildren: () => import('@features/chart-review/chart-review.module').then(m => m.ChartReviewModule)\r\n  },\r\n  {\r\n    path: 'auth',\r\n    loadChildren: () => import('@features/auth/auth.module').then(m => m.AuthModule)\r\n  },\r\n  {\r\n    path: 'pdf-test',\r\n    component: PdfViewerTestComponent\r\n  },\r\n  {\r\n    path: 'component-test',\r\n    component: ComponentTestComponent\r\n  }\r\n];\r\n", "import { ApplicationConfig, provideZoneChangeDetection } from '@angular/core';\r\nimport { provideRouter } from '@angular/router';\r\nimport { provideHttpClient, withFetch } from '@angular/common/http';\r\n\r\nimport { routes } from '@app/app.routes';\r\nimport { provideClientHydration, withEventReplay } from '@angular/platform-browser';\r\n\r\nexport const appConfig: ApplicationConfig = {\r\n  providers: [\r\n    provideZoneChangeDetection({ eventCoalescing: true }),\r\n    provideRouter(routes),\r\n    provideClientHydration(withEventReplay()),\r\n    provideHttpClient(withFetch())\r\n  ]\r\n};\r\n", "import { Component, OnInit, AfterViewInit, ViewChild, ElementRef, Renderer2 } from '@angular/core';\r\nimport { RouterOutlet, Router, NavigationEnd } from '@angular/router';\r\nimport { filter } from 'rxjs/operators';\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  imports: [RouterOutlet],\r\n  templateUrl: './app.component.html',\r\n  styleUrl: './app.component.scss'\r\n})\r\nexport class AppComponent implements OnInit, AfterViewInit {\r\n  title = 'clinical-quality-app';\r\n  @ViewChild(RouterOutlet) routerOutlet!: RouterOutlet;\r\n  \r\n  constructor(\r\n    private router: Router,\r\n    private renderer: Renderer2,\r\n    private el: ElementRef\r\n  ) {\r\n    console.log('AppComponent constructor');\r\n  }\r\n  \r\n  ngOnInit() {\r\n    console.log('AppComponent ngOnInit');\r\n    \r\n    // Log router events to see if there are duplicate navigation events\r\n    this.router.events.pipe(\r\n      filter(event => event instanceof NavigationEnd)\r\n    ).subscribe((event: NavigationEnd) => {\r\n      console.log('Navigation event:', event.url);\r\n      console.log('Current router config:', JSON.stringify(this.router.config, (key, value) => {\r\n        if (key === 'component' && typeof value === 'function') {\r\n          return value.name;\r\n        }\r\n        if (key === '_loadedConfig') {\r\n          return {\r\n            routes: value.routes.map((r: any) => ({\r\n              path: r.path,\r\n              component: r.component?.name\r\n            }))\r\n          };\r\n        }\r\n        return value;\r\n      }, 2));\r\n      \r\n      // Check for multiple router outlets after navigation\r\n      if (typeof document !== 'undefined') {\r\n        const routerOutlets = document.querySelectorAll('router-outlet');\r\n        console.log(`Found ${routerOutlets.length} router-outlet elements after navigation to ${event.url}`);\r\n        \r\n        // Log the DOM structure around router outlets\r\n        routerOutlets.forEach((outlet, index) => {\r\n          console.log(`Router outlet #${index + 1} parent:`, outlet.parentElement?.tagName);\r\n          console.log(`Router outlet #${index + 1} siblings:`, outlet.parentElement?.children.length);\r\n        });\r\n      }\r\n    });\r\n  }\r\n  \r\n  ngAfterViewInit() {\r\n    console.log('AppComponent ngAfterViewInit');\r\n    \r\n    // Only try to access the component if the outlet is activated\r\n    if (this.routerOutlet && this.routerOutlet.isActivated) {\r\n      console.log('Router outlet component:', this.routerOutlet.component?.constructor.name);\r\n    } else {\r\n      console.log('Router outlet is not yet activated');\r\n    }\r\n    \r\n    // Check for multiple router outlets\r\n    if (typeof document !== 'undefined') {\r\n      const routerOutlets = document.querySelectorAll('router-outlet');\r\n      console.log(`Found ${routerOutlets.length} router-outlet elements in ngAfterViewInit`);\r\n      \r\n      // Add a MutationObserver to detect DOM changes that might add router outlets\r\n      if (typeof MutationObserver !== 'undefined') {\r\n        const observer = new MutationObserver((mutations) => {\r\n          const currentOutlets = document.querySelectorAll('router-outlet');\r\n          if (currentOutlets.length > 1) {\r\n            console.log(`MutationObserver detected ${currentOutlets.length} router outlets`);\r\n            console.log('DOM mutation detected that might have added router outlets');\r\n          }\r\n        });\r\n        \r\n        observer.observe(document.body, {\r\n          childList: true,\r\n          subtree: true\r\n        });\r\n      }\r\n    }\r\n  }\r\n}\r\n", "<router-outlet></router-outlet>\r\n", "import { bootstrapApplication } from '@angular/platform-browser';\r\nimport { appConfig } from '@app/app.config';\r\nimport { AppComponent } from '@app/app.component';\r\n\r\n// Configure the PDF worker source for ngx-extended-pdf-viewer (only in browser)\r\nif (typeof window !== 'undefined') {\r\n  console.log('Setting PDF worker source path to: /assets/pdf.worker.mjs');\r\n  (window as any).pdfWorkerSrc = '/assets/pdf.worker.mjs';\r\n\r\n  // Add a check to verify the worker path was set correctly\r\n  setTimeout(() => {\r\n    console.log('Verifying PDF worker configuration:');\r\n    console.log('window.pdfWorkerSrc =', (window as any).pdfWorkerSrc);\r\n\r\n    // Check if the file exists by creating a test request\r\n    const testRequest = new XMLHttpRequest();\r\n    testRequest.open('HEAD', '/assets/pdf.worker.mjs', true);\r\n    testRequest.onreadystatechange = function() {\r\n      if (this.readyState === this.DONE) {\r\n        console.log('PDF worker file check:', this.status);\r\n        if (this.status === 200) {\r\n          console.log('PDF worker file exists and is accessible');\r\n        } else {\r\n          console.error('PDF worker file not found or not accessible!');\r\n        }\r\n      }\r\n    };\r\n    testRequest.send();\r\n  }, 1000);\r\n}\r\n\r\nbootstrapApplication(AppComponent, appConfig)\r\n  .catch((err) => console.error(err));\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCM,IAAO,yBAAP,MAAO,wBAAsB;;EAEjC,cAAc,MAAY;AACxB,YAAQ,IAAI,GAAG,IAAI,iBAAiB;EACtC;;EAGA,YAAY;EACZ,gBAAqB;EACrB,yBAAgC,CAAA;EAChC,eAAe;EACf,YAAY;EACZ,cAA2B;IACzB,UAAU;IACV,YAAY;IACZ,KAAK;IACL,MAAM;IACN,eAAe;IACf,OAAO;;EAGT,iBAA8B;IAC5B,UAAU;IACV,YAAY;IACZ,KAAK;IACL,MAAM;IACN,eAAe;IACf,OAAO;;;EAIT,mBAAqC;IACnC,cAAc;IACd,iBAAiB;IACjB,UAAU;IACV,YAAY;IACZ,aAAa;IACb,QAAQ;IACR,KAAK;IACL,cAAc;IACd,KAAK;;;EAIP,WAAsB;IACpB;MACE,IAAI;MACJ,eAAe;MACf,UAAU;MACV,WAAW;MACX,MAAM;MACN,SAAS;MACT,SAAS;;IAEX;MACE,IAAI;MACJ,eAAe;MACf,UAAU;MACV,WAAW;MACX,MAAM;MACN,SAAS;MACT,SAAS;;IAEX;MACE,IAAI;MACJ,eAAe;MACf,UAAU;MACV,WAAW;MACX,MAAM;MACN,SAAS;MACT,SAAS;;;;EAKb,mBAAqC;IACnC,EAAE,OAAO,mBAAmB,OAAO,+BAA8B;IACjE,EAAE,OAAO,mBAAmB,OAAO,0BAAyB;IAC5D,EAAE,OAAO,WAAW,OAAO,kDAAiD;IAC5E,EAAE,OAAO,aAAa,OAAO,6CAA4C;IACzE,EAAE,OAAO,iBAAiB,OAAO,kDAAiD;IAClF,EAAE,OAAO,gBAAgB,OAAO,2CAA0C;IAC1E,EAAE,OAAO,gBAAgB,OAAO,2CAA0C;IAC1E,EAAE,OAAO,eAAe,OAAO,4BAA2B;IAC1D,EAAE,OAAO,WAAW,OAAO,0CAAyC;IACpE,EAAE,OAAO,aAAa,OAAO,gCAA+B;IAC5D,EAAE,OAAO,cAAc,OAAO,kBAAiB;IAC/C,EAAE,OAAO,aAAa,OAAO,YAAW;IACxC,EAAE,OAAO,SAAS,OAAO,QAAO;;;EAIlC,cAA2B;IACzB,MAAM;IACN,QAAQ;;EAGV,YAAwB;IACtB,EAAE,OAAO,WAAW,OAAO,YAAY,MAAM,YAAI;IACjD,EAAE,OAAO,YAAY,OAAO,aAAa,MAAM,eAAI;IACnD,EAAE,OAAO,QAAQ,OAAO,SAAS,MAAM,SAAG;IAC1C,EAAE,OAAO,UAAU,QAAQ,MAAM,KAAK,OAAM,GAAI,MAAM,YAAI;;;;EAM5D,iBAAiB,SAAgB;AAC/B,SAAK,YAAY;EACnB;EAEA,iBAAiB,OAAU;AACzB,SAAK,gBAAgB;AACrB,YAAQ,IAAI,+BAA+B,KAAK;EAClD;EAEA,sBAAsB,QAAa;AACjC,SAAK,yBAAyB;AAC9B,YAAQ,IAAI,kCAAkC,MAAM;EACtD;EAEA,aAAa,MAAY;AACvB,SAAK,eAAe;AACpB,YAAQ,IAAI,iBAAiB,IAAI;EACnC;EAIA,cAAc,OAAa;AACzB,SAAK,YAAY;AACjB,YAAQ,IAAI,kBAAkB,KAAK;EACrC;EAEA,oBAAoB,MAAiB;AACnC,SAAK,cAAc;AACnB,YAAQ,IAAI,yBAAyB,IAAI;EAC3C;EAEA,mBAAmB,KAAW;AAC5B,YAAQ,IAAI,wBAAwB,GAAG;EACzC;EAEA,0BAAuB;AACrB,YAAQ,IAAI,kCAAkC;EAChD;EAEA,iBAAiB,MAAe;AAC9B,SAAK,WAAW;AAChB,YAAQ,IAAI,sBAAsB,IAAI;EACxC;EAEA,gBAAgB,OAAqC;AACnD,YAAQ,IAAI,sBAAsB,KAAK;EACzC;EAEA,oBAAoB,OAAwC;AAC1D,YAAQ,IAAI,yBAAyB,KAAK;EAC5C;EAEA,oBAAoB,OAAyC;AAC3D,YAAQ,IAAI,yBAAyB,KAAK;EAC5C;;EAGA,eAAgC;IAC9B;MACE,UAAU;MACV,WAAW;MACX,YAAY;MACZ,UAAU;MACV,UAAU;MACV,KAAK;MACL,KAAK;MACL,SAAS;MACT,YAAY;MACZ,QAAQ;MACR,UAAU;MACV,UAAU;QACR,KAAK;QACL,WAAW;QACX,UAAU;QACV,UAAU;;MAEZ,SAAS;MACT,SAAS;MACT,UAAU;MACV,QAAQ;;IAEV;MACE,UAAU;MACV,WAAW;MACX,YAAY;MACZ,UAAU;MACV,UAAU;MACV,KAAK;MACL,KAAK;MACL,SAAS;MACT,YAAY;MACZ,QAAQ;MACR,UAAU;MACV,UAAU;QACR,KAAK;QACL,WAAW;QACX,UAAU;QACV,UAAU;;MAEZ,SAAS;MACT,SAAS;MACT,UAAU;MACV,QAAQ;;IAEV;MACE,UAAU;MACV,WAAW;MACX,YAAY;MACZ,UAAU;MACV,UAAU;MACV,KAAK;MACL,KAAK;MACL,SAAS;MACT,YAAY;MACZ,QAAQ;MACR,UAAU;MACV,UAAU;QACR,KAAK;QACL,WAAW;QACX,UAAU;QACV,UAAU;;MAEZ,SAAS;MACT,SAAS;MACT,UAAU;MACV,QAAQ;;;;EAKZ,cAAW;AACT,YAAQ,IAAI,cAAc;EAC5B;EAEA,cAAW;AACT,YAAQ,IAAI,cAAc;EAC5B;EAEA,iBAAiB,QAAe;AAC9B,YAAQ,IAAI,qBAAqB,MAAM;EACzC;EAEA,gBAAgB,MAAc;AAC5B,YAAQ,IAAI,sBAAsB,IAAI;EACxC;EAEA,SAAM;AACJ,YAAQ,IAAI,gBAAgB;EAE9B;;qCAhQW,yBAAsB;EAAA;yEAAtB,yBAAsB,WAAA,CAAA,CAAA,oBAAA,CAAA,GAAA,OAAA,KAAA,MAAA,IAAA,QAAA,CAAA,CAAA,GAAA,0BAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,oBAAA,GAAA,CAAA,GAAA,kBAAA,eAAA,aAAA,GAAA,CAAA,WAAA,WAAA,cAAA,YAAA,GAAA,YAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,WAAA,WAAA,cAAA,WAAA,GAAA,SAAA,YAAA,GAAA,CAAA,WAAA,WAAA,cAAA,SAAA,GAAA,SAAA,YAAA,GAAA,CAAA,WAAA,WAAA,cAAA,SAAA,GAAA,SAAA,YAAA,GAAA,CAAA,WAAA,aAAA,cAAA,WAAA,GAAA,SAAA,YAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,WAAA,aAAA,cAAA,SAAA,GAAA,SAAA,YAAA,GAAA,CAAA,SAAA,OAAA,GAAA,CAAA,WAAA,aAAA,cAAA,SAAA,GAAA,SAAA,YAAA,GAAA,CAAA,SAAA,OAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,wBAAA,GAAA,CAAA,WAAA,WAAA,GAAA,SAAA,YAAA,GAAA,CAAA,WAAA,aAAA,GAAA,SAAA,YAAA,GAAA,CAAA,GAAA,kBAAA,eAAA,eAAA,GAAA,CAAA,SAAA,YAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,GAAA,kBAAA,eAAA,WAAA,GAAA,CAAA,SAAA,aAAA,eAAA,oBAAA,GAAA,iBAAA,mBAAA,WAAA,SAAA,GAAA,CAAA,SAAA,sBAAA,eAAA,mBAAA,GAAA,iBAAA,mBAAA,WAAA,eAAA,SAAA,GAAA,CAAA,SAAA,mBAAA,eAAA,mBAAA,GAAA,iBAAA,cAAA,SAAA,GAAA,CAAA,SAAA,iBAAA,eAAA,eAAA,GAAA,iBAAA,cAAA,YAAA,SAAA,GAAA,CAAA,GAAA,kBAAA,eAAA,cAAA,GAAA,CAAA,SAAA,WAAA,GAAA,iBAAA,cAAA,aAAA,SAAA,GAAA,CAAA,SAAA,YAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,GAAA,iBAAA,YAAA,GAAA,CAAA,GAAA,kBAAA,YAAA,GAAA,CAAA,GAAA,kBAAA,YAAA,GAAA,CAAA,GAAA,QAAA,GAAA,CAAA,SAAA,SAAA,eAAA,SAAA,GAAA,iBAAA,eAAA,SAAA,GAAA,CAAA,SAAA,SAAA,eAAA,SAAA,GAAA,iBAAA,aAAA,SAAA,GAAA,CAAA,GAAA,iBAAA,oBAAA,YAAA,GAAA,CAAA,GAAA,kBAAA,eAAA,mBAAA,GAAA,CAAA,GAAA,aAAA,MAAA,GAAA,CAAA,GAAA,kBAAA,eAAA,WAAA,GAAA,CAAA,SAAA,QAAA,GAAA,cAAA,aAAA,iBAAA,iBAAA,MAAA,GAAA,CAAA,GAAA,kBAAA,eAAA,WAAA,GAAA,CAAA,WAAA,uDAAA,WAAA,kBAAA,GAAA,aAAA,aAAA,kBAAA,iBAAA,QAAA,WAAA,CAAA,GAAA,UAAA,SAAA,gCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACzCnC,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAsC,GAAA,MAAA,CAAA;AACb,MAAA,iBAAA,GAAA,qBAAA;AAAmB,MAAA,uBAAA;AAC1C,MAAA,yBAAA,GAAA,KAAA,CAAA;AAA4B,MAAA,iBAAA,GAAA,4FAAA;AAA0F,MAAA,uBAAA;AAGtH,MAAA,yBAAA,GAAA,WAAA,CAAA,EAAmC,GAAA,MAAA,CAAA;AACP,MAAA,iBAAA,GAAA,SAAA;AAAO,MAAA,uBAAA;AACjC,MAAA,yBAAA,GAAA,KAAA,CAAA;AACE,MAAA,iBAAA,GAAA,+EAAA;AAA4E,MAAA,yBAAA,IAAA,QAAA;AAAQ,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA;AAAS,MAAA,iBAAA,IAAA,IAAA;AAAE,MAAA,yBAAA,IAAA,QAAA;AAAQ,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA;AAAS,MAAA,iBAAA,IAAA,IAAA;AAAE,MAAA,yBAAA,IAAA,QAAA;AAAQ,MAAA,iBAAA,IAAA,OAAA;AAAK,MAAA,uBAAA;AAAS,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,yBAAA,IAAA,QAAA;AAAQ,MAAA,iBAAA,IAAA,OAAA;AAAK,MAAA,uBAAA;AAAS,MAAA,iBAAA,IAAA,gIAAA;AAErL,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,OAAA,CAAA,EAA2B,IAAA,OAAA,CAAA,EACG,IAAA,MAAA,CAAA;AACE,MAAA,iBAAA,IAAA,2BAAA;AAAyB,MAAA,uBAAA;AACrD,MAAA,yBAAA,IAAA,OAAA,CAAA;AAAgC,MAAA,iBAAA,IAAA,kFAAA;AAAgF,MAAA,uBAAA;AAChH,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAoD,IAAA,cAAA,EAAA;AAKjD,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA,EAAa;AAEtB,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA4B,IAAA,KAAA,EACrB,IAAA,MAAA;AAAM,MAAA,iBAAA,IAAA,yEAAA;AAAmF,MAAA,uBAAA,EAAO,EAAM,EACvG;AAGR,MAAA,yBAAA,IAAA,OAAA,CAAA,EAA4B,IAAA,MAAA,CAAA;AACE,MAAA,iBAAA,IAAA,0BAAA;AAAwB,MAAA,uBAAA;AACpD,MAAA,yBAAA,IAAA,OAAA,CAAA;AAAgC,MAAA,iBAAA,IAAA,kFAAA;AAAgF,MAAA,uBAAA;AAChH,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAoD,IAAA,cAAA,EAAA;AAKhD,MAAA,qBAAA,SAAA,SAAA,+DAAA;AAAA,eAAS,IAAA,cAAc,iBAAiB;MAAC,CAAA;AAC1C,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA,EAAa;AAEtB,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA4B,IAAA,KAAA,EACrB,IAAA,MAAA;AAAM,MAAA,iBAAA,IAAA,wEAAA;AAAkF,MAAA,uBAAA,EAAO,EAAM,EACtG,EACF;AAGR,MAAA,yBAAA,IAAA,OAAA,CAAA,EAA2B,IAAA,OAAA,CAAA,EACG,IAAA,MAAA,CAAA;AACE,MAAA,iBAAA,IAAA,wBAAA;AAAsB,MAAA,uBAAA;AAClD,MAAA,yBAAA,IAAA,OAAA,CAAA;AAAgC,MAAA,iBAAA,IAAA,kFAAA;AAAgF,MAAA,uBAAA;AAChH,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAoD,IAAA,cAAA,EAAA;AAKhD,MAAA,qBAAA,SAAA,SAAA,+DAAA;AAAA,eAAS,IAAA,cAAc,eAAe;MAAC,CAAA;AACxC,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA,EAAa;AAEtB,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA4B,IAAA,KAAA,EACrB,IAAA,MAAA;AAAM,MAAA,iBAAA,IAAA,sEAAA;AAAgF,MAAA,uBAAA,EAAO,EAAM,EACpG;AAGR,MAAA,yBAAA,IAAA,OAAA,CAAA,EAA4B,IAAA,MAAA,CAAA;AACE,MAAA,iBAAA,IAAA,wBAAA;AAAsB,MAAA,uBAAA;AAClD,MAAA,yBAAA,IAAA,OAAA,CAAA;AAAgC,MAAA,iBAAA,IAAA,kFAAA;AAAgF,MAAA,uBAAA;AAChH,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAoD,IAAA,cAAA,EAAA;AAKhD,MAAA,qBAAA,SAAA,SAAA,+DAAA;AAAA,eAAS,IAAA,cAAc,eAAe;MAAC,CAAA;AACxC,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA,EAAa;AAEtB,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA4B,IAAA,KAAA,EACrB,IAAA,MAAA;AAAM,MAAA,iBAAA,IAAA,sEAAA;AAAgF,MAAA,uBAAA,EAAO,EAAM,EACpG,EACF;AAGR,MAAA,yBAAA,IAAA,OAAA,CAAA,EAA2B,IAAA,OAAA,CAAA,EACG,IAAA,MAAA,CAAA;AACE,MAAA,iBAAA,IAAA,4BAAA;AAA0B,MAAA,uBAAA;AACtD,MAAA,yBAAA,IAAA,OAAA,CAAA;AAAgC,MAAA,iBAAA,IAAA,kFAAA;AAAgF,MAAA,uBAAA;AAChH,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAoD,IAAA,cAAA,EAAA;AAKhD,MAAA,qBAAA,SAAA,SAAA,+DAAA;AAAA,eAAS,IAAA,cAAc,mBAAmB;MAAC,CAAA;AAE3C,MAAA,oBAAA,IAAA,oBAAA,EAAA;AAAqD,MAAA,iBAAA,IAAA,iBAAA;AACvD,MAAA,uBAAA,EAAa;AAEf,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA4B,IAAA,KAAA,EACrB,IAAA,MAAA;AAAM,MAAA,iBAAA,IAAA,kFAAA;AAA4F,MAAA,uBAAA,EAAO,EAAM,EAChH;AAGR,MAAA,yBAAA,IAAA,OAAA,CAAA,EAA4B,IAAA,MAAA,CAAA;AACE,MAAA,iBAAA,IAAA,0BAAA;AAAwB,MAAA,uBAAA;AACpD,MAAA,yBAAA,IAAA,OAAA,CAAA;AAAgC,MAAA,iBAAA,IAAA,kFAAA;AAAgF,MAAA,uBAAA;AAChH,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAoD,IAAA,cAAA,EAAA;AAKhD,MAAA,qBAAA,SAAA,SAAA,+DAAA;AAAA,eAAS,IAAA,cAAc,iBAAiB;MAAC,CAAA;AAEzC,MAAA,oBAAA,IAAA,oBAAA,EAAA;AAAmD,MAAA,iBAAA,IAAA,iBAAA;AACrD,MAAA,uBAAA,EAAa;AAEf,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA4B,IAAA,KAAA,EACrB,IAAA,MAAA;AAAM,MAAA,iBAAA,IAAA,gFAAA;AAA0F,MAAA,uBAAA,EAAO,EAAM,EAC9G,EACF;AAGR,MAAA,yBAAA,IAAA,OAAA,CAAA,EAA2B,KAAA,OAAA,CAAA,EACG,KAAA,MAAA,CAAA;AACE,MAAA,iBAAA,KAAA,0BAAA;AAAwB,MAAA,uBAAA;AACpD,MAAA,yBAAA,KAAA,OAAA,CAAA;AAAgC,MAAA,iBAAA,KAAA,kFAAA;AAAgF,MAAA,uBAAA;AAChH,MAAA,yBAAA,KAAA,OAAA,EAAA,EAAoD,KAAA,cAAA,EAAA;AAKhD,MAAA,qBAAA,SAAA,SAAA,gEAAA;AAAA,eAAS,IAAA,cAAc,iBAAiB;MAAC,CAAA;AAEzC,MAAA,oBAAA,KAAA,oBAAA,EAAA;AAAmD,MAAA,iBAAA,KAAA,iBAAA;AACrD,MAAA,uBAAA,EAAa;AAEf,MAAA,yBAAA,KAAA,OAAA,EAAA,EAA4B,KAAA,KAAA,EACrB,KAAA,MAAA;AAAM,MAAA,iBAAA,KAAA,gFAAA;AAA0F,MAAA,uBAAA,EAAO,EAAM,EAC9G,EACF;AAIR,MAAA,yBAAA,KAAA,MAAA,EAAA;AAA6B,MAAA,iBAAA,KAAA,0BAAA;AAAwB,MAAA,uBAAA;AACrD,MAAA,yBAAA,KAAA,KAAA,EAAA;AAAkC,MAAA,iBAAA,KAAA,gHAAA;AAA8G,MAAA,uBAAA;AAChJ,MAAA,yBAAA,KAAA,OAAA,CAAA,EAA2B,KAAA,OAAA,CAAA,EACG,KAAA,MAAA,CAAA;AACE,MAAA,iBAAA,KAAA,iCAAA;AAA+B,MAAA,uBAAA;AAC3D,MAAA,yBAAA,KAAA,OAAA,CAAA;AAAgC,MAAA,iBAAA,KAAA,kDAAA;AAAgD,MAAA,uBAAA;AAChF,MAAA,yBAAA,KAAA,OAAA,EAAA,EAAoD,KAAA,cAAA,EAAA;AAIhD,MAAA,qBAAA,SAAA,SAAA,gEAAA;AAAA,eAAS,IAAA,cAAc,qBAAqB;MAAC,CAAA;AAC9C,MAAA,iBAAA,KAAA,QAAA;AAAM,MAAA,uBAAA,EAAa;AAEtB,MAAA,yBAAA,KAAA,OAAA,EAAA,EAA4B,KAAA,KAAA,EACrB,KAAA,MAAA;AAAM,MAAA,iBAAA,KAAA,uEAAA;AAAiF,MAAA,uBAAA,EAAO,EAAM,EACrG;AAGR,MAAA,yBAAA,KAAA,OAAA,CAAA,EAA4B,KAAA,MAAA,CAAA;AACE,MAAA,iBAAA,KAAA,mCAAA;AAAiC,MAAA,uBAAA;AAC7D,MAAA,yBAAA,KAAA,OAAA,CAAA;AAAgC,MAAA,iBAAA,KAAA,kDAAA;AAAgD,MAAA,uBAAA;AAChF,MAAA,yBAAA,KAAA,OAAA,EAAA,EAAoD,KAAA,cAAA,EAAA;AAIhD,MAAA,qBAAA,SAAA,SAAA,gEAAA;AAAA,eAAS,IAAA,cAAc,uBAAuB;MAAC,CAAA;AAE/C,MAAA,oBAAA,KAAA,kBAAA;AAAqC,MAAA,iBAAA,KAAA,iBAAA;AACvC,MAAA,uBAAA,EAAa;AAEf,MAAA,yBAAA,KAAA,OAAA,EAAA,EAA4B,KAAA,KAAA,EACrB,KAAA,MAAA;AAAM,MAAA,iBAAA,KAAA,iFAAA;AAA2F,MAAA,uBAAA,EAAO,EAAM,EAC/G,EACF,EACF;AAIR,MAAA,yBAAA,KAAA,WAAA,CAAA,EAAmC,KAAA,MAAA,CAAA;AACP,MAAA,iBAAA,KAAA,eAAA;AAAa,MAAA,uBAAA;AACvC,MAAA,yBAAA,KAAA,OAAA,CAAA,EAA2B,KAAA,OAAA,CAAA,EACG,KAAA,MAAA,CAAA;AACE,MAAA,iBAAA,KAAA,UAAA;AAAQ,MAAA,uBAAA;AACpC,MAAA,yBAAA,KAAA,OAAA,CAAA;AAAgC,MAAA,iBAAA,KAAA,oFAAA;AAAkF,MAAA,uBAAA;AAClH,MAAA,yBAAA,KAAA,OAAA,EAAA,EAAsD,KAAA,gBAAA,EAAA;AAGlD,MAAA,2BAAA,iBAAA,SAAA,wEAAA,QAAA;AAAA,QAAA,6BAAA,IAAA,WAAA,MAAA,MAAA,IAAA,YAAA;AAAA,eAAA;MAAA,CAAA;AACD,MAAA,uBAAA,EAAe;AAElB,MAAA,yBAAA,KAAA,OAAA,EAAA,EAA4B,KAAA,KAAA,EACrB,KAAA,MAAA;AAAM,MAAA,iBAAA,KAAA,gDAAA;AAA0D,MAAA,uBAAA,EAAO,EAAM,EAC9E,EACF;AAIR,MAAA,yBAAA,KAAA,MAAA,EAAA;AAA6B,MAAA,iBAAA,KAAA,wBAAA;AAAsB,MAAA,uBAAA;AAEnD,MAAA,yBAAA,KAAA,OAAA,CAAA,EAA2B,KAAA,OAAA,CAAA,EACG,KAAA,MAAA,CAAA;AACE,MAAA,iBAAA,KAAA,0BAAA;AAAwB,MAAA,uBAAA;AACpD,MAAA,yBAAA,KAAA,OAAA,EAAA,EAAkD,KAAA,gBAAA,EAAA;AAK9C,MAAA,2BAAA,iBAAA,SAAA,wEAAA,QAAA;AAAA,QAAA,6BAAA,IAAA,eAAA,MAAA,MAAA,IAAA,gBAAA;AAAA,eAAA;MAAA,CAAA;AACA,MAAA,qBAAA,mBAAA,SAAA,0EAAA,QAAA;AAAA,eAAmB,IAAA,iBAAA,MAAA;MAAwB,CAAA;AAC5C,MAAA,uBAAA,EAAe;AAElB,MAAA,yBAAA,KAAA,OAAA,EAAA,EAA4B,KAAA,KAAA,EACrB,KAAA,MAAA;AAAM,MAAA,iBAAA,KAAA,qEAAA;AAA+E,MAAA,uBAAA,EAAO,EAAM,EACnG;AAGR,MAAA,yBAAA,KAAA,OAAA,CAAA,EAA4B,KAAA,MAAA,CAAA;AACE,MAAA,iBAAA,KAAA,yBAAA;AAAuB,MAAA,uBAAA;AACnD,MAAA,yBAAA,KAAA,OAAA,EAAA,EAAkD,KAAA,gBAAA,EAAA;AAM9C,MAAA,2BAAA,iBAAA,SAAA,wEAAA,QAAA;AAAA,QAAA,6BAAA,IAAA,wBAAA,MAAA,MAAA,IAAA,yBAAA;AAAA,eAAA;MAAA,CAAA;AACA,MAAA,qBAAA,mBAAA,SAAA,0EAAA,QAAA;AAAA,eAAmB,IAAA,sBAAA,MAAA;MAA6B,CAAA;AACjD,MAAA,uBAAA,EAAe;AAElB,MAAA,yBAAA,KAAA,OAAA,EAAA,EAA4B,KAAA,KAAA,EACrB,KAAA,MAAA;AAAM,MAAA,iBAAA,KAAA,wEAAA;AAAkF,MAAA,uBAAA,EAAO,EAAM,EACtG,EACF;AAGR,MAAA,yBAAA,KAAA,OAAA,CAAA,EAA2B,KAAA,OAAA,CAAA,EACG,KAAA,MAAA,CAAA;AACE,MAAA,iBAAA,KAAA,UAAA;AAAQ,MAAA,uBAAA;AACpC,MAAA,yBAAA,KAAA,OAAA,EAAA,EAAkD,KAAA,gBAAA,EAAA;AAI9C,MAAA,2BAAA,iBAAA,SAAA,wEAAA,QAAA;AAAA,QAAA,6BAAA,IAAA,cAAA,MAAA,MAAA,IAAA,eAAA;AAAA,eAAA;MAAA,CAAA;AACA,MAAA,qBAAA,cAAA,SAAA,qEAAA,QAAA;AAAA,eAAc,IAAA,aAAA,MAAA;MAAoB,CAAA;AACnC,MAAA,uBAAA,EAAe;AAElB,MAAA,yBAAA,KAAA,OAAA,EAAA,EAA4B,KAAA,KAAA,EACrB,KAAA,MAAA;AAAM,MAAA,iBAAA,KAAA,uDAAA;AAAiE,MAAA,uBAAA,EAAO,EAAM,EACrF;AAGR,MAAA,yBAAA,KAAA,OAAA,CAAA,EAA4B,KAAA,MAAA,CAAA;AACE,MAAA,iBAAA,KAAA,qBAAA;AAAmB,MAAA,uBAAA;AAC/C,MAAA,yBAAA,KAAA,OAAA,EAAA,EAAkD,KAAA,gBAAA,EAAA;AAK9C,MAAA,2BAAA,iBAAA,SAAA,wEAAA,QAAA;AAAA,QAAA,6BAAA,IAAA,cAAA,MAAA,MAAA,IAAA,eAAA;AAAA,eAAA;MAAA,CAAA;AACA,MAAA,qBAAA,cAAA,SAAA,qEAAA,QAAA;AAAA,eAAc,IAAA,aAAA,MAAA;MAAoB,CAAA;AACnC,MAAA,uBAAA,EAAe;AAElB,MAAA,yBAAA,KAAA,OAAA,EAAA,EAA4B,KAAA,KAAA,EACrB,KAAA,MAAA;AAAM,MAAA,iBAAA,KAAA,iDAAA;AAA2D,MAAA,uBAAA,EAAO,EAAM,EAC/E,EACF;AAKR,MAAA,yBAAA,KAAA,OAAA,CAAA,EAA2B,KAAA,OAAA,CAAA,EACG,KAAA,MAAA,CAAA;AACE,MAAA,iBAAA,KAAA,SAAA;AAAO,MAAA,uBAAA;AACnC,MAAA,yBAAA,KAAA,OAAA,EAAA,EAAqD,KAAA,eAAA,EAAA;AAGjD,MAAA,2BAAA,iBAAA,SAAA,uEAAA,QAAA;AAAA,QAAA,6BAAA,IAAA,aAAA,MAAA,MAAA,IAAA,cAAA;AAAA,eAAA;MAAA,CAAA;AACA,MAAA,qBAAA,cAAA,SAAA,oEAAA,QAAA;AAAA,eAAc,IAAA,oBAAA,MAAA;MAA2B,CAAA,EAAC,aAAA,SAAA,mEAAA,QAAA;AAAA,eAC7B,IAAA,mBAAA,MAAA;MAA0B,CAAA;AACxC,MAAA,uBAAA,EAAc;AAEjB,MAAA,yBAAA,KAAA,OAAA,EAAA,EAA4B,KAAA,KAAA,EACrB,KAAA,MAAA;AAAM,MAAA,iBAAA,KAAA,6CAAA;AAAuD,MAAA,uBAAA,EAAO,EAAM,EAC3E;AAGR,MAAA,yBAAA,KAAA,OAAA,CAAA,EAA4B,KAAA,MAAA,CAAA;AACE,MAAA,iBAAA,KAAA,0BAAA;AAAwB,MAAA,uBAAA;AACpD,MAAA,yBAAA,KAAA,OAAA,EAAA,EAAqD,KAAA,eAAA,EAAA;AAGjD,MAAA,2BAAA,iBAAA,SAAA,uEAAA,QAAA;AAAA,QAAA,6BAAA,IAAA,gBAAA,MAAA,MAAA,IAAA,iBAAA;AAAA,eAAA;MAAA,CAAA;AACD,MAAA,uBAAA,EAAc;AAEjB,MAAA,yBAAA,KAAA,OAAA,EAAA,EAA4B,KAAA,KAAA,EACrB,KAAA,MAAA;AAAM,MAAA,iBAAA,KAAA,yEAAA;AAAmF,MAAA,uBAAA,EAAO,EAAM,EACvG,EACF,EACF;AAMR,MAAA,yBAAA,KAAA,WAAA,CAAA,EAAmC,KAAA,MAAA,CAAA;AACP,MAAA,iBAAA,KAAA,QAAA;AAAM,MAAA,uBAAA;AAChC,MAAA,yBAAA,KAAA,OAAA,EAAA,EAAsC,KAAA,OAAA,EAAA,EACG,KAAA,MAAA,CAAA;AACT,MAAA,iBAAA,KAAA,kCAAA;AAAgC,MAAA,uBAAA;AAC5D,MAAA,yBAAA,KAAA,OAAA,EAAA;AACE,MAAA,oBAAA,KAAA,sBAAA,EAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,KAAA,OAAA,EAAA,EAA4B,KAAA,KAAA,EACrB,KAAA,MAAA;AAAM,MAAA,iBAAA,KAAA,6DAAA;AAAuE,MAAA,uBAAA,EAAO,EAAM,EAC3F,EACF,EACF;AAIR,MAAA,yBAAA,KAAA,WAAA,CAAA,EAAmC,KAAA,MAAA,CAAA;AACP,MAAA,iBAAA,KAAA,oBAAA;AAAkB,MAAA,uBAAA;AAG5C,MAAA,yBAAA,KAAA,OAAA,CAAA,EAA2B,KAAA,OAAA,CAAA,EACG,KAAA,MAAA,CAAA;AACE,MAAA,iBAAA,KAAA,OAAA;AAAK,MAAA,uBAAA;AACjC,MAAA,yBAAA,KAAA,OAAA,CAAA;AAAgC,MAAA,iBAAA,KAAA,yDAAA;AAAuD,MAAA,uBAAA;AACvF,MAAA,yBAAA,KAAA,OAAA,EAAA,EAAkD,KAAA,aAAA,EAAA;AAI9C,MAAA,2BAAA,iBAAA,SAAA,qEAAA,QAAA;AAAA,QAAA,6BAAA,IAAA,WAAA,MAAA,MAAA,IAAA,YAAA;AAAA,eAAA;MAAA,CAAA;AACA,MAAA,qBAAA,eAAA,SAAA,mEAAA,QAAA;AAAA,eAAe,IAAA,cAAA,MAAA;MAAqB,CAAA;AACrC,MAAA,uBAAA,EAAY;AAEf,MAAA,yBAAA,KAAA,OAAA,EAAA,EAA4B,KAAA,KAAA,EACrB,KAAA,MAAA;AAAM,MAAA,iBAAA,KAAA,+DAAA;AAAyE,MAAA,uBAAA,EAAO,EAAM,EAC7F;AAGR,MAAA,yBAAA,KAAA,OAAA,CAAA,EAA4B,KAAA,MAAA,CAAA;AACE,MAAA,iBAAA,KAAA,iBAAA;AAAe,MAAA,uBAAA;AAC3C,MAAA,yBAAA,KAAA,OAAA,CAAA;AAAgC,MAAA,iBAAA,KAAA,yDAAA;AAAuD,MAAA,uBAAA;AACvF,MAAA,yBAAA,KAAA,OAAA,EAAA,EAAkD,KAAA,aAAA,EAAA;AAK9C,MAAA,2BAAA,iBAAA,SAAA,qEAAA,QAAA;AAAA,QAAA,6BAAA,IAAA,WAAA,MAAA,MAAA,IAAA,YAAA;AAAA,eAAA;MAAA,CAAA;AACD,MAAA,uBAAA,EAAY;AAEf,MAAA,yBAAA,KAAA,OAAA,EAAA,EAA4B,KAAA,KAAA,EACrB,KAAA,MAAA;AAAM,MAAA,iBAAA,KAAA,2CAAA;AAAqD,MAAA,uBAAA,EAAO,EAAM,EACzE,EACF;AAIR,MAAA,yBAAA,KAAA,OAAA,EAAA,EAAuD,KAAA,OAAA,EAAA,EACd,KAAA,MAAA,CAAA;AACT,MAAA,iBAAA,KAAA,cAAA;AAAY,MAAA,uBAAA;AACxC,MAAA,yBAAA,KAAA,OAAA,EAAA,EAA0D,KAAA,oBAAA,EAAA;AAGtD,MAAA,qBAAA,aAAA,SAAA,0EAAA;AAAA,eAAa,IAAA,wBAAA;MAAyB,CAAA;AACvC,MAAA,uBAAA,EAAmB;AAEtB,MAAA,yBAAA,KAAA,OAAA,EAAA,EAA4B,KAAA,KAAA,EACrB,KAAA,MAAA;AAAM,MAAA,iBAAA,KAAA,iEAAA;AAA2E,MAAA,uBAAA,EAAO,EAAM,EAC/F,EACF;AAIR,MAAA,yBAAA,KAAA,OAAA,EAAA,EAAsC,KAAA,OAAA,EAAA,EACG,KAAA,MAAA,CAAA;AACT,MAAA,iBAAA,KAAA,MAAA;AAAI,MAAA,uBAAA;AAChC,MAAA,yBAAA,KAAA,OAAA,CAAA;AAAgC,MAAA,iBAAA,KAAA,uDAAA;AAAqD,MAAA,uBAAA;AACrF,MAAA,yBAAA,KAAA,OAAA,EAAA,EAAkD,KAAA,YAAA,EAAA;AAI9C,MAAA,qBAAA,cAAA,SAAA,iEAAA,QAAA;AAAA,eAAc,IAAA,iBAAA,MAAA;MAAwB,CAAA,EAAC,aAAA,SAAA,gEAAA,QAAA;AAAA,eAC1B,IAAA,gBAAA,MAAA;MAAuB,CAAA,EAAC,iBAAA,SAAA,oEAAA,QAAA;AAAA,eACpB,IAAA,oBAAA,MAAA;MAA2B,CAAA,EAAC,iBAAA,SAAA,oEAAA,QAAA;AAAA,eAC5B,IAAA,oBAAA,MAAA;MAA2B,CAAA;AAC7C,MAAA,uBAAA,EAAW;AAEd,MAAA,yBAAA,KAAA,OAAA,EAAA,EAA4B,KAAA,KAAA,EACrB,KAAA,MAAA;AAAM,MAAA,iBAAA,KAAA,sDAAA;AAAgE,MAAA,uBAAA,EAAO,EAAM,EACpF,EACF;AAIR,MAAA,yBAAA,KAAA,OAAA,EAAA,EAAsC,KAAA,OAAA,EAAA,EACG,KAAA,MAAA,CAAA;AACT,MAAA,iBAAA,KAAA,MAAA;AAAI,MAAA,uBAAA;AAChC,MAAA,yBAAA,KAAA,OAAA,EAAA,EAAkD,KAAA,YAAA,EAAA;AAM9C,MAAA,qBAAA,aAAA,SAAA,kEAAA;AAAA,eAAa,IAAA,YAAA;MAAa,CAAA,EAAC,aAAA,SAAA,kEAAA;AAAA,eACd,IAAA,YAAA;MAAa,CAAA,EAAC,kBAAA,SAAA,qEAAA,QAAA;AAAA,eACT,IAAA,iBAAA,MAAA;MAAwB,CAAA,EAAC,iBAAA,SAAA,oEAAA,QAAA;AAAA,eAC1B,IAAA,gBAAA,MAAA;MAAuB,CAAA;AACzC,MAAA,uBAAA,EAAW;AAEd,MAAA,yBAAA,KAAA,OAAA,EAAA,EAA4B,KAAA,KAAA,EACrB,KAAA,MAAA;AAAM,MAAA,iBAAA,KAAA,oEAAA;AAA8E,MAAA,uBAAA,EAAO,EAAM,EAClG,EACF,EACF,EAwBE;;;AAjaA,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,cAAA,IAAA;AAeA,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,cAAA,IAAA;AAkBA,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,cAAA,IAAA;AAgBA,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,cAAA,IAAA;AAkBA,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,cAAA,IAAA;AAkBA,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,cAAA,IAAA;AAoBA,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,cAAA,IAAA;AAuBA,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,cAAA,IAAA;AAeA,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,cAAA,IAAA;AAuBA,MAAA,oBAAA,EAAA;AAAA,MAAA,2BAAA,WAAA,IAAA,SAAA;AAmBA,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,gBAAA;AACA,MAAA,2BAAA,WAAA,IAAA,aAAA;AAeA,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,gBAAA,EAA4B,eAAA,IAAA;AAE5B,MAAA,2BAAA,WAAA,IAAA,sBAAA;AAiBA,MAAA,oBAAA,EAAA;AAAA,MAAA,2BAAA,WAAA,IAAA,YAAA;AAeA,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,YAAA,IAAA;AACA,MAAA,2BAAA,WAAA,IAAA,YAAA;AAkBA,MAAA,oBAAA,EAAA;AAAA,MAAA,2BAAA,WAAA,IAAA,WAAA;AAeA,MAAA,oBAAA,CAAA;AAAA,MAAA,2BAAA,WAAA,IAAA,cAAA;AAmBkB,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,UAAA,IAAA,YAAA;AAsBlB,MAAA,oBAAA,EAAA;AAAA,MAAA,2BAAA,WAAA,IAAA,SAAA;AAgBA,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,aAAA,GAAA;AACA,MAAA,2BAAA,WAAA,IAAA,SAAA;AAeA,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,gBAAA;AAkBA,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,QAAA;AAqBA,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,WAAA,EAAoB,aAAA,IAAA,SAAA;;;IDzX5B;IACA;IAAW;IAAA;IAAA;IACX;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;;IAEA;EAAoB,GAAA,QAAA,CAAA,y5KAAA,EAAA,CAAA;;;sEAKX,wBAAsB,CAAA;UAvBlC;uBACW,sBAAoB,YAClB,MAAI,SACP;MACP;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;MACA;MACA;MACA;MACA;;MAEA;OACD,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SAAA,QAAA,CAAA,okJAAA,EAAA,CAAA;;;;6EAIU,wBAAsB,EAAA,WAAA,0BAAA,UAAA,wEAAA,YAAA,GAAA,CAAA;AAAA,GAAA;;;AErC5B,IAAM,SAAiB;EAC5B;IACE,MAAM;IACN,YAAY;IACZ,WAAW;;EAEb;IACE,MAAM;IACN,cAAc,MAAM,OAAO,qBAAsC,EAAE,KAAK,OAAK,EAAE,eAAe;;EAEhG;IACE,MAAM;IACN,cAAc,MAAM,OAAO,qBAA4C,EAAE,KAAK,OAAK,EAAE,iBAAiB;;EAExG;IACE,MAAM;IACN,cAAc,MAAM,OAAO,qBAA4B,EAAE,KAAK,OAAK,EAAE,UAAU;;EAEjF;IACE,MAAM;IACN,WAAW;;EAEb;IACE,MAAM;IACN,WAAW;;;;;ACrBR,IAAM,YAA+B;EAC1C,WAAW;IACT,2BAA2B,EAAE,iBAAiB,KAAI,CAAE;IACpD,cAAc,MAAM;IACpB,uBAAuB,gBAAe,CAAE;IACxC,kBAAkB,UAAS,CAAE;;;;;ACF3B,IAAO,eAAP,MAAO,cAAY;EAKb;EACA;EACA;EANV,QAAQ;EACiB;EAEzB,YACU,QACA,UACA,IAAc;AAFd,SAAA,SAAA;AACA,SAAA,WAAA;AACA,SAAA,KAAA;AAER,YAAQ,IAAI,0BAA0B;EACxC;EAEA,WAAQ;AACN,YAAQ,IAAI,uBAAuB;AAGnC,SAAK,OAAO,OAAO,KACjB,OAAO,WAAS,iBAAiB,aAAa,CAAC,EAC/C,UAAU,CAAC,UAAwB;AACnC,cAAQ,IAAI,qBAAqB,MAAM,GAAG;AAC1C,cAAQ,IAAI,0BAA0B,KAAK,UAAU,KAAK,OAAO,QAAQ,CAAC,KAAK,UAAS;AACtF,YAAI,QAAQ,eAAe,OAAO,UAAU,YAAY;AACtD,iBAAO,MAAM;QACf;AACA,YAAI,QAAQ,iBAAiB;AAC3B,iBAAO;YACL,QAAQ,MAAM,OAAO,IAAI,CAAC,OAAY;cACpC,MAAM,EAAE;cACR,WAAW,EAAE,WAAW;cACxB;;QAEN;AACA,eAAO;MACT,GAAG,CAAC,CAAC;AAGL,UAAI,OAAO,aAAa,aAAa;AACnC,cAAM,gBAAgB,SAAS,iBAAiB,eAAe;AAC/D,gBAAQ,IAAI,SAAS,cAAc,MAAM,+CAA+C,MAAM,GAAG,EAAE;AAGnG,sBAAc,QAAQ,CAAC,QAAQ,UAAS;AACtC,kBAAQ,IAAI,kBAAkB,QAAQ,CAAC,YAAY,OAAO,eAAe,OAAO;AAChF,kBAAQ,IAAI,kBAAkB,QAAQ,CAAC,cAAc,OAAO,eAAe,SAAS,MAAM;QAC5F,CAAC;MACH;IACF,CAAC;EACH;EAEA,kBAAe;AACb,YAAQ,IAAI,8BAA8B;AAG1C,QAAI,KAAK,gBAAgB,KAAK,aAAa,aAAa;AACtD,cAAQ,IAAI,4BAA4B,KAAK,aAAa,WAAW,YAAY,IAAI;IACvF,OAAO;AACL,cAAQ,IAAI,oCAAoC;IAClD;AAGA,QAAI,OAAO,aAAa,aAAa;AACnC,YAAM,gBAAgB,SAAS,iBAAiB,eAAe;AAC/D,cAAQ,IAAI,SAAS,cAAc,MAAM,4CAA4C;AAGrF,UAAI,OAAO,qBAAqB,aAAa;AAC3C,cAAM,WAAW,IAAI,iBAAiB,CAAC,cAAa;AAClD,gBAAM,iBAAiB,SAAS,iBAAiB,eAAe;AAChE,cAAI,eAAe,SAAS,GAAG;AAC7B,oBAAQ,IAAI,6BAA6B,eAAe,MAAM,iBAAiB;AAC/E,oBAAQ,IAAI,4DAA4D;UAC1E;QACF,CAAC;AAED,iBAAS,QAAQ,SAAS,MAAM;UAC9B,WAAW;UACX,SAAS;SACV;MACH;IACF;EACF;;qCAhFW,eAAY,4BAAA,MAAA,GAAA,4BAAA,SAAA,GAAA,4BAAA,UAAA,CAAA;EAAA;yEAAZ,eAAY,WAAA,CAAA,CAAA,UAAA,CAAA,GAAA,WAAA,SAAA,mBAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;4BAEZ,cAAY,CAAA;;;;;;;;ACZzB,MAAA,oBAAA,GAAA,eAAA;;oBDMY,YAAY,GAAA,eAAA,EAAA,CAAA;;;sEAIX,cAAY,CAAA;UANxB;uBACW,YAAU,SACX,CAAC,YAAY,GAAC,UAAA,sCAAA,CAAA;6EAME,cAAY,CAAA;UAApC;WAAU,YAAY;;;;6EAFZ,cAAY,EAAA,WAAA,gBAAA,UAAA,4BAAA,YAAA,GAAA,CAAA;AAAA,GAAA;;;AELzB,IAAI,OAAO,WAAW,aAAa;AACjC,UAAQ,IAAI,2DAA2D;AACtE,SAAe,eAAe;AAG/B,aAAW,MAAK;AACd,YAAQ,IAAI,qCAAqC;AACjD,YAAQ,IAAI,yBAA0B,OAAe,YAAY;AAGjE,UAAM,cAAc,IAAI,eAAc;AACtC,gBAAY,KAAK,QAAQ,0BAA0B,IAAI;AACvD,gBAAY,qBAAqB,WAAA;AAC/B,UAAI,KAAK,eAAe,KAAK,MAAM;AACjC,gBAAQ,IAAI,0BAA0B,KAAK,MAAM;AACjD,YAAI,KAAK,WAAW,KAAK;AACvB,kBAAQ,IAAI,0CAA0C;QACxD,OAAO;AACL,kBAAQ,MAAM,8CAA8C;QAC9D;MACF;IACF;AACA,gBAAY,KAAI;EAClB,GAAG,GAAI;AACT;AAEA,qBAAqB,cAAc,SAAS,EACzC,MAAM,CAAC,QAAQ,QAAQ,MAAM,GAAG,CAAC;", "names": []}
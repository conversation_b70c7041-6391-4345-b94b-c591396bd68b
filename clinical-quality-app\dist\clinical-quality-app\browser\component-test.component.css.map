{"version": 3, "sources": ["src/app/shared/components/component-test/component-test.component.scss", "src/styles/_variables.scss", "src/styles/_mixins.scss"], "sourcesContent": ["@use 'variables' as variables;\r\n@use 'mixins' as mix;\r\n\r\n.component-test-container {\r\n  max-width: 1600px; // Increased from 1400px to accommodate full demographics width (1380px) + padding\r\n  margin: 0 auto;\r\n  padding: 30px;\r\n  background-color: variables.$background-gray;\r\n}\r\n\r\n.page-title {\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  line-height: 32px;\r\n  color: variables.$text-black;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.page-description {\r\n  font-size: 14px;\r\n  font-weight: 300;\r\n  line-height: 20px;\r\n  color: variables.$text-black;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.component-section {\r\n  @include mix.card;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  line-height: 32px;\r\n  color: variables.$text-black;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-description {\r\n  font-size: 14px;\r\n  color: variables.$gray-3;\r\n  margin-bottom: 24px;\r\n  line-height: 1.5;\r\n}\r\n\r\n.subsection-description {\r\n  font-size: 14px;\r\n  color: variables.$gray-3;\r\n  margin-bottom: 16px;\r\n  line-height: 1.5;\r\n}\r\n\r\n.subsection-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  line-height: 24px;\r\n  color: variables.$text-black;\r\n  margin: 24px 0 16px 0;\r\n  padding-left: 12px;\r\n  border-left: 3px solid variables.$primary-blue;\r\n}\r\n\r\n.component-subtitle {\r\n  font-size: 11px;\r\n  font-weight: 300;\r\n  line-height: 16px;\r\n  color: variables.$gray-3;\r\n  margin-bottom: 12px;\r\n  font-style: italic;\r\n}\r\n\r\n.component-row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  margin-bottom: 40px; // Increased spacing to accommodate expanded dropdown/calendar states\r\n  min-width: 100%; // Ensure full width utilization\r\n\r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  // Special handling for demographics rows that need full width\r\n  &.demographics-row {\r\n    flex-wrap: nowrap;\r\n    overflow-x: visible;\r\n  }\r\n}\r\n\r\n.component-item {\r\n  flex: 1;\r\n  min-width: 300px;\r\n\r\n  &.full-width {\r\n    flex: 0 0 100%;\r\n  }\r\n}\r\n\r\n.component-title {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  line-height: 20px;\r\n  color: variables.$text-black;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.component-demo {\r\n  padding: 20px;\r\n  background-color: variables.$white;\r\n  border-radius: variables.$border-radius-lg;\r\n  outline: variables.$border-width-default variables.$gray-1 solid;\r\n  outline-offset: -(variables.$border-width-default);\r\n  margin-bottom: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-height: 80px;\r\n  overflow-x: visible; // Prevent horizontal scrollbars, allow content to extend naturally\r\n\r\n  // Special handling for tables\r\n  &.table-demo {\r\n    justify-content: flex-start;\r\n    align-items: flex-start;\r\n    padding: 0;\r\n    display: block;\r\n  }\r\n\r\n  // Figma-specific sizing for components\r\n  &.figma-sized {\r\n    justify-content: flex-start;\r\n    align-items: flex-start;\r\n\r\n    // Button containers - match Figma button sizing\r\n    &.button-demo {\r\n      width: fit-content;\r\n      min-width: auto;\r\n      padding: 20px;\r\n    }\r\n\r\n    // Form control containers - match Figma form sizing\r\n    &.form-demo {\r\n      width: 300px; // Standard form width from Figma\r\n      min-width: 300px;\r\n      min-height: 400px; // Increased height to accommodate expanded dropdowns and calendars\r\n      padding: 20px 20px 60px 20px; // Extra bottom padding for expanded content\r\n      overflow: visible; // Allow dropdown/calendar popups to extend beyond container\r\n\r\n      app-dropdown,\r\n      app-date-picker,\r\n      app-notes {\r\n        width: 100%;\r\n      }\r\n\r\n      // Ensure dropdown and date picker have adequate space for expanded states\r\n      app-dropdown,\r\n      app-date-picker {\r\n        position: relative;\r\n        z-index: 10; // Ensure popups appear above other content\r\n      }\r\n    }\r\n\r\n    // Hits component - match Figma hits table sizing\r\n    &.hits-demo {\r\n      width: 557px; // Figma: 517px + 40px padding\r\n      min-width: 557px;\r\n      padding: 20px;\r\n\r\n      app-hits {\r\n        width: 517px; // Exact Figma width\r\n      }\r\n    }\r\n\r\n    // Demographics component - match Figma demographics sizing\r\n    &.demographics-demo {\r\n      width: 100%; // Allow full width to accommodate 1380px Figma specification\r\n      min-width: 1420px; // Ensure minimum width for 1380px component + 40px padding\r\n      padding: 20px;\r\n      overflow-x: visible; // Prevent horizontal scrollbars\r\n    }\r\n\r\n    // Results container - match Figma results sizing\r\n    &.results-demo {\r\n      width: 400px; // Estimated from Figma\r\n      min-width: 400px;\r\n      padding: 20px;\r\n    }\r\n\r\n    // Menu component - full width as per Figma\r\n    &.menu-demo {\r\n      width: 100%;\r\n      padding: 0;\r\n    }\r\n\r\n    // Status indicators - minimal sizing\r\n    &.status-demo {\r\n      width: fit-content;\r\n      min-width: auto;\r\n      padding: 20px;\r\n    }\r\n\r\n    // Checkbox - minimal sizing\r\n    &.checkbox-demo {\r\n      width: fit-content;\r\n      min-width: auto;\r\n      padding: 20px;\r\n    }\r\n  }\r\n}\r\n\r\n.component-code {\r\n  background-color: variables.$light-background;\r\n  border-radius: variables.$border-radius-lg;\r\n  padding: 12px;\r\n  overflow: auto;\r\n\r\n  pre {\r\n    margin: 0;\r\n    font-family: monospace;\r\n    font-size: 12px;\r\n    line-height: 1.5;\r\n    color: variables.$gray-4;\r\n  }\r\n}\r\n\r\n.full-width {\r\n  width: 100%;\r\n}\r\n\r\n// Responsive styles\r\n@include mix.for-tablet-portrait-up {\r\n  .component-row {\r\n    flex-wrap: nowrap;\r\n  }\r\n}\r\n\r\n@include mix.for-phone-only {\r\n  .component-item {\r\n    flex: 0 0 100%;\r\n  }\r\n}\r\n\r\n// Icon demo specific styles\r\n.icons-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\r\n  gap: 16px;\r\n  padding: 16px;\r\n}\r\n\r\n.icon-demo-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 12px;\r\n  border: 1px solid variables.$gray-1;\r\n  border-radius: 8px;\r\n  transition: background-color 0.2s ease;\r\n\r\n  &:hover {\r\n    background-color: variables.$gray-1;\r\n  }\r\n}\r\n\r\n.icon-label {\r\n  font-size: 10px;\r\n  font-family: 'Urbane', sans-serif;\r\n  font-weight: 300;\r\n  color: variables.$gray-3;\r\n  text-align: center;\r\n}\r\n\r\n", "// Color Variables\r\n$text-black: #17181A;\r\n$primary-blue: #3870B8;\r\n$hover-blue: #468CE7;\r\n$click-blue: #285082;\r\n$light-blue: #BFD0EE;\r\n$link: #0071BC;\r\n$gray-1: #F1F5F7;\r\n$gray-2: #D9E1E7;\r\n$gray-3: #547996;\r\n$gray-4: #384455;\r\n$success-green: #1AD598;\r\n$white: #FFFFFF;\r\n$background-gray: #F6F6F6;\r\n$light-background: #F9FBFC;\r\n$light-primary: #809FB8;\r\n\r\n// Opacity Variables\r\n$primary-blue-opacity-20: rgba(56, 112, 184, 0.20);\r\n$success-green-opacity-10: rgba(26, 213, 152, 0.10);\r\n$success-green-opacity-40: rgba(26, 213, 152, 0.40);\r\n\r\n// Spacing Variables\r\n$spacing-xs: 4px;\r\n$spacing-sm: 8px;\r\n$spacing-md: 12px;\r\n$spacing-lg: 16px;\r\n$spacing-xl: 20px;\r\n$spacing-xxl: 24px;\r\n$spacing-xxxl: 30px;\r\n$spacing-huge: 40px;\r\n$spacing-giant: 48px;\r\n$spacing-mega: 55px;\r\n\r\n// Border Radius\r\n$border-radius-sm: 5px;\r\n$border-radius-md: 6px;\r\n$border-radius-lg: 8px;\r\n$border-radius-xl: 10px;\r\n$border-radius-round: 90px;\r\n$border-radius-circle: 120px;\r\n\r\n// Box Shadow\r\n$box-shadow-sm: 0px 1px 2px rgba(16, 24, 40, 0.05);\r\n\r\n// Border Width\r\n$border-width-default: 1px;\r\n$border-width-thick: 1.5px;\r\n\r\n// Z-index\r\n$z-index-default: 1;\r\n$z-index-dropdown: 1000;\r\n$z-index-sticky: 1020;\r\n$z-index-fixed: 1030;\r\n$z-index-modal-backdrop: 1040;\r\n$z-index-modal: 1050;\r\n$z-index-popover: 1060;\r\n$z-index-tooltip: 1070;", "// Import variables\r\n@use 'variables' as variables;\r\n\r\n// Flexbox Mixins\r\n@mixin flex-row {\r\n  display: flex;\r\n  flex-direction: row;\r\n}\r\n\r\n@mixin flex-column {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n@mixin flex-center {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n@mixin flex-between {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n@mixin flex-start {\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  align-items: center;\r\n}\r\n\r\n@mixin flex-end {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  align-items: center;\r\n}\r\n\r\n// Layout Mixins\r\n@mixin container {\r\n  width: 100%;\r\n  padding-left: variables.$spacing-xxxl;\r\n  padding-right: variables.$spacing-xxxl;\r\n}\r\n\r\n@mixin card {\r\n  background: variables.$white;\r\n  border-radius: variables.$border-radius-lg;\r\n  outline: variables.$border-width-default variables.$gray-1 solid;\r\n  outline-offset: -(variables.$border-width-default);\r\n  padding: variables.$spacing-xl;\r\n  margin-bottom: variables.$spacing-xl;\r\n}\r\n\r\n// Button Mixins\r\n@mixin button-base {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  gap: variables.$spacing-sm;\r\n  border-radius: variables.$border-radius-lg;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  border: none;\r\n  outline: none;\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n@mixin button-primary {\r\n  @include button-base;\r\n  background: variables.$primary-blue;\r\n  color: variables.$white;\r\n  padding: variables.$spacing-sm variables.$spacing-lg;\r\n\r\n  &:hover {\r\n    background: variables.$hover-blue;\r\n  }\r\n\r\n  &:active {\r\n    background: variables.$click-blue;\r\n  }\r\n\r\n  &:disabled {\r\n    background: variables.$light-blue;\r\n    cursor: not-allowed;\r\n  }\r\n}\r\n\r\n@mixin button-secondary {\r\n  @include button-base;\r\n  background: variables.$white;\r\n  outline: variables.$border-width-default variables.$gray-2 solid;\r\n  outline-offset: -(variables.$border-width-default);\r\n  color: variables.$text-black;\r\n  padding: variables.$spacing-sm variables.$spacing-md;\r\n\r\n  &:hover {\r\n    background: variables.$gray-1;\r\n  }\r\n\r\n  &:active {\r\n    background: variables.$text-black;\r\n    color: variables.$white;\r\n  }\r\n}\r\n\r\n@mixin button-icon {\r\n  @include button-base;\r\n  gap: variables.$spacing-sm;\r\n\r\n  .icon {\r\n    width: 20px;\r\n    height: 20px;\r\n  }\r\n}\r\n\r\n// Form Element Mixins\r\n@mixin input-field {\r\n  padding: variables.$spacing-md;\r\n  border-radius: variables.$border-radius-xl;\r\n  outline: variables.$border-width-default variables.$gray-2 solid;\r\n  outline-offset: -(variables.$border-width-default);\r\n  font-size: 12px;\r\n  font-family: 'Urbane', sans-serif;\r\n  width: 100%;\r\n\r\n  &:focus {\r\n    outline-color: variables.$gray-3;\r\n  }\r\n}\r\n\r\n@mixin textarea-field {\r\n  padding: variables.$spacing-md;\r\n  border-radius: variables.$border-radius-xl;\r\n  outline: variables.$border-width-default variables.$gray-2 solid;\r\n  outline-offset: -(variables.$border-width-default);\r\n  font-size: 12px;\r\n  font-family: 'Urbane', sans-serif;\r\n  width: 100%;\r\n  min-height: 88px;\r\n\r\n  &:focus {\r\n    outline-color: variables.$gray-3;\r\n  }\r\n}\r\n\r\n@mixin checkbox {\r\n  width: 16px;\r\n  height: 16px;\r\n  border-radius: 5px; // Figma specifies 5px border radius\r\n  border: 1px solid variables.$gray-2;\r\n  background-color: variables.$white;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  appearance: none;\r\n  -webkit-appearance: none;\r\n  -moz-appearance: none;\r\n  position: relative;\r\n\r\n  &:checked {\r\n    background-color: variables.$text-black;\r\n    border-color: variables.$text-black;\r\n\r\n    &::after {\r\n      content: '';\r\n      position: absolute;\r\n      left: 4px;\r\n      top: 4px;\r\n      width: 8.33px;\r\n      height: 7.5px;\r\n      background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8.33' height='7.5' viewBox='0 0 8.33 7.5'%3E%3Cpath d='M1 3.75L3.5 6.25L7.33 1.25' stroke='white' stroke-width='1.5' fill='none' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E\");\r\n      background-repeat: no-repeat;\r\n      background-position: center;\r\n      background-size: contain;\r\n    }\r\n  }\r\n\r\n  &:hover:not(:disabled) {\r\n    border-color: variables.$gray-3;\r\n  }\r\n\r\n  &:focus {\r\n    outline: 2px solid variables.$primary-blue;\r\n    outline-offset: 2px;\r\n  }\r\n\r\n  &:disabled {\r\n    opacity: 0.5;\r\n    cursor: not-allowed;\r\n  }\r\n}\r\n\r\n// Table Mixins\r\n@mixin table-header {\r\n  padding: variables.$spacing-md;\r\n  border-bottom: variables.$border-width-default variables.$gray-1 solid;\r\n  font-weight: 500;\r\n  color: variables.$text-black;\r\n  height: 68px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n@mixin table-cell {\r\n  padding: variables.$spacing-md;\r\n  font-weight: 300;\r\n  height: 68px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-start;\r\n}\r\n\r\n\r\n\r\n// Icon Mixins\r\n@mixin icon-container {\r\n  width: 20px;\r\n  height: 20px;\r\n  position: relative;\r\n}\r\n\r\n// Status Indicators\r\n@mixin status-badge($color, $bg-color) {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: variables.$spacing-xs variables.$spacing-sm;\r\n  border-radius: variables.$border-radius-round;\r\n  background-color: $bg-color;\r\n  color: $color;\r\n  font-size: 11px;\r\n  font-weight: 300;\r\n}\r\n\r\n@mixin success-badge {\r\n  @include status-badge(variables.$success-green, variables.$success-green-opacity-10);\r\n  outline: variables.$border-width-default variables.$success-green-opacity-40 solid;\r\n  outline-offset: -(variables.$border-width-default);\r\n}\r\n\r\n// Responsive Mixins\r\n@mixin for-phone-only {\r\n  @media (max-width: 599px) { @content; }\r\n}\r\n\r\n@mixin for-tablet-portrait-up {\r\n  @media (min-width: 600px) { @content; }\r\n}\r\n\r\n@mixin for-tablet-landscape-up {\r\n  @media (min-width: 900px) { @content; }\r\n}\r\n\r\n@mixin for-desktop-up {\r\n  @media (min-width: 1200px) { @content; }\r\n}\r\n\r\n@mixin for-big-desktop-up {\r\n  @media (min-width: 1800px) { @content; }\r\n}"], "mappings": ";AAGA,CAAA;AACE,aAAA;AACA,UAAA,EAAA;AACA,WAAA;AACA,oBCMgB;;ADHlB,CAAA;AACE,aAAA;AACA,eAAA;AACA,eAAA;AACA,SCbW;ADcX,iBAAA;;AAGF,CAAA;AACE,aAAA;AACA,eAAA;AACA,eAAA;AACA,SCrBW;ADsBX,iBAAA;;AAGF,CAAA;AEoBE,cDlCM;ACmCN,iBDViB;ACWjB,WAAA,IAAA,QAAA;AACA,kBAAA;AACA,WDvBW;ACwBX,iBDxBW;ADCX,iBAAA;;AAGF,CAAA;AACE,aAAA;AACA,eAAA;AACA,eAAA;AACA,SClCW;ADmCX,iBAAA;;AAGF,CAAA;AACE,aAAA;AACA,SChCO;ADiCP,iBAAA;AACA,eAAA;;AAGF,CAAA;AACE,aAAA;AACA,SCvCO;ADwCP,iBAAA;AACA,eAAA;;AAGF,CAAA;AACE,aAAA;AACA,eAAA;AACA,eAAA;AACA,SCxDW;ADyDX,UAAA,KAAA,EAAA,KAAA;AACA,gBAAA;AACA,eAAA,IAAA,MAAA;;AAGF,CAAA;AACE,aAAA;AACA,eAAA;AACA,eAAA;AACA,SC1DO;AD2DP,iBAAA;AACA,cAAA;;AAGF,CAAA;AACE,WAAA;AACA,aAAA;AACA,OAAA;AACA,iBAAA;AACA,aAAA;;AAEA,CAPF,aAOE;AACE,iBAAA;;AAIF,CAZF,aAYE,CAAA;AACE,aAAA;AACA,cAAA;;AAIJ,CAAA;AACE,QAAA;AACA,aAAA;;AAEA,CAJF,cAIE,CAAA;AACE,QAAA,EAAA,EAAA;;AAIJ,CAAA;AACE,aAAA;AACA,eAAA;AACA,eAAA;AACA,SCtGW;ADuGX,iBAAA;;AAGF,CAAA;AACE,WAAA;AACA,oBCjGM;ADkGN,iBCzEiB;AD0EjB,WAAA,IAAA,QAAA;AACA,kBAAA;AACA,iBAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,cAAA;AACA,cAAA;;AAGA,CAdF,cAcE,CAAA;AACE,mBAAA;AACA,eAAA;AACA,WAAA;AACA,WAAA;;AAIF,CAtBF,cAsBE,CAAA;AACE,mBAAA;AACA,eAAA;;AAGA,CA3BJ,cA2BI,CALF,WAKE,CAAA;AACE,SAAA;AACA,aAAA;AACA,WAAA;;AAIF,CAlCJ,cAkCI,CAZF,WAYE,CAAA;AACE,SAAA;AACA,aAAA;AACA,cAAA;AACA,WAAA,KAAA,KAAA,KAAA;AACA,YAAA;;AAEA,CAzCN,cAyCM,CAnBJ,WAmBI,CAPF,UAOE;AAAA,CAzCN,cAyCM,CAnBJ,WAmBI,CAPF,UAOE;AAAA,CAzCN,cAyCM,CAnBJ,WAmBI,CAPF,UAOE;AAGE,SAAA;;AAIF,CAhDN,cAgDM,CA1BJ,WA0BI,CAdF,UAcE;AAAA,CAhDN,cAgDM,CA1BJ,WA0BI,CAdF,UAcE;AAEE,YAAA;AACA,WAAA;;AAKJ,CAxDJ,cAwDI,CAlCF,WAkCE,CAAA;AACE,SAAA;AACA,aAAA;AACA,WAAA;;AAEA,CA7DN,cA6DM,CAvCJ,WAuCI,CALF,UAKE;AACE,SAAA;;AAKJ,CAnEJ,cAmEI,CA7CF,WA6CE,CAAA;AACE,SAAA;AACA,aAAA;AACA,WAAA;AACA,cAAA;;AAIF,CA3EJ,cA2EI,CArDF,WAqDE,CAAA;AACE,SAAA;AACA,aAAA;AACA,WAAA;;AAIF,CAlFJ,cAkFI,CA5DF,WA4DE,CAAA;AACE,SAAA;AACA,WAAA;;AAIF,CAxFJ,cAwFI,CAlEF,WAkEE,CAAA;AACE,SAAA;AACA,aAAA;AACA,WAAA;;AAIF,CA/FJ,cA+FI,CAzEF,WAyEE,CAAA;AACE,SAAA;AACA,aAAA;AACA,WAAA;;AAKN,CAAA;AACE,oBCrMiB;ADsMjB,iBC/KiB;ADgLjB,WAAA;AACA,YAAA;;AAEA,CANF,eAME;AACE,UAAA;AACA,eAAA;AACA,aAAA;AACA,eAAA;AACA,SCnNK;;ADuNT,CAnIE;AAoIA,SAAA;;AEoBA,OAAA,CAAA,SAAA,EAAA;AFfA,GA/JF;AAgKI,eAAA;;;AEUF,OAAA,CAAA,SAAA,EAAA;AFLA,GAnJF;AAoJI,UAAA,EAAA,EAAA;;;AAKJ,CAAA;AACE,WAAA;AACA,yBAAA,OAAA,QAAA,EAAA,OAAA,KAAA,EAAA;AACA,OAAA;AACA,WAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,OAAA;AACA,WAAA;AACA,UAAA,IAAA,MAAA;AACA,iBAAA;AACA,cAAA,iBAAA,KAAA;;AAEA,CAVF,cAUE;AACE,oBC9PK;;ADkQT,CAAA;AACE,aAAA;AACA,eAAA,QAAA,EAAA;AACA,eAAA;AACA,SCpQO;ADqQP,cAAA;;", "names": []}
import {
  DemographicsComponent,
  HitsComponent,
  NgxExtendedPdfViewerComponent,
  NgxExtendedPdfViewerModule,
  NgxExtendedPdfViewerService,
  PdfViewerTestComponent,
  ResultsComponent,
  pdfDefaultOptions
} from "./chunk-FVKW5FZS.js";
import {
  ButtonComponent,
  FormsModule,
  MenuComponent,
  NgControlStatus,
  NgModel
} from "./chunk-JRLQF6CE.js";
import {
  ActivatedRoute,
  BehaviorSubject,
  CommonModule,
  Component,
  HttpClient,
  Inject,
  Injectable,
  NO_ERRORS_SCHEMA,
  NgIf,
  NgModule,
  Observable,
  PLATFORM_ID,
  Router,
  RouterModule,
  Subject,
  Subscription,
  ViewChild,
  catchError,
  from,
  inject,
  isPlatformBrowser,
  map,
  of,
  setClassMetadata,
  shareReplay,
  switchMap,
  tap,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdefineInjectable,
  ɵɵdefineInjector,
  ɵɵdefineNgModule,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵinject,
  ɵɵlistener,
  ɵɵloadQuery,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵqueryRefresh,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtwoWayBindingSet,
  ɵɵtwoWayListener,
  ɵɵtwoWayProperty,
  ɵɵviewQuery
} from "./chunk-F5HTA5WY.js";
import {
  __async
} from "./chunk-PC6IZSQ2.js";

// src/app/features/chart-review/services/pdf.service.ts
var PdfService = class _PdfService {
  http;
  platformId;
  pdfDocument = null;
  pdfDocumentSubject = new BehaviorSubject(null);
  pdfInfoSubject = new BehaviorSubject(null);
  textContentCache = /* @__PURE__ */ new Map();
  highlightsSubject = new BehaviorSubject([]);
  pdfBase64DataUrlSubject = new BehaviorSubject(null);
  // New subject for base64 data URL
  loadingSubject = new BehaviorSubject(false);
  errorSubject = new Subject();
  isBrowser;
  pdfjsLib = null;
  pdfJsLibPromise = null;
  // Promise for PDF.js library loading
  /**
   * Observable for the current PDF document
   */
  pdfDocument$ = this.pdfDocumentSubject.asObservable();
  /**
   * Observable for the PDF document information
   */
  pdfInfo$ = this.pdfInfoSubject.asObservable();
  /**
   * Observable for the highlights
   */
  highlights$ = this.highlightsSubject.asObservable();
  /**
   * Observable for the PDF data as a base64 data URL
   */
  pdfBase64DataUrl$ = this.pdfBase64DataUrlSubject.asObservable();
  /**
   * Observable for loading state
   */
  loading$ = this.loadingSubject.asObservable();
  /**
   * Observable for error messages
   */
  error$ = this.errorSubject.asObservable();
  constructor(http, platformId) {
    this.http = http;
    this.platformId = platformId;
    this.isBrowser = isPlatformBrowser(this.platformId);
    if (this.isBrowser) {
      this.pdfJsLibPromise = this.loadPdfJsLibrary();
    }
  }
  /**
   * Dynamically loads the PDF.js library.
   * This ensures it's only loaded in browser environments.
   * @returns A promise that resolves with the PDF.js library or null if an error occurs.
   */
  loadPdfJsLibrary() {
    return __async(this, null, function* () {
      try {
        console.log("[PdfService] Starting to load PDF.js library...");
        const pdfjs = yield import("./chunk-MSP5HGLP.js");
        this.pdfjsLib = pdfjs;
        this.pdfjsLib.GlobalWorkerOptions.workerSrc = "assets/pdf.worker.mjs";
        console.log("[PdfService] PDF.js library loaded and workerSrc set.");
        return this.pdfjsLib;
      } catch (error) {
        console.error("[PdfService] Error loading PDF.js library:", error);
        const errorMessage = error instanceof Error ? error.message : String(error);
        this.errorSubject.next(`Critical: PDF.js library failed to load: ${errorMessage}`);
        this.pdfjsLib = null;
        return null;
      }
    });
  }
  /**
   * Ensures PDF.js library is loaded before proceeding.
   * @returns A promise that resolves when PDF.js is ready, or rejects if it fails to load.
   */
  ensurePdfJsLibLoaded() {
    return __async(this, null, function* () {
      if (this.pdfjsLib) {
        return Promise.resolve();
      }
      if (this.pdfJsLibPromise) {
        yield this.pdfJsLibPromise;
        if (!this.pdfjsLib) {
          const message = "PDF.js library was not available after loading attempt.";
          console.error(`[PdfService] ${message}`);
          this.errorSubject.next(`Critical: ${message}`);
          throw new Error(message);
        }
        return Promise.resolve();
      }
      console.error("[PdfService] PDF.js library initialization not started.");
      throw new Error("PDF.js library initialization not started.");
    });
  }
  /**
   * Clears the text content cache
   */
  clearCache() {
    this.textContentCache.clear();
    this.pdfBase64DataUrlSubject.next(null);
  }
  /**
   * Loads a PDF document from a URL
   * @param url URL of the PDF document
   * @returns Observable of the PDF document
   */
  loadPdf(url) {
    console.log("[PdfService] loadPdf called with URL:", url);
    if (!this.isBrowser) {
      console.log("[PdfService] Not in browser environment, returning empty document");
      return of({ numPages: 0, getPage: () => Promise.reject("PDF.js not available in server environment") });
    }
    this.loadingSubject.next(true);
    this.clearCache();
    return from(this.ensurePdfJsLibLoaded()).pipe(
      switchMap(() => {
        if (!this.pdfjsLib) {
          console.error("[PdfService] loadPdf: PDF.js library still not loaded after ensurePdfJsLibLoaded resolved.");
          throw new Error("PDF.js library failed to initialize properly.");
        }
        console.log("[PdfService] Attempting to fetch PDF from URL:", url);
        return this.http.get(url, { responseType: "arraybuffer" }).pipe(tap((arrayBuffer) => {
          const dataUrl = this.arrayBufferToDataUrl(arrayBuffer);
          this.pdfBase64DataUrlSubject.next(dataUrl);
        }));
      }),
      switchMap((data) => {
        try {
          return from(this.pdfjsLib.getDocument({ data }).promise);
        } catch (error) {
          console.error("[PdfService] Error creating PDF document in getDocument:", error);
          this.errorSubject.next("Failed to process PDF data");
          throw error;
        }
      }),
      // Emits PDFDocumentProxy
      map((pdfDocProxy) => {
        this.pdfDocument = pdfDocProxy;
        this.pdfDocumentSubject.next(pdfDocProxy);
        const documentInfo = {
          numPages: pdfDocProxy.numPages,
          getPage: (pageNumber) => pdfDocProxy.getPage(pageNumber)
        };
        this.pdfInfoSubject.next(documentInfo);
        this.loadingSubject.next(false);
        return documentInfo;
      }),
      catchError((error) => {
        console.error("Error loading PDF:", error);
        this.errorSubject.next(`Failed to load PDF: ${error.message}`);
        this.loadingSubject.next(false);
        throw error;
      }),
      // Share the PDF document among multiple subscribers
      shareReplay(1)
    );
  }
  /**
   * Loads a PDF document from a base64 data URL
   * @param dataUrl Base64 data URL containing the PDF
   * @returns Observable of the PDF document
   */
  loadPdfFromDataUrl(dataUrl) {
    if (!this.isBrowser) {
      return of({ numPages: 0, getPage: () => Promise.reject("PDF.js not available in server environment") });
    }
    this.loadingSubject.next(true);
    this.clearCache();
    this.pdfBase64DataUrlSubject.next(dataUrl);
    return from(this.ensurePdfJsLibLoaded()).pipe(switchMap(() => {
      if (!this.pdfjsLib) {
        console.error("[PdfService] loadPdfFromDataUrl: PDF.js library still not loaded after ensurePdfJsLibLoaded resolved.");
        throw new Error("PDF.js library failed to initialize properly.");
      }
      const base64Data = dataUrl.split(",")[1];
      const binaryString = window.atob(base64Data);
      const arrayBuffer = new ArrayBuffer(binaryString.length);
      const uint8Array = new Uint8Array(arrayBuffer);
      for (let i = 0; i < binaryString.length; i++) {
        uint8Array[i] = binaryString.charCodeAt(i);
      }
      console.log("[PdfService] Calling pdfjsLib.getDocument... (from data URL)");
      return from(this.pdfjsLib.getDocument({ data: arrayBuffer }).promise);
    }), map((pdfDocument) => {
      console.log("[PdfService] PDF loaded from data URL with", pdfDocument.numPages, "pages");
      this.pdfDocument = pdfDocument;
      this.pdfDocumentSubject.next(pdfDocument);
      const documentInfo = {
        numPages: pdfDocument.numPages,
        getPage: (pageNumber) => pdfDocument.getPage(pageNumber)
      };
      this.pdfInfoSubject.next(documentInfo);
      this.loadingSubject.next(false);
      return documentInfo;
    }), catchError((error) => {
      console.error("[PdfService] Error loading PDF from data URL:", error);
      this.errorSubject.next(`Failed to load PDF: ${error.message}`);
      this.loadingSubject.next(false);
      throw error;
    }), shareReplay(1));
  }
  /**
   * Loads a PDF document from a File object
   * @param file File object containing the PDF
   * @returns Observable of the PDF document
   */
  loadPdfFromFile(file) {
    if (!this.isBrowser) {
      return of({ numPages: 0, getPage: () => Promise.reject("PDF.js not available in server environment") });
    }
    this.loadingSubject.next(true);
    this.clearCache();
    return from(this.ensurePdfJsLibLoaded()).pipe(
      switchMap(() => {
        if (!this.pdfjsLib) {
          console.error("[PdfService] loadPdfFromFile: PDF.js library still not loaded after ensurePdfJsLibLoaded resolved.");
          throw new Error("PDF.js library failed to initialize properly.");
        }
        return new Observable((observer) => {
          const fileReader = new FileReader();
          fileReader.onload = () => {
            console.log("[PdfService] FileReader onload triggered for loadPdfFromFile.");
            const arrayBuffer = fileReader.result;
            if (!(arrayBuffer instanceof ArrayBuffer)) {
              console.error("[PdfService] FileReader result is not an ArrayBuffer.");
              observer.error(new Error("File read result was not an ArrayBuffer."));
              return;
            }
            const dataUrl = this.arrayBufferToDataUrl(arrayBuffer);
            this.pdfBase64DataUrlSubject.next(dataUrl);
            console.log("[PdfService] Calling pdfjsLib.getDocument... (from file)");
            this.pdfjsLib.getDocument({ data: arrayBuffer }).promise.then((pdfDocument) => {
              console.log("[PdfService] pdfjsLib.getDocument resolved successfully.");
              console.log(`[PdfService] PDF loaded with ${pdfDocument.numPages} pages.`);
              this.pdfDocument = pdfDocument;
              this.pdfDocumentSubject.next(pdfDocument);
              const documentInfo = {
                numPages: pdfDocument.numPages,
                getPage: (pageNumber) => pdfDocument.getPage(pageNumber)
              };
              this.pdfInfoSubject.next(documentInfo);
              this.loadingSubject.next(false);
              observer.next(documentInfo);
              observer.complete();
            }).catch((error) => {
              console.error("[PdfService] Error loading PDF from file:", error);
              this.errorSubject.next(`Failed to load PDF: ${error.message}`);
              this.loadingSubject.next(false);
              observer.error(error);
            });
          };
          fileReader.onerror = (event) => {
            console.error("[PdfService] Error reading file:", event);
            this.errorSubject.next("Failed to read the file");
            this.loadingSubject.next(false);
            observer.error(new Error("Failed to read the file"));
          };
          fileReader.readAsArrayBuffer(file);
        });
      }),
      // Share the PDF document among multiple subscribers
      shareReplay(1)
    );
  }
  /**
   * Gets text content for a specific page
   * @param pageNumber Page number (1-based)
   * @returns Observable of the text content
   */
  getTextContent(pageNumber) {
    if (!this.isBrowser) {
      return of({ pageNumber, items: [], styles: {} });
    }
    if (this.textContentCache.has(pageNumber)) {
      return of(this.textContentCache.get(pageNumber));
    }
    return from(this.ensurePdfJsLibLoaded()).pipe(
      switchMap(() => {
        if (!this.pdfjsLib) {
          console.error("[PdfService] getTextContent: PDF.js library not loaded.");
          throw new Error("PDF.js library not loaded for getTextContent");
        }
        if (!this.pdfDocument) {
          console.error("[PdfService] getTextContent: PDF document not loaded.");
          throw new Error("PDF document not loaded for getTextContent");
        }
        return from(this.pdfDocument.getPage(pageNumber));
      }),
      // This switchMap now correctly expects Observable<PDFPageProxy>
      switchMap((page) => {
        return from(page.getTextContent());
      }),
      map((textContent) => {
        const result = {
          pageNumber,
          items: textContent.items,
          styles: textContent.styles
        };
        this.textContentCache.set(pageNumber, result);
        return result;
      }),
      catchError((error) => {
        console.error(`[PdfService] Error getting text content for page ${pageNumber}:`, error);
        this.errorSubject.next(`Failed to extract text from page ${pageNumber}: ${error.message}`);
        return of({ pageNumber, items: [], styles: {} });
      })
    );
  }
  /**
   * Searches for text across all pages of the PDF
   * @param searchText Text to search for
   * @param options Search options (case sensitivity, etc.)
   * @returns Observable of search results
   */
  searchText(searchText, options = {}) {
    if (!this.isBrowser || !searchText.trim()) {
      return of([]);
    }
    return from(this.ensurePdfJsLibLoaded()).pipe(switchMap(() => {
      if (!this.pdfjsLib) {
        console.error("[PdfService] searchText: PDF.js library not loaded.");
        throw new Error("PDF.js library not loaded for searchText");
      }
      if (!this.pdfDocument) {
        console.error("[PdfService] searchText: PDF document not loaded.");
        return of([]);
      }
      const results = [];
      const searchTextRegex = this.createSearchRegex(searchText, options.caseSensitive, options.wholeWord);
      const pagePromises = [];
      for (let i = 1; i <= this.pdfDocument.numPages; i++) {
        const pageNum = i;
        const pagePromise = this.searchInPage(pageNum, searchTextRegex).then((pageResults) => {
          results.push(...pageResults);
        }).catch((error) => {
          console.error(`[PdfService] Error searching in page ${pageNum}:`, error);
        });
        pagePromises.push(pagePromise);
      }
      return from(Promise.all(pagePromises).then(() => results));
    }), catchError((error) => {
      console.error("[PdfService] Error in searchText observable chain:", error);
      this.errorSubject.next(`Failed to search text: ${error.message}`);
      return of([]);
    }));
  }
  /**
   * Creates a regular expression for searching
   * @param searchText The text to search for
   * @param caseSensitive Whether the search is case-sensitive
   * @param wholeWord Whether to match whole words only
   * @returns A RegExp object
   */
  createSearchRegex(searchText, caseSensitive, wholeWord) {
    let regexPattern = searchText.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
    if (wholeWord) {
      regexPattern = `\\b${regexPattern}\\b`;
    }
    return new RegExp(regexPattern, caseSensitive ? "g" : "gi");
  }
  /**
   * Searches for text in a specific page
   * @param pageNumber The page number (1-based)
   * @param searchRegex The regular expression to search with
   * @returns A Promise of search results
   */
  searchInPage(pageNumber, searchRegex) {
    return __async(this, null, function* () {
      if (!this.pdfjsLib || !this.pdfDocument) {
        console.error("[PdfService] searchInPage: PDF.js library or document not loaded.");
        throw new Error("PDF.js library or document not loaded for searchInPage");
      }
      const pageResults = [];
      try {
        let textContent;
        if (this.textContentCache.has(pageNumber)) {
          textContent = this.textContentCache.get(pageNumber);
        } else {
          const pageForText = yield this.pdfDocument.getPage(pageNumber);
          const content = yield pageForText.getTextContent();
          textContent = {
            pageNumber,
            items: content.items,
            styles: content.styles
          };
          this.textContentCache.set(pageNumber, textContent);
        }
        const pageForViewport = yield this.pdfDocument.getPage(pageNumber);
        const viewport = pageForViewport.getViewport({ scale: 1 });
        const lines = [];
        let currentLine = [];
        let lastY = null;
        for (const item of textContent.items) {
          if (item.str.trim() === "")
            continue;
          if (lastY === null || Math.abs(item.transform[5] - lastY) < 2) {
            currentLine.push(item);
          } else {
            if (currentLine.length > 0) {
              lines.push({
                text: currentLine.map((i) => i.str).join(""),
                items: [...currentLine]
              });
            }
            currentLine = [item];
          }
          lastY = item.transform[5];
        }
        if (currentLine.length > 0) {
          lines.push({
            text: currentLine.map((i) => i.str).join(""),
            items: [...currentLine]
          });
        }
        let matchIndex = 0;
        for (const line of lines) {
          let match;
          searchRegex.lastIndex = 0;
          while ((match = searchRegex.exec(line.text)) !== null) {
            const startIndex = match.index;
            const endIndex = startIndex + match[0].length;
            let currentPos = 0;
            let startItem = null;
            let endItem = null;
            for (const item of line.items) {
              const itemStart = currentPos;
              const itemEnd = currentPos + item.str.length;
              if (startItem === null && startIndex < itemEnd) {
                startItem = item;
              }
              if (endItem === null && endIndex <= itemEnd) {
                endItem = item;
              }
              if (startItem !== null && endItem !== null) {
                break;
              }
              currentPos = itemEnd;
            }
            if (startItem && endItem) {
              const position = {
                left: startItem.transform[4],
                top: viewport.height - startItem.transform[5] - startItem.height,
                right: endItem.transform[4] + endItem.width,
                bottom: viewport.height - endItem.transform[5]
              };
              pageResults.push({
                pageNumber,
                matchIndex: matchIndex++,
                text: match[0],
                position
              });
            }
          }
        }
        return pageResults;
      } catch (error) {
        console.error(`[PdfService] Error in searchInPage for page ${pageNumber}:`, error);
        return [];
      }
    });
  }
  /**
   * Adds a highlight to the PDF
   * @param pageNumber The page number (1-based)
   * @param position The position of the highlight
   * @param text The text being highlighted
   * @param color The color of the highlight
   * @returns The ID of the highlight
   */
  addHighlight(pageNumber, position, text, color = "rgba(255, 255, 0, 0.3)") {
    const id = `highlight-${Date.now()}-${Math.floor(Math.random() * 1e3)}`;
    const highlight = {
      id,
      pageNumber,
      position,
      color,
      text
    };
    const currentHighlights = this.highlightsSubject.value;
    this.highlightsSubject.next([...currentHighlights, highlight]);
    return id;
  }
  /**
   * Removes a highlight from the PDF
   * @param id The ID of the highlight to remove
   */
  removeHighlight(id) {
    const currentHighlights = this.highlightsSubject.value;
    this.highlightsSubject.next(currentHighlights.filter((h) => h.id !== id));
  }
  /**
   * Gets all highlights for a specific page
   * @param pageNumber The page number (1-based)
   * @returns An Observable of highlights for the page
   */
  getPageHighlights(pageNumber) {
    return this.highlights$.pipe(map((highlights) => highlights.filter((h) => h.pageNumber === pageNumber)));
  }
  /**
   * Clears all highlights
   */
  clearHighlights() {
    this.highlightsSubject.next([]);
  }
  /**
   * Closes the current PDF document
   */
  closePdf() {
    if (this.pdfDocument) {
      this.pdfDocument.destroy();
      this.pdfDocument = null;
    }
    this.pdfDocumentSubject.next(null);
    this.pdfInfoSubject.next(null);
    this.highlightsSubject.next([]);
    this.pdfBase64DataUrlSubject.next(null);
    this.clearCache();
    this.loadingSubject.next(false);
    console.log("[PdfService] PDF closed and resources released.");
  }
  /**
   * Converts an ArrayBuffer to a base64 data URL string.
   * @param buffer The ArrayBuffer to convert.
   * @returns A string representing the base64 data URL.
   */
  arrayBufferToDataUrl(buffer) {
    let binary = "";
    const bytes = new Uint8Array(buffer);
    const len = bytes.byteLength;
    for (let i = 0; i < len; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    const base64 = window.btoa(binary);
    return `data:application/pdf;base64,${base64}`;
  }
  static \u0275fac = function PdfService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _PdfService)(\u0275\u0275inject(HttpClient), \u0275\u0275inject(PLATFORM_ID));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _PdfService, factory: _PdfService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(PdfService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: HttpClient }, { type: Object, decorators: [{
    type: Inject,
    args: [PLATFORM_ID]
  }] }], null);
})();

// src/app/features/chart-review/components/pdf-viewer/pdf-viewer.component.ts
function PdfViewerComponent_ngx_extended_pdf_viewer_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "ngx-extended-pdf-viewer", 5);
    \u0275\u0275twoWayListener("pageChange", function PdfViewerComponent_ngx_extended_pdf_viewer_2_Template_ngx_extended_pdf_viewer_pageChange_0_listener($event) {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      \u0275\u0275twoWayBindingSet(ctx_r1.currentPage, $event) || (ctx_r1.currentPage = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275listener("zoomChange", function PdfViewerComponent_ngx_extended_pdf_viewer_2_Template_ngx_extended_pdf_viewer_zoomChange_0_listener($event) {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.onZoomChange($event));
    })("pdfLoaded", function PdfViewerComponent_ngx_extended_pdf_viewer_2_Template_ngx_extended_pdf_viewer_pdfLoaded_0_listener($event) {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.onPdfLoaded($event));
    })("pageChange", function PdfViewerComponent_ngx_extended_pdf_viewer_2_Template_ngx_extended_pdf_viewer_pageChange_0_listener($event) {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.onPageChange($event));
    })("error", function PdfViewerComponent_ngx_extended_pdf_viewer_2_Template_ngx_extended_pdf_viewer_error_0_listener($event) {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.onPdfViewerError($event));
    });
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275property("src", ctx_r1.pdfBase64Source);
    \u0275\u0275twoWayProperty("page", ctx_r1.currentPage);
    \u0275\u0275property("zoom", ctx_r1.zoom)("autoScale", "page-fit")("defaultZoom", "page-fit")("height", ctx_r1.viewerHeight)("language", "en-US")("showToolbar", true)("showSidebarButton", true)("showFindButton", true)("showPagingButtons", true)("showZoomButtons", true)("showPresentationModeButton", true)("showOpenFileButton", true)("showPrintButton", true)("showDownloadButton", true)("showBookmodeButton", "always-in-secondary-menu")("showSecondaryToolbarButton", true)("showRotateButton", true)("showHandToolButton", true)("showScrollingButtons", "always-in-secondary-menu")("showSpreadButton", true)("showPropertiesButton", true)("scrollMode", ctx_r1.scrollMode)("disableForms", !ctx_r1.renderInteractiveForms)("minZoom", ctx_r1.minZoom)("maxZoom", ctx_r1.maxZoom);
  }
}
function PdfViewerComponent_div_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 6)(1, "p");
    \u0275\u0275text(2, "Please select a PDF file to view.");
    \u0275\u0275elementEnd()();
  }
}
function PdfViewerComponent_div_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 7);
    \u0275\u0275text(1, " PDF Viewer is not available during server-side rendering. ");
    \u0275\u0275elementEnd();
  }
}
var PdfViewerComponent = class _PdfViewerComponent {
  extPdfViewerService;
  pdfServiceInstance;
  platformId;
  // PDF viewer state
  currentPage = 1;
  totalPages = 0;
  zoom = "page-fit";
  // Set to page-fit for default zoom
  // Search state
  searchText = "";
  caseSensitive = false;
  wholeWord = false;
  // searchResults: PdfSearchResult[] = []; // Commented out as search functionality is not fully implemented here
  // currentSearchResult: number = -1; // Commented out
  isSearching = false;
  // File state
  currentFileName = "";
  subscriptions = new Subscription();
  isBrowser = false;
  // Initialize with a default value
  pdfBase64Source = null;
  // Store the base64 source
  // Configuration options
  scrollMode = 0;
  // 0 = vertical (default), 1 = horizontal, 2 = wrapped
  minZoom = 0.5;
  // Already in decimal format (50%)
  maxZoom = 2;
  // Already in decimal format (200%)
  renderTextMode = 2;
  // 0 = disabled, 1 = enabled, 2 = enhanced
  renderInteractiveForms = true;
  viewerPositionTop = 0;
  viewerHeight = "100%";
  constructor(extPdfViewerService, pdfServiceInstance, platformId) {
    this.extPdfViewerService = extPdfViewerService;
    this.pdfServiceInstance = pdfServiceInstance;
    this.platformId = platformId;
    this.isBrowser = isPlatformBrowser(this.platformId);
    if (this.isBrowser) {
      pdfDefaultOptions.disableStream = false;
      pdfDefaultOptions.disableAutoFetch = false;
      pdfDefaultOptions.defaultZoomValue = "page-fit";
      const annotationParams = {
        // Text editor color (for adding text annotations)
        defaultTextColor: "#FF0000",
        // Red text
        // Highlight color
        defaultHighlightColor: "#FFFF00",
        // Yellow highlight
        // Drawing/pen color
        defaultDrawColor: "#0000FF",
        // Blue pen
        // Ink thickness for drawing
        defaultInkThickness: 3,
        // Ink opacity (0-1)
        defaultInkOpacity: 0.8
      };
      pdfDefaultOptions.annotationEditorParams = annotationParams;
      console.log("[PdfViewer] Configured annotation colors:", annotationParams);
      console.log("[PdfViewer] Set default zoom to page-fit");
    }
  }
  ngOnInit() {
    console.log("[PdfViewer] ngOnInit called");
    console.log("[PdfViewer] Browser environment:", this.isBrowser ? "Browser" : "Server");
    console.log("[PdfViewer] Initial zoom value:", this.zoom, "(decimal format)");
    console.log("[PdfViewer] Scroll mode:", this.getScrollModeName(this.scrollMode));
    console.log("[PdfViewer] Forms enabled:", this.renderInteractiveForms);
    if (this.isBrowser) {
      console.log("[PdfViewer] PDF worker configuration:", window.pdfWorkerSrc);
      this.subscriptions.add(this.pdfServiceInstance.pdfBase64DataUrl$.subscribe((dataUrl) => {
        if (dataUrl) {
          console.log("[PdfViewer] Received base64 data URL from PdfService.");
          this.pdfBase64Source = dataUrl;
          if (!this.currentFileName && dataUrl.startsWith("data:application/pdf;base64,")) {
            this.currentFileName = "loaded_from_service.pdf";
          }
        } else {
          console.log("[PdfViewer] Received null base64 data URL from PdfService.");
        }
      }));
    }
    console.log("[PdfViewer] Using ngx-extended-pdf-viewer version 23.1.1");
  }
  ngAfterViewInit() {
    console.log("[PdfViewer] ngAfterViewInit called");
    console.log("[PdfViewer] PDF viewer ready for rendering");
    setTimeout(() => {
      console.log("[PdfViewer] Delayed check - Zoom value:", this.zoom, "(decimal format)");
      console.log("[PdfViewer] Viewer container dimensions:", this.getViewerContainerDimensions());
      this.setDefaultAnnotationColors();
    }, 1e3);
  }
  /**
   * Sets the default colors for annotation tools by directly manipulating the color inputs
   */
  setDefaultAnnotationColors() {
    if (!this.isBrowser)
      return;
    try {
      const textColorInput = document.getElementById("editorFreeTextColor");
      if (textColorInput) {
        textColorInput.value = "#FF0000";
        console.log("[PdfViewer] Set text color input to:", textColorInput.value);
        textColorInput.dispatchEvent(new Event("input", { bubbles: true }));
      } else {
        console.warn("[PdfViewer] Text color input element not found");
      }
      const drawColorInput = document.getElementById("editorInkColor");
      if (drawColorInput) {
        drawColorInput.value = "#0000FF";
        console.log("[PdfViewer] Set draw color input to:", drawColorInput.value);
        drawColorInput.dispatchEvent(new Event("input", { bubbles: true }));
      } else {
        console.warn("[PdfViewer] Draw color input element not found");
      }
      const highlightColorInput = document.getElementById("editorHighlightColor");
      if (highlightColorInput) {
        highlightColorInput.value = "#FFFF00";
        console.log("[PdfViewer] Set highlight color input to:", highlightColorInput.value);
        highlightColorInput.dispatchEvent(new Event("input", { bubbles: true }));
      } else {
        console.warn("[PdfViewer] Highlight color input element not found");
      }
    } catch (error) {
      console.error("[PdfViewer] Error setting default annotation colors:", error);
    }
  }
  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }
  /**
   * Handles file selection from the file input
   * @param event The file input change event
   */
  onFileSelected(event) {
    if (!this.isBrowser) {
      console.error("[PdfViewer] PDF loading attempted during server-side rendering");
      return;
    }
    console.log("[PdfViewer] File selection event triggered");
    const input = event.target;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];
      console.log(`[PdfViewer] File selected: ${file.name}, type: ${file.type}, size: ${file.size} bytes`);
      if (file.type === "application/pdf") {
        this.currentFileName = file.name;
        this.pdfBase64Source = null;
        console.log("[PdfViewer] Valid PDF file selected, beginning conversion to base64");
        const reader = new FileReader();
        reader.onloadstart = () => {
          console.log("[PdfViewer] FileReader: Started loading file");
        };
        reader.onprogress = (event2) => {
          if (event2.lengthComputable) {
            const percentLoaded = Math.round(event2.loaded / event2.total * 100);
            console.log(`[PdfViewer] FileReader: Loading progress: ${percentLoaded}%`);
          }
        };
        reader.onload = (e) => {
          console.log("[PdfViewer] FileReader: File successfully loaded");
          const rawResult = e.target?.result;
          this.pdfBase64Source = rawResult;
          const base64Preview = this.pdfBase64Source?.substring(0, 100) + "...";
          console.log(`[PdfViewer] Assigned pdfBase64Source length: ${this.pdfBase64Source?.length ?? "null"}`);
          console.log(`[PdfViewer] Assigned pdfBase64Source (preview): ${base64Preview}`);
          if (this.pdfBase64Source?.startsWith("data:application/pdf;base64,")) {
            console.log("[PdfViewer] Base64 data has correct PDF MIME type prefix");
          } else {
            console.warn("[PdfViewer] Base64 data does not have expected PDF MIME type prefix");
          }
          console.log("[PdfViewer] PDF base64 source set, viewer should update");
        };
        reader.onerror = (error) => {
          console.error("[PdfViewer] FileReader error:", error);
          this.pdfBase64Source = null;
          this.currentFileName = "";
          this.totalPages = 0;
          alert("Error reading the selected PDF file.");
        };
        console.log("[PdfViewer] Starting FileReader.readAsDataURL...");
        reader.readAsDataURL(file);
      } else {
        this.pdfBase64Source = null;
        this.currentFileName = "";
        this.totalPages = 0;
        console.error(`[PdfViewer] Invalid file type: ${file.type}, expected application/pdf`);
        alert("Please select a valid PDF file.");
      }
    } else {
      console.warn("[PdfViewer] No file selected or file input event without files");
      this.pdfBase64Source = null;
      this.currentFileName = "";
      this.totalPages = 0;
    }
  }
  /**
   * Loads a PDF from a File object
   * @param file The PDF file to load
   */
  /**
   * Handles PDF loaded event
   * @param pdf The loaded PDF document
   */
  onPdfLoaded(pdf) {
    console.log("[PdfViewer] PDF loaded event triggered");
    console.log("[PdfViewer] PDF document:", pdf);
    if (pdf && pdf.numPages) {
      this.totalPages = pdf.numPages;
      console.log(`[PdfViewer] PDF loaded with ${this.totalPages} pages`);
    }
    setTimeout(() => {
      console.log("[PdfViewer] Viewer dimensions after PDF load:", this.getViewerContainerDimensions());
      console.log("[PdfViewer] Current zoom after PDF load:", this.zoom, "(decimal format)");
    }, 500);
  }
  /**
   * Handles page change events
   * @param pageNumber The new page number
   */
  onPageChange(pageNumber) {
    console.log(`[PdfViewer] Page changed to ${pageNumber}`);
    this.currentPage = pageNumber;
  }
  /**
   * Handles zoom change events from the PDF viewer
   * @param newZoom New zoom level (as a decimal, e.g., 1.0 for 100%)
   */
  onZoomChange(newZoom) {
    const zoomValue = typeof newZoom === "string" ? parseFloat(newZoom) : newZoom;
    console.log(`[PdfViewer] PDF viewer zoom changed to ${zoomValue}`);
    this.zoom = zoomValue;
  }
  /**
   * Performs a search across the PDF
   */
  /**
   * Handles PDF viewer errors
   * @param error The error object from the PDF viewer
   */
  /**
   * Handles PDF viewer errors
   * @param error The error object from the PDF viewer
   */
  onPdfViewerError(error) {
    console.error("[PdfViewer] PDF Viewer Error:", error);
    console.error("[PdfViewer] PDF Viewer Context:", {
      hasBase64Source: !!this.pdfBase64Source,
      base64SourceLength: this.pdfBase64Source ? this.pdfBase64Source.length : 0,
      base64SourcePrefix: this.pdfBase64Source ? this.pdfBase64Source.substring(0, 50) : "N/A",
      fileName: this.currentFileName,
      totalPages: this.totalPages,
      workerSrc: this.isBrowser ? window.pdfWorkerSrc : "N/A",
      viewerDimensions: this.getViewerContainerDimensions(),
      scrollMode: this.scrollMode,
      scrollModeName: this.getScrollModeName(this.scrollMode),
      renderInteractiveForms: this.renderInteractiveForms,
      ngxExtendedPdfViewerVersion: "23.1.1"
    });
  }
  /**
   * Gets the name of the scroll mode
   * @param mode The scroll mode number
   * @returns The name of the scroll mode
   */
  getScrollModeName(mode) {
    switch (mode) {
      case 0:
        return "Vertical";
      case 1:
        return "Horizontal";
      case 2:
        return "Wrapped";
      default:
        return "Unknown";
    }
  }
  /**
   * Gets the dimensions of the viewer container
   * @returns An object with the width and height of the viewer container
   */
  getViewerContainerDimensions() {
    if (!this.isBrowser) {
      return { width: "N/A", height: "N/A" };
    }
    const container = document.querySelector(".pdf-viewer-wrapper");
    if (!container) {
      return { width: "Container not found", height: "Container not found" };
    }
    return {
      width: `${container.clientWidth}px`,
      height: `${container.clientHeight}px`
    };
  }
  static \u0275fac = function PdfViewerComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _PdfViewerComponent)(\u0275\u0275directiveInject(NgxExtendedPdfViewerService), \u0275\u0275directiveInject(PdfService), \u0275\u0275directiveInject(PLATFORM_ID));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _PdfViewerComponent, selectors: [["app-pdf-viewer"]], decls: 5, vars: 3, consts: [[1, "pdf-viewer-container"], [1, "pdf-viewer-wrapper"], [3, "src", "page", "zoom", "autoScale", "defaultZoom", "height", "language", "showToolbar", "showSidebarButton", "showFindButton", "showPagingButtons", "showZoomButtons", "showPresentationModeButton", "showOpenFileButton", "showPrintButton", "showDownloadButton", "showBookmodeButton", "showSecondaryToolbarButton", "showRotateButton", "showHandToolButton", "showScrollingButtons", "showSpreadButton", "showPropertiesButton", "scrollMode", "disableForms", "minZoom", "maxZoom", "pageChange", "zoomChange", "pdfLoaded", "error", 4, "ngIf"], ["class", "no-pdf-message", 4, "ngIf"], ["class", "ssr-placeholder", 4, "ngIf"], [3, "pageChange", "zoomChange", "pdfLoaded", "error", "src", "page", "zoom", "autoScale", "defaultZoom", "height", "language", "showToolbar", "showSidebarButton", "showFindButton", "showPagingButtons", "showZoomButtons", "showPresentationModeButton", "showOpenFileButton", "showPrintButton", "showDownloadButton", "showBookmodeButton", "showSecondaryToolbarButton", "showRotateButton", "showHandToolButton", "showScrollingButtons", "showSpreadButton", "showPropertiesButton", "scrollMode", "disableForms", "minZoom", "maxZoom"], [1, "no-pdf-message"], [1, "ssr-placeholder"]], template: function PdfViewerComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1);
      \u0275\u0275template(2, PdfViewerComponent_ngx_extended_pdf_viewer_2_Template, 1, 27, "ngx-extended-pdf-viewer", 2)(3, PdfViewerComponent_div_3_Template, 3, 0, "div", 3)(4, PdfViewerComponent_div_4_Template, 2, 0, "div", 4);
      \u0275\u0275elementEnd()();
    }
    if (rf & 2) {
      \u0275\u0275advance(2);
      \u0275\u0275property("ngIf", ctx.isBrowser && ctx.pdfBase64Source);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.isBrowser && !ctx.pdfBase64Source);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.isBrowser);
    }
  }, dependencies: [CommonModule, NgIf, NgxExtendedPdfViewerModule, NgxExtendedPdfViewerComponent, FormsModule], styles: ["\n\n.pdf-viewer-container[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  width: 100%;\n}\n.test-controls[_ngcontent-%COMP%] {\n  background-color: #ffffff;\n  padding: 16px;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  margin-bottom: 20px;\n}\nh2[_ngcontent-%COMP%] {\n  margin-top: 0;\n  margin-bottom: 16px;\n  font-size: 20px;\n  color: #333;\n}\n.file-input-container[_ngcontent-%COMP%] {\n  margin-bottom: 16px;\n}\n.file-input-label[_ngcontent-%COMP%] {\n  display: inline-block;\n  padding: 8px 16px;\n  background-color: #2196f3;\n  color: white;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 14px;\n  transition: background-color 0.2s;\n}\n.file-input-label[_ngcontent-%COMP%]:hover {\n  background-color: #1976d2;\n}\n.file-input[_ngcontent-%COMP%] {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  border: 0;\n}\n.config-controls[_ngcontent-%COMP%] {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16px;\n  margin-bottom: 16px;\n}\n.control-group[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n.control-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\n  font-size: 14px;\n  color: #333;\n}\n.control-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%], \n.control-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\n  padding: 4px 8px;\n  border: 1px solid #ccc;\n  border-radius: 4px;\n  background-color: #fff;\n  color: #17181A;\n  font-size: 14px;\n  cursor: pointer;\n}\n.control-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover, \n.control-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]:hover {\n  background-color: #f0f0f0;\n}\n.debug-info[_ngcontent-%COMP%] {\n  background-color: #f0f0f0;\n  padding: 12px;\n  border-radius: 4px;\n  font-size: 12px;\n  font-family: monospace;\n}\n.debug-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin-top: 0;\n  margin-bottom: 8px;\n  font-size: 14px;\n}\n.debug-info[_ngcontent-%COMP%]   div[_ngcontent-%COMP%] {\n  margin-bottom: 4px;\n}\n.pdf-viewer-wrapper[_ngcontent-%COMP%] {\n  flex: 1;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  min-height: 500px;\n}\n[_nghost-%COMP%]     ngx-extended-pdf-viewer {\n  display: block;\n  width: 100%;\n  height: 100%;\n}\n[_nghost-%COMP%]     .viewer-container {\n  overflow: auto !important;\n}\n[_nghost-%COMP%]     .page {\n  margin: 8px auto !important;\n}\n[_nghost-%COMP%]     .textLayer .highlight {\n  background-color: rgba(180, 235, 180, 0.4) !important;\n  border-radius: 2px;\n  box-shadow: 0 0 1px rgba(0, 0, 0, 0.1);\n  mix-blend-mode: multiply;\n}\n[_nghost-%COMP%]     .textLayer .highlight.selected {\n  background-color: rgba(180, 235, 180, 0.6) !important;\n}\n.no-pdf-message[_ngcontent-%COMP%], \n.ssr-placeholder[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  font-size: 16px;\n  color: #666;\n  text-align: center;\n  padding: 20px;\n}\n@media (max-width: 768px) {\n  .config-controls[_ngcontent-%COMP%] {\n    flex-direction: column;\n    align-items: flex-start;\n  }\n  .pdf-viewer-wrapper[_ngcontent-%COMP%] {\n    height: calc(100vh - 400px);\n  }\n}\n/*# sourceMappingURL=pdf-viewer.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(PdfViewerComponent, [{
    type: Component,
    args: [{
      selector: "app-pdf-viewer",
      standalone: true,
      imports: [CommonModule, NgxExtendedPdfViewerModule, FormsModule],
      schemas: [NO_ERRORS_SCHEMA],
      template: `<div class="pdf-viewer-container">\r
  <!-- PDF Viewer -->\r
  <div class="pdf-viewer-wrapper">\r
    <ngx-extended-pdf-viewer *ngIf="isBrowser && pdfBase64Source"\r
      [src]="pdfBase64Source"\r
      [(page)]="currentPage"\r
      [zoom]="zoom"\r
      [autoScale]="'page-fit'"\r
      [defaultZoom]="'page-fit'"\r
      (zoomChange)="onZoomChange($event)"\r
      (pdfLoaded)="onPdfLoaded($event)"\r
      (pageChange)="onPageChange($event)"\r
      [height]="viewerHeight"\r
      [language]="'en-US'"\r
      [showToolbar]="true"\r
      [showSidebarButton]="true"\r
      [showFindButton]="true"\r
      [showPagingButtons]="true"\r
      [showZoomButtons]="true"\r
      [showPresentationModeButton]="true"\r
      [showOpenFileButton]="true"\r
      [showPrintButton]="true"\r
      [showDownloadButton]="true"\r
      [showBookmodeButton]="'always-in-secondary-menu'"\r
      [showSecondaryToolbarButton]="true"\r
      [showRotateButton]="true"\r
      [showHandToolButton]="true"\r
      [showScrollingButtons]="'always-in-secondary-menu'"\r
      [showSpreadButton]="true"\r
      [showPropertiesButton]="true"\r
      [scrollMode]="scrollMode"\r
      [disableForms]="!renderInteractiveForms"\r
      [minZoom]="minZoom"\r
      [maxZoom]="maxZoom"\r
      (error)="onPdfViewerError($event)"\r
    ></ngx-extended-pdf-viewer>\r
\r
    <!-- Message when no PDF is loaded -->\r
    <div *ngIf="isBrowser && !pdfBase64Source" class="no-pdf-message">\r
      <p>Please select a PDF file to view.</p>\r
    </div>\r
\r
    <!-- Message when not in browser -->\r
    <div *ngIf="!isBrowser" class="ssr-placeholder">\r
      PDF Viewer is not available during server-side rendering.\r
    </div>\r
  </div>\r
</div>\r
`,
      styles: ["/* src/app/features/chart-review/components/pdf-viewer/pdf-viewer.component.scss */\n.pdf-viewer-container {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  width: 100%;\n}\n.test-controls {\n  background-color: #ffffff;\n  padding: 16px;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  margin-bottom: 20px;\n}\nh2 {\n  margin-top: 0;\n  margin-bottom: 16px;\n  font-size: 20px;\n  color: #333;\n}\n.file-input-container {\n  margin-bottom: 16px;\n}\n.file-input-label {\n  display: inline-block;\n  padding: 8px 16px;\n  background-color: #2196f3;\n  color: white;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 14px;\n  transition: background-color 0.2s;\n}\n.file-input-label:hover {\n  background-color: #1976d2;\n}\n.file-input {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  border: 0;\n}\n.config-controls {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16px;\n  margin-bottom: 16px;\n}\n.control-group {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n.control-group label {\n  font-size: 14px;\n  color: #333;\n}\n.control-group button,\n.control-group select {\n  padding: 4px 8px;\n  border: 1px solid #ccc;\n  border-radius: 4px;\n  background-color: #fff;\n  color: #17181A;\n  font-size: 14px;\n  cursor: pointer;\n}\n.control-group button:hover,\n.control-group select:hover {\n  background-color: #f0f0f0;\n}\n.debug-info {\n  background-color: #f0f0f0;\n  padding: 12px;\n  border-radius: 4px;\n  font-size: 12px;\n  font-family: monospace;\n}\n.debug-info h3 {\n  margin-top: 0;\n  margin-bottom: 8px;\n  font-size: 14px;\n}\n.debug-info div {\n  margin-bottom: 4px;\n}\n.pdf-viewer-wrapper {\n  flex: 1;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  min-height: 500px;\n}\n:host ::ng-deep ngx-extended-pdf-viewer {\n  display: block;\n  width: 100%;\n  height: 100%;\n}\n:host ::ng-deep .viewer-container {\n  overflow: auto !important;\n}\n:host ::ng-deep .page {\n  margin: 8px auto !important;\n}\n:host ::ng-deep .textLayer .highlight {\n  background-color: rgba(180, 235, 180, 0.4) !important;\n  border-radius: 2px;\n  box-shadow: 0 0 1px rgba(0, 0, 0, 0.1);\n  mix-blend-mode: multiply;\n}\n:host ::ng-deep .textLayer .highlight.selected {\n  background-color: rgba(180, 235, 180, 0.6) !important;\n}\n.no-pdf-message,\n.ssr-placeholder {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  font-size: 16px;\n  color: #666;\n  text-align: center;\n  padding: 20px;\n}\n@media (max-width: 768px) {\n  .config-controls {\n    flex-direction: column;\n    align-items: flex-start;\n  }\n  .pdf-viewer-wrapper {\n    height: calc(100vh - 400px);\n  }\n}\n/*# sourceMappingURL=pdf-viewer.component.css.map */\n"]
    }]
  }], () => [{ type: NgxExtendedPdfViewerService }, { type: PdfService }, { type: Object, decorators: [{
    type: Inject,
    args: [PLATFORM_ID]
  }] }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(PdfViewerComponent, { className: "PdfViewerComponent", filePath: "src/app/features/chart-review/components/pdf-viewer/pdf-viewer.component.ts", lineNumber: 16 });
})();

// src/app/features/chart-review/pages/chart-review-page/chart-review-page.component.ts
var ChartReviewPageComponent = class _ChartReviewPageComponent {
  pdfViewerComponent;
  chartId = "";
  selectedFinding = null;
  // Demographics data for the patient info header (will be updated with route data)
  demographicsData = {
    measureTitle: "Controlling Blood Pressure (CBP)",
    measureSubtitle: "Measure",
    memberId: "",
    memberName: "",
    dateOfBirth: "",
    gender: "",
    lob: "",
    providerName: "",
    npi: ""
  };
  // Navigation data
  userProfile = {
    name: "Jane Chu",
    avatar: ""
  };
  menuItems = [
    { label: "Dashboard", route: "/dashboard", icon: "\u{1F3E0}" },
    { label: "Profile", route: "/profile", icon: "\u{1F464}" },
    { label: "Settings", route: "/settings", icon: "\u2699\uFE0F" },
    { label: "Help", route: "/help", icon: "\u2753" },
    { label: "Logout", action: () => this.logout(), icon: "\u{1F6AA}" }
  ];
  // Hits data for the hits component
  hitsData = [
    {
      id: "hit-1",
      dateOfService: "07/22/24",
      systolic: 136,
      diastolic: 82,
      page: 2,
      comment: "",
      include: false
    },
    {
      id: "hit-2",
      dateOfService: "07/22/24",
      systolic: 140,
      diastolic: 82,
      page: 2,
      comment: "",
      include: false
    },
    {
      id: "hit-3",
      dateOfService: "05/22/24",
      systolic: 150,
      diastolic: 90,
      page: 7,
      comment: "",
      include: false
    }
  ];
  // Results data for the results component
  resultsData = {
    category: "inclusions",
    telehealth: false,
    sys: "",
    dias: "",
    dateOfService: "",
    notes: ""
  };
  // zoom property removed as it's no longer needed
  // TODO: Determine how selectedFinding should be set now that the sidebar is removed.
  // It might come from interactions with the PDF viewer or another source.
  route = inject(ActivatedRoute);
  router = inject(Router);
  pdfService = inject(PdfService);
  // Inject PdfService
  http = inject(HttpClient);
  // Inject HttpClient for loading default PDF
  platformId = inject(PLATFORM_ID);
  // Inject platform ID for browser detection
  // Remove the constructor if only used for DI before
  ngOnInit() {
    this.chartId = this.route.snapshot.paramMap.get("id") || "";
    if (this.chartId) {
      console.log(`[ChartReviewPage] Attempting to load chart with ID: ${this.chartId}`);
      const pdfUrl = `assets/charts/${this.chartId}.pdf`;
      this.pdfService.loadPdf(pdfUrl).subscribe({
        next: (pdfDoc) => {
          console.log(`[ChartReviewPage] PDF loaded successfully from asset path: ${pdfUrl}, Pages: ${pdfDoc.numPages}`);
        },
        error: (err) => {
          console.error(`[ChartReviewPage] Error loading PDF from asset path (${pdfUrl}):`, err);
        }
      });
    } else {
      console.warn("[ChartReviewPage] No chart ID found in route parameters. Cannot load PDF.");
    }
    this.updateDemographicsData();
    console.log("[ChartReviewPage] Demographics data initialized:", this.demographicsData);
    this.loadPdfForMember();
  }
  /**
   * Handles the file selection event from an input element.
   * This method is for manual file uploads by the user, not for loading charts by ID.
   * Commenting out for now as the primary task is to load by chartId.
   */
  /*
  onFileSelected(event: Event): void {
    console.log('[ChartReviewPage] onFileSelected method called.');
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];
      console.log(`[ChartReviewPage] File selected: ${file.name}`);
      // loadPdfFromFile is used when a user explicitly selects a file using a file input.
      this.pdfService.loadPdfFromFile(file).subscribe({
        next: (pdfDoc) => {
          console.log(`[ChartReviewPage] PDF loaded successfully by service (from file input): ${pdfDoc.numPages} pages`);
        },
        error: (err) => {
          console.error('[ChartReviewPage] Error loading PDF via service (from file input):', err);
        }
      });
    } else {
      console.log('[ChartReviewPage] No file selected via file input.');
    }
  }
  */
  // Zoom methods removed as requested
  /**
   * Navigates the PDF viewer to a specific page.
   * @param pageNumber The page number to navigate to.
   */
  goToPdfPage(pageNumber) {
    if (this.pdfViewerComponent) {
      console.log(`[ChartReviewPage] Navigating to PDF page: ${pageNumber}`);
      this.pdfViewerComponent.currentPage = pageNumber;
    } else {
      console.warn("[ChartReviewPage] goToPdfPage called, but pdfViewerComponent is not available.");
    }
  }
  // Navigation event handlers
  onLogoClick() {
    console.log("Logo clicked");
    this.router.navigate(["/dashboard"]);
  }
  onUserClick() {
    console.log("User clicked");
  }
  onDropdownToggle(isOpen) {
    console.log("Dropdown toggled:", isOpen);
  }
  onMenuItemClick(item) {
    console.log("Menu item clicked:", item);
    if (item.route) {
      this.router.navigate([item.route]);
    } else if (item.action) {
      item.action();
    }
  }
  logout() {
    console.log("Logout clicked");
    this.router.navigate(["/"]);
  }
  // Demographics component event handler
  onDemographicsBackClick() {
    console.log("Demographics back button clicked");
    this.router.navigate(["/dashboard"]);
  }
  // Update demographics data based on member ID
  updateDemographicsData() {
    const memberId = this.chartId;
    console.log(`[ChartReviewPage] Updating demographics for member ID: ${memberId}`);
    this.demographicsData.memberId = memberId || "Unknown";
    if (memberId === "55820474") {
      this.demographicsData.memberName = "John Dey";
      this.demographicsData.dateOfBirth = "01/05/1972";
      this.demographicsData.gender = "M";
      this.demographicsData.lob = "PPO";
      this.demographicsData.providerName = "Nicolas Dejong PA";
      this.demographicsData.npi = "882716229";
    } else {
      this.demographicsData.memberName = "Sample Patient";
      this.demographicsData.dateOfBirth = "01/01/1970";
      this.demographicsData.gender = "U";
      this.demographicsData.lob = "HMO";
      this.demographicsData.providerName = "Dr. Sample Provider";
      this.demographicsData.npi = "123456789";
    }
    console.log("[ChartReviewPage] Demographics updated:", this.demographicsData);
  }
  // Load PDF based on member ID from route parameters
  loadPdfForMember() {
    const memberId = this.chartId;
    console.log(`[ChartReviewPage] Loading PDF for member ID: ${memberId}`);
    if (!memberId) {
      console.warn("[ChartReviewPage] No member ID available, loading default PDF");
      this.loadFallbackPdf();
      return;
    }
    const pdfPath = `assets/charts/${memberId}.pdf`;
    console.log(`[ChartReviewPage] Attempting to load PDF from: ${pdfPath}`);
    this.http.get(pdfPath, { responseType: "blob" }).subscribe({
      next: (blob) => {
        console.log(`[ChartReviewPage] PDF for member ${memberId} loaded successfully`);
        this.convertBlobToDataUrlAndLoad(blob);
      },
      error: (error) => {
        console.error(`[ChartReviewPage] Error loading PDF for member ${memberId}:`, error);
        console.log("[ChartReviewPage] Falling back to default PDF...");
        this.loadFallbackPdf();
      }
    });
  }
  // Load fallback PDF when member-specific PDF is not available
  loadFallbackPdf() {
    console.log("[ChartReviewPage] Loading fallback PDF...");
    this.http.get("assets/charts/CBP_Redacted_Usability.pdf", { responseType: "blob" }).subscribe({
      next: (blob) => {
        console.log("[ChartReviewPage] Fallback PDF loaded successfully");
        this.convertBlobToDataUrlAndLoad(blob);
      },
      error: (error) => {
        console.error("[ChartReviewPage] Error loading fallback PDF:", error);
        console.log("[ChartReviewPage] Trying final fallback PDF...");
        this.http.get("assets/charts/55820474.pdf", { responseType: "blob" }).subscribe({
          next: (blob) => {
            console.log("[ChartReviewPage] Final fallback PDF loaded successfully");
            this.convertBlobToDataUrlAndLoad(blob);
          },
          error: (altError) => {
            console.error("[ChartReviewPage] Error loading final fallback PDF:", altError);
            console.error("[ChartReviewPage] No PDFs available to load");
          }
        });
      }
    });
  }
  // Helper method to convert blob to data URL and load into PDF service
  convertBlobToDataUrlAndLoad(blob) {
    if (!isPlatformBrowser(this.platformId)) {
      console.log("[ChartReviewPage] Not in browser environment, skipping FileReader operation");
      return;
    }
    if (typeof FileReader === "undefined") {
      console.error("[ChartReviewPage] FileReader is not available in this environment");
      return;
    }
    const reader = new FileReader();
    reader.onload = () => {
      const dataUrl = reader.result;
      console.log("[ChartReviewPage] PDF converted to base64, loading into viewer");
      this.pdfService.loadPdfFromDataUrl(dataUrl).subscribe({
        next: (pdfDoc) => {
          console.log(`[ChartReviewPage] PDF loaded successfully: ${pdfDoc.numPages} pages`);
        },
        error: (error) => {
          console.error("[ChartReviewPage] Error loading PDF via service:", error);
        }
      });
    };
    reader.readAsDataURL(blob);
  }
  // Hits component event handlers
  onHitsDataChange(data) {
    console.log("Hits data changed:", data);
    this.hitsData = data;
  }
  onHitsPageClick(event) {
    console.log("Hits page clicked:", event);
    this.goToPdfPage(event.page);
  }
  onHitsCommentChange(event) {
    console.log("Hits comment changed:", event);
    const hitIndex = this.hitsData.findIndex((hit) => hit.id === event.hit.id);
    if (hitIndex !== -1) {
      this.hitsData[hitIndex].comment = event.comment;
    }
  }
  onHitsIncludeChange(event) {
    console.log("Hits include changed:", event);
    const hitIndex = this.hitsData.findIndex((hit) => hit.id === event.hit.id);
    if (hitIndex !== -1) {
      this.hitsData[hitIndex].include = event.include;
    }
  }
  // Results component event handlers
  onResultsDataChange(data) {
    console.log("Results data changed:", data);
    this.resultsData = data;
  }
  // Complete review action
  onCompleteReview() {
    console.log("Complete review clicked");
    console.log("Results data:", this.resultsData);
    console.log("Hits data:", this.hitsData);
  }
  static \u0275fac = function ChartReviewPageComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ChartReviewPageComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _ChartReviewPageComponent, selectors: [["app-chart-review-page"]], viewQuery: function ChartReviewPageComponent_Query(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275viewQuery(PdfViewerComponent, 5);
    }
    if (rf & 2) {
      let _t;
      \u0275\u0275queryRefresh(_t = \u0275\u0275loadQuery()) && (ctx.pdfViewerComponent = _t.first);
    }
  }, decls: 16, vars: 6, consts: [[1, "container"], ["logoSrc", "assets/logos/Stellarus_logo_2C_blacktype.png?v=2024", "logoAlt", "Stellarus Logo", 3, "logoClick", "userClick", "dropdownToggle", "menuItemClick", "user", "menuItems"], [1, "main-content"], [1, "demographics-section"], ["backButtonText", "Back", 3, "backClick", "data", "showBackButton"], [1, "content-layout"], [1, "pdf-column"], [1, "right-column"], [1, "hits-section"], ["title", "Hits", 3, "dataChange", "pageClick", "commentChange", "includeChange", "data"], [1, "results-section"], ["title", "Results", 3, "ngModelChange", "dataChange", "ngModel"], [1, "submit-section"], ["variant", "primary", 3, "buttonClick"]], template: function ChartReviewPageComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "app-menu", 1);
      \u0275\u0275listener("logoClick", function ChartReviewPageComponent_Template_app_menu_logoClick_1_listener() {
        return ctx.onLogoClick();
      })("userClick", function ChartReviewPageComponent_Template_app_menu_userClick_1_listener() {
        return ctx.onUserClick();
      })("dropdownToggle", function ChartReviewPageComponent_Template_app_menu_dropdownToggle_1_listener($event) {
        return ctx.onDropdownToggle($event);
      })("menuItemClick", function ChartReviewPageComponent_Template_app_menu_menuItemClick_1_listener($event) {
        return ctx.onMenuItemClick($event);
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(2, "div", 2)(3, "div", 3)(4, "app-demographics", 4);
      \u0275\u0275listener("backClick", function ChartReviewPageComponent_Template_app_demographics_backClick_4_listener() {
        return ctx.onDemographicsBackClick();
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(5, "div", 5)(6, "div", 6);
      \u0275\u0275element(7, "app-pdf-viewer");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(8, "div", 7)(9, "div", 8)(10, "app-hits", 9);
      \u0275\u0275listener("dataChange", function ChartReviewPageComponent_Template_app_hits_dataChange_10_listener($event) {
        return ctx.onHitsDataChange($event);
      })("pageClick", function ChartReviewPageComponent_Template_app_hits_pageClick_10_listener($event) {
        return ctx.onHitsPageClick($event);
      })("commentChange", function ChartReviewPageComponent_Template_app_hits_commentChange_10_listener($event) {
        return ctx.onHitsCommentChange($event);
      })("includeChange", function ChartReviewPageComponent_Template_app_hits_includeChange_10_listener($event) {
        return ctx.onHitsIncludeChange($event);
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(11, "div", 10)(12, "app-results", 11);
      \u0275\u0275twoWayListener("ngModelChange", function ChartReviewPageComponent_Template_app_results_ngModelChange_12_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.resultsData, $event) || (ctx.resultsData = $event);
        return $event;
      });
      \u0275\u0275listener("dataChange", function ChartReviewPageComponent_Template_app_results_dataChange_12_listener($event) {
        return ctx.onResultsDataChange($event);
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(13, "div", 12)(14, "app-button", 13);
      \u0275\u0275listener("buttonClick", function ChartReviewPageComponent_Template_app_button_buttonClick_14_listener() {
        return ctx.onCompleteReview();
      });
      \u0275\u0275text(15, " Submit ");
      \u0275\u0275elementEnd()()()()()();
    }
    if (rf & 2) {
      \u0275\u0275advance();
      \u0275\u0275property("user", ctx.userProfile)("menuItems", ctx.menuItems);
      \u0275\u0275advance(3);
      \u0275\u0275property("data", ctx.demographicsData)("showBackButton", true);
      \u0275\u0275advance(6);
      \u0275\u0275property("data", ctx.hitsData);
      \u0275\u0275advance(2);
      \u0275\u0275twoWayProperty("ngModel", ctx.resultsData);
    }
  }, dependencies: [
    CommonModule,
    PdfViewerComponent,
    FormsModule,
    NgControlStatus,
    NgModel,
    MenuComponent,
    HitsComponent,
    ResultsComponent,
    ButtonComponent,
    DemographicsComponent,
    RouterModule
  ], styles: ["\n\n.container[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n}\n.main-content[_ngcontent-%COMP%] {\n  width: 100%;\n  max-width: 1800px;\n  margin: 0 auto;\n  background: #F6F6F6;\n  flex-direction: column;\n  justify-content: flex-start;\n  align-items: flex-start;\n  display: flex;\n  padding: 20px 30px;\n  gap: 20px;\n  box-sizing: border-box;\n}\n@media (max-width: 768px) {\n  .main-content[_ngcontent-%COMP%] {\n    padding: 15px 20px;\n  }\n}\n@media (max-width: 480px) {\n  .main-content[_ngcontent-%COMP%] {\n    padding: 10px 15px;\n  }\n}\n.demographics-section[_ngcontent-%COMP%] {\n  width: 100%;\n  margin-bottom: 0;\n}\n@media (max-width: 1200px) {\n  .demographics-section[_ngcontent-%COMP%] {\n    width: 100%;\n  }\n}\n[_nghost-%COMP%]     .demographics-section app-demographics .demographics-container {\n  max-width: 100%;\n  box-sizing: border-box;\n}\n.content-layout[_ngcontent-%COMP%] {\n  width: 100%;\n  justify-content: flex-start;\n  align-items: flex-start;\n  gap: 20px;\n  display: flex;\n}\n@media (max-width: 1400px) {\n  .content-layout[_ngcontent-%COMP%] {\n    gap: 15px;\n  }\n}\n@media (max-width: 1200px) {\n  .content-layout[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: 20px;\n  }\n}\n@media (max-width: 768px) {\n  .content-layout[_ngcontent-%COMP%] {\n    gap: 15px;\n  }\n}\n.pdf-column[_ngcontent-%COMP%] {\n  flex: 1;\n  min-width: 500px;\n  padding: 20px;\n  background: white;\n  border-radius: 8px;\n  outline: 1px #F1F5F7 solid;\n  outline-offset: -1px;\n  flex-direction: column;\n  justify-content: flex-start;\n  align-items: flex-start;\n  display: flex;\n}\n@media (max-width: 1400px) {\n  .pdf-column[_ngcontent-%COMP%] {\n    min-width: 450px;\n  }\n}\n@media (max-width: 1200px) {\n  .pdf-column[_ngcontent-%COMP%] {\n    flex: none;\n    width: 100%;\n    min-width: auto;\n  }\n}\n@media (max-width: 768px) {\n  .pdf-column[_ngcontent-%COMP%] {\n    padding: 15px;\n  }\n}\n@media (max-width: 480px) {\n  .pdf-column[_ngcontent-%COMP%] {\n    padding: 10px;\n  }\n}\n.right-column[_ngcontent-%COMP%] {\n  width: 517px;\n  max-width: 600px;\n  min-width: 400px;\n  flex-shrink: 0;\n  flex-direction: column;\n  justify-content: flex-start;\n  align-items: flex-end;\n  gap: 20px;\n  display: flex;\n}\n@media (max-width: 1600px) {\n  .right-column[_ngcontent-%COMP%] {\n    width: 500px;\n  }\n}\n@media (max-width: 1400px) {\n  .right-column[_ngcontent-%COMP%] {\n    width: 480px;\n    min-width: 380px;\n  }\n}\n@media (max-width: 1200px) {\n  .right-column[_ngcontent-%COMP%] {\n    width: 100%;\n    max-width: none;\n    min-width: auto;\n    align-items: stretch;\n  }\n}\n@media (max-width: 768px) {\n  .right-column[_ngcontent-%COMP%] {\n    gap: 15px;\n  }\n}\n.hits-section[_ngcontent-%COMP%] {\n  align-self: stretch;\n  display: flex;\n  flex-direction: column;\n}\n.results-section[_ngcontent-%COMP%] {\n  align-self: stretch;\n  display: flex;\n  flex-direction: column;\n}\n.submit-section[_ngcontent-%COMP%] {\n  border-radius: 8px;\n  justify-content: flex-end;\n  align-items: flex-end;\n  display: inline-flex;\n}\n[_nghost-%COMP%]     app-pdf-viewer {\n  align-self: stretch;\n  height: 979px;\n  min-height: 600px;\n  position: relative;\n  background: #E8E8EB;\n  overflow: hidden;\n}\n@media (max-width: 1600px) {\n  [_nghost-%COMP%]     app-pdf-viewer {\n    height: 850px;\n  }\n}\n@media (max-width: 1400px) {\n  [_nghost-%COMP%]     app-pdf-viewer {\n    height: 750px;\n    min-height: 550px;\n  }\n}\n@media (max-width: 1200px) {\n  [_nghost-%COMP%]     app-pdf-viewer {\n    height: 70vh;\n    min-height: 500px;\n  }\n}\n@media (max-width: 768px) {\n  [_nghost-%COMP%]     app-pdf-viewer {\n    height: 60vh;\n    min-height: 400px;\n  }\n}\n@media (max-width: 480px) {\n  [_nghost-%COMP%]     app-pdf-viewer {\n    height: 50vh;\n    min-height: 350px;\n  }\n}\n[_nghost-%COMP%]     .pdf-viewer-container {\n  width: 100%;\n  height: 100%;\n  background: #E8E8EB;\n  overflow: auto;\n}\n[_nghost-%COMP%]     ngx-extended-pdf-viewer {\n  width: 100%;\n  height: 100%;\n}\n/*# sourceMappingURL=chart-review-page.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ChartReviewPageComponent, [{
    type: Component,
    args: [{ selector: "app-chart-review-page", standalone: true, imports: [
      CommonModule,
      PdfViewerComponent,
      FormsModule,
      MenuComponent,
      HitsComponent,
      ResultsComponent,
      ButtonComponent,
      DemographicsComponent,
      RouterModule
    ], template: '<div class="container">\r\n  <!-- Menu -->\r\n  <app-menu\r\n    logoSrc="assets/logos/Stellarus_logo_2C_blacktype.png?v=2024"\r\n    logoAlt="Stellarus Logo"\r\n    [user]="userProfile"\r\n    [menuItems]="menuItems"\r\n    (logoClick)="onLogoClick()"\r\n    (userClick)="onUserClick()"\r\n    (dropdownToggle)="onDropdownToggle($event)"\r\n    (menuItemClick)="onMenuItemClick($event)">\r\n  </app-menu>\r\n\r\n  <div class="main-content">\r\n    <!-- Patient Demographics Header - spans full width -->\r\n    <div class="demographics-section">\r\n      <app-demographics\r\n        [data]="demographicsData"\r\n        [showBackButton]="true"\r\n        backButtonText="Back"\r\n        (backClick)="onDemographicsBackClick()">\r\n      </app-demographics>\r\n    </div>\r\n\r\n    <!-- Two-column layout matching Figma mockup -->\r\n    <div class="content-layout">\r\n      <!-- Left Column: PDF Viewer -->\r\n      <div class="pdf-column">\r\n        <app-pdf-viewer></app-pdf-viewer>\r\n      </div>\r\n\r\n      <!-- Right Column: Hits and Results -->\r\n      <div class="right-column">\r\n        <!-- Hits Section -->\r\n        <div class="hits-section">\r\n          <app-hits\r\n            title="Hits"\r\n            [data]="hitsData"\r\n            (dataChange)="onHitsDataChange($event)"\r\n            (pageClick)="onHitsPageClick($event)"\r\n            (commentChange)="onHitsCommentChange($event)"\r\n            (includeChange)="onHitsIncludeChange($event)">\r\n          </app-hits>\r\n        </div>\r\n\r\n        <!-- Results Section -->\r\n        <div class="results-section">\r\n          <app-results\r\n            title="Results"\r\n            [(ngModel)]="resultsData"\r\n            (dataChange)="onResultsDataChange($event)">\r\n          </app-results>\r\n        </div>\r\n\r\n        <!-- Submit Button -->\r\n        <div class="submit-section">\r\n          <app-button\r\n            variant="primary"\r\n            (buttonClick)="onCompleteReview()">\r\n            Submit\r\n          </app-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n', styles: ["/* src/app/features/chart-review/pages/chart-review-page/chart-review-page.component.scss */\n.container {\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n}\n.main-content {\n  width: 100%;\n  max-width: 1800px;\n  margin: 0 auto;\n  background: #F6F6F6;\n  flex-direction: column;\n  justify-content: flex-start;\n  align-items: flex-start;\n  display: flex;\n  padding: 20px 30px;\n  gap: 20px;\n  box-sizing: border-box;\n}\n@media (max-width: 768px) {\n  .main-content {\n    padding: 15px 20px;\n  }\n}\n@media (max-width: 480px) {\n  .main-content {\n    padding: 10px 15px;\n  }\n}\n.demographics-section {\n  width: 100%;\n  margin-bottom: 0;\n}\n@media (max-width: 1200px) {\n  .demographics-section {\n    width: 100%;\n  }\n}\n:host ::ng-deep .demographics-section app-demographics .demographics-container {\n  max-width: 100%;\n  box-sizing: border-box;\n}\n.content-layout {\n  width: 100%;\n  justify-content: flex-start;\n  align-items: flex-start;\n  gap: 20px;\n  display: flex;\n}\n@media (max-width: 1400px) {\n  .content-layout {\n    gap: 15px;\n  }\n}\n@media (max-width: 1200px) {\n  .content-layout {\n    flex-direction: column;\n    gap: 20px;\n  }\n}\n@media (max-width: 768px) {\n  .content-layout {\n    gap: 15px;\n  }\n}\n.pdf-column {\n  flex: 1;\n  min-width: 500px;\n  padding: 20px;\n  background: white;\n  border-radius: 8px;\n  outline: 1px #F1F5F7 solid;\n  outline-offset: -1px;\n  flex-direction: column;\n  justify-content: flex-start;\n  align-items: flex-start;\n  display: flex;\n}\n@media (max-width: 1400px) {\n  .pdf-column {\n    min-width: 450px;\n  }\n}\n@media (max-width: 1200px) {\n  .pdf-column {\n    flex: none;\n    width: 100%;\n    min-width: auto;\n  }\n}\n@media (max-width: 768px) {\n  .pdf-column {\n    padding: 15px;\n  }\n}\n@media (max-width: 480px) {\n  .pdf-column {\n    padding: 10px;\n  }\n}\n.right-column {\n  width: 517px;\n  max-width: 600px;\n  min-width: 400px;\n  flex-shrink: 0;\n  flex-direction: column;\n  justify-content: flex-start;\n  align-items: flex-end;\n  gap: 20px;\n  display: flex;\n}\n@media (max-width: 1600px) {\n  .right-column {\n    width: 500px;\n  }\n}\n@media (max-width: 1400px) {\n  .right-column {\n    width: 480px;\n    min-width: 380px;\n  }\n}\n@media (max-width: 1200px) {\n  .right-column {\n    width: 100%;\n    max-width: none;\n    min-width: auto;\n    align-items: stretch;\n  }\n}\n@media (max-width: 768px) {\n  .right-column {\n    gap: 15px;\n  }\n}\n.hits-section {\n  align-self: stretch;\n  display: flex;\n  flex-direction: column;\n}\n.results-section {\n  align-self: stretch;\n  display: flex;\n  flex-direction: column;\n}\n.submit-section {\n  border-radius: 8px;\n  justify-content: flex-end;\n  align-items: flex-end;\n  display: inline-flex;\n}\n:host ::ng-deep app-pdf-viewer {\n  align-self: stretch;\n  height: 979px;\n  min-height: 600px;\n  position: relative;\n  background: #E8E8EB;\n  overflow: hidden;\n}\n@media (max-width: 1600px) {\n  :host ::ng-deep app-pdf-viewer {\n    height: 850px;\n  }\n}\n@media (max-width: 1400px) {\n  :host ::ng-deep app-pdf-viewer {\n    height: 750px;\n    min-height: 550px;\n  }\n}\n@media (max-width: 1200px) {\n  :host ::ng-deep app-pdf-viewer {\n    height: 70vh;\n    min-height: 500px;\n  }\n}\n@media (max-width: 768px) {\n  :host ::ng-deep app-pdf-viewer {\n    height: 60vh;\n    min-height: 400px;\n  }\n}\n@media (max-width: 480px) {\n  :host ::ng-deep app-pdf-viewer {\n    height: 50vh;\n    min-height: 350px;\n  }\n}\n:host ::ng-deep .pdf-viewer-container {\n  width: 100%;\n  height: 100%;\n  background: #E8E8EB;\n  overflow: auto;\n}\n:host ::ng-deep ngx-extended-pdf-viewer {\n  width: 100%;\n  height: 100%;\n}\n/*# sourceMappingURL=chart-review-page.component.css.map */\n"] }]
  }], null, { pdfViewerComponent: [{
    type: ViewChild,
    args: [PdfViewerComponent]
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(ChartReviewPageComponent, { className: "ChartReviewPageComponent", filePath: "src/app/features/chart-review/pages/chart-review-page/chart-review-page.component.ts", lineNumber: 41 });
})();

// src/app/features/chart-review/chart-review-routing.module.ts
var routes = [
  {
    path: ":id",
    // Changed from '' to ':id' to accept a route parameter
    component: ChartReviewPageComponent
  },
  {
    path: "pdf-test",
    component: PdfViewerTestComponent
  }
];
var ChartReviewRoutingModule = class _ChartReviewRoutingModule {
  static \u0275fac = function ChartReviewRoutingModule_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ChartReviewRoutingModule)();
  };
  static \u0275mod = /* @__PURE__ */ \u0275\u0275defineNgModule({ type: _ChartReviewRoutingModule });
  static \u0275inj = /* @__PURE__ */ \u0275\u0275defineInjector({ imports: [RouterModule.forChild(routes), RouterModule] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ChartReviewRoutingModule, [{
    type: NgModule,
    args: [{
      imports: [RouterModule.forChild(routes)],
      exports: [RouterModule]
    }]
  }], null, null);
})();

// src/app/features/chart-review/components/annotation/annotation.component.ts
var AnnotationComponent = class _AnnotationComponent {
  static \u0275fac = function AnnotationComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _AnnotationComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _AnnotationComponent, selectors: [["app-annotation"]], decls: 2, vars: 0, template: function AnnotationComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "p");
      \u0275\u0275text(1, "annotation works!");
      \u0275\u0275elementEnd();
    }
  }, dependencies: [CommonModule], encapsulation: 2 });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AnnotationComponent, [{
    type: Component,
    args: [{ selector: "app-annotation", standalone: true, imports: [CommonModule], template: "<p>annotation works!</p>\r\n" }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(AnnotationComponent, { className: "AnnotationComponent", filePath: "src/app/features/chart-review/components/annotation/annotation.component.ts", lineNumber: 11 });
})();

// src/app/features/chart-review/components/validation/validation.component.ts
var ValidationComponent = class _ValidationComponent {
  static \u0275fac = function ValidationComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ValidationComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _ValidationComponent, selectors: [["app-validation"]], decls: 2, vars: 0, template: function ValidationComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "p");
      \u0275\u0275text(1, "validation works!");
      \u0275\u0275elementEnd();
    }
  }, dependencies: [CommonModule], encapsulation: 2 });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ValidationComponent, [{
    type: Component,
    args: [{ selector: "app-validation", standalone: true, imports: [CommonModule], template: "<p>validation works!</p>\r\n" }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(ValidationComponent, { className: "ValidationComponent", filePath: "src/app/features/chart-review/components/validation/validation.component.ts", lineNumber: 11 });
})();

// src/app/features/chart-review/chart-review.module.ts
var ChartReviewModule = class _ChartReviewModule {
  static \u0275fac = function ChartReviewModule_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ChartReviewModule)();
  };
  static \u0275mod = /* @__PURE__ */ \u0275\u0275defineNgModule({ type: _ChartReviewModule });
  static \u0275inj = /* @__PURE__ */ \u0275\u0275defineInjector({ imports: [
    CommonModule,
    FormsModule,
    NgxExtendedPdfViewerModule,
    ChartReviewRoutingModule,
    // Import all standalone components
    PdfViewerComponent,
    PdfViewerTestComponent,
    AnnotationComponent,
    ValidationComponent,
    ChartReviewPageComponent
  ] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ChartReviewModule, [{
    type: NgModule,
    args: [{
      declarations: [
        // All components are now standalone, so no declarations needed
      ],
      imports: [
        CommonModule,
        FormsModule,
        NgxExtendedPdfViewerModule,
        ChartReviewRoutingModule,
        // Import all standalone components
        PdfViewerComponent,
        PdfViewerTestComponent,
        AnnotationComponent,
        ValidationComponent,
        ChartReviewPageComponent
      ],
      exports: [
        // No need to export components that are imported
      ]
    }]
  }], null, null);
})();
export {
  ChartReviewModule
};
//# sourceMappingURL=chunk-JQPK26PE.js.map

{"version": 3, "sources": ["src/app/features/dashboard/components/sort-button/sort-button.component.scss"], "sourcesContent": ["@use 'variables' as vars;\r\n@use 'mixins' as mix;\r\n\r\n.sort-button {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 8px 14px;\r\n  background-color: var(--white, white);\r\n  border-radius: 8px;\r\n  border: 1px solid var(--light-content, #D9E1E7);\r\n  cursor: pointer;\r\n  \r\n  &:hover {\r\n    background-color: var(--light-background, #F9FBFC);\r\n  }\r\n  \r\n  span {\r\n    font-size: 12px;\r\n    font-weight: 500;\r\n    line-height: 20px;\r\n    color: var(--text-black, #17181A);\r\n  }\r\n}\r\n\r\n.sort-icon {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}"], "mappings": ";AAGA,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;AACA,WAAA,IAAA;AACA,oBAAA,IAAA,OAAA,EAAA;AACA,iBAAA;AACA,UAAA,IAAA,MAAA,IAAA,eAAA,EAAA;AACA,UAAA;;AAEA,CAVF,WAUE;AACE,oBAAA,IAAA,kBAAA,EAAA;;AAGF,CAdF,YAcE;AACE,aAAA;AACA,eAAA;AACA,eAAA;AACA,SAAA,IAAA,YAAA,EAAA;;AAIJ,CAAA;AACE,WAAA;AACA,eAAA;AACA,mBAAA;;", "names": []}
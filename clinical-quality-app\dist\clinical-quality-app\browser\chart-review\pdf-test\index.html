<!DOCTYPE html><html lang="en"><head>
  <meta charset="utf-8">
  <title>ClinicalQualityApp</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/png" href="assets/logos/Stellarus-Favicon-red.png">
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&amp;display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
<link rel="stylesheet" href="styles.css"><style ng-app-id="ng">

.container[_ngcontent-ng-c2414578827] {
  display: flex;
  flex-direction: column;
  height: 100vh;
}
.main-content[_ngcontent-ng-c2414578827] {
  width: 100%;
  max-width: 1800px;
  margin: 0 auto;
  background: #F6F6F6;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  display: flex;
  padding: 20px 30px;
  gap: 20px;
  box-sizing: border-box;
}
@media (max-width: 768px) {
  .main-content[_ngcontent-ng-c2414578827] {
    padding: 15px 20px;
  }
}
@media (max-width: 480px) {
  .main-content[_ngcontent-ng-c2414578827] {
    padding: 10px 15px;
  }
}
.demographics-section[_ngcontent-ng-c2414578827] {
  width: 100%;
  margin-bottom: 0;
}
@media (max-width: 1200px) {
  .demographics-section[_ngcontent-ng-c2414578827] {
    width: 100%;
  }
}
[_nghost-ng-c2414578827]     .demographics-section app-demographics .demographics-container {
  max-width: 100%;
  box-sizing: border-box;
}
.content-layout[_ngcontent-ng-c2414578827] {
  width: 100%;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 20px;
  display: flex;
}
@media (max-width: 1400px) {
  .content-layout[_ngcontent-ng-c2414578827] {
    gap: 15px;
  }
}
@media (max-width: 1200px) {
  .content-layout[_ngcontent-ng-c2414578827] {
    flex-direction: column;
    gap: 20px;
  }
}
@media (max-width: 768px) {
  .content-layout[_ngcontent-ng-c2414578827] {
    gap: 15px;
  }
}
.pdf-column[_ngcontent-ng-c2414578827] {
  flex: 1;
  min-width: 500px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  outline: 1px #F1F5F7 solid;
  outline-offset: -1px;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  display: flex;
}
@media (max-width: 1400px) {
  .pdf-column[_ngcontent-ng-c2414578827] {
    min-width: 450px;
  }
}
@media (max-width: 1200px) {
  .pdf-column[_ngcontent-ng-c2414578827] {
    flex: none;
    width: 100%;
    min-width: auto;
  }
}
@media (max-width: 768px) {
  .pdf-column[_ngcontent-ng-c2414578827] {
    padding: 15px;
  }
}
@media (max-width: 480px) {
  .pdf-column[_ngcontent-ng-c2414578827] {
    padding: 10px;
  }
}
.right-column[_ngcontent-ng-c2414578827] {
  width: 517px;
  max-width: 600px;
  min-width: 400px;
  flex-shrink: 0;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-end;
  gap: 20px;
  display: flex;
}
@media (max-width: 1600px) {
  .right-column[_ngcontent-ng-c2414578827] {
    width: 500px;
  }
}
@media (max-width: 1400px) {
  .right-column[_ngcontent-ng-c2414578827] {
    width: 480px;
    min-width: 380px;
  }
}
@media (max-width: 1200px) {
  .right-column[_ngcontent-ng-c2414578827] {
    width: 100%;
    max-width: none;
    min-width: auto;
    align-items: stretch;
  }
}
@media (max-width: 768px) {
  .right-column[_ngcontent-ng-c2414578827] {
    gap: 15px;
  }
}
.hits-section[_ngcontent-ng-c2414578827] {
  align-self: stretch;
  display: flex;
  flex-direction: column;
}
.results-section[_ngcontent-ng-c2414578827] {
  align-self: stretch;
  display: flex;
  flex-direction: column;
}
.submit-section[_ngcontent-ng-c2414578827] {
  border-radius: 8px;
  justify-content: flex-end;
  align-items: flex-end;
  display: inline-flex;
}
[_nghost-ng-c2414578827]     app-pdf-viewer {
  align-self: stretch;
  height: 979px;
  min-height: 600px;
  position: relative;
  background: #E8E8EB;
  overflow: hidden;
}
@media (max-width: 1600px) {
  [_nghost-ng-c2414578827]     app-pdf-viewer {
    height: 850px;
  }
}
@media (max-width: 1400px) {
  [_nghost-ng-c2414578827]     app-pdf-viewer {
    height: 750px;
    min-height: 550px;
  }
}
@media (max-width: 1200px) {
  [_nghost-ng-c2414578827]     app-pdf-viewer {
    height: 70vh;
    min-height: 500px;
  }
}
@media (max-width: 768px) {
  [_nghost-ng-c2414578827]     app-pdf-viewer {
    height: 60vh;
    min-height: 400px;
  }
}
@media (max-width: 480px) {
  [_nghost-ng-c2414578827]     app-pdf-viewer {
    height: 50vh;
    min-height: 350px;
  }
}
[_nghost-ng-c2414578827]     .pdf-viewer-container {
  width: 100%;
  height: 100%;
  background: #E8E8EB;
  overflow: auto;
}
[_nghost-ng-c2414578827]     ngx-extended-pdf-viewer {
  width: 100%;
  height: 100%;
}
/*# sourceMappingURL=/chart-review-page.component.css.map */</style><style ng-app-id="ng">

.menu-container[_ngcontent-ng-c3806090911] {
  width: 100%;
  height: 80px;
  background: #FFFFFF;
  border-bottom: 1px solid #F1F5F7;
  position: sticky;
  top: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
}
.menu-content[_ngcontent-ng-c3806090911] {
  width: 100%;
  height: 100%;
  padding: 12px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.logo-section[_ngcontent-ng-c3806090911] {
  flex: 1;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.logo-container[_ngcontent-ng-c3806090911] {
  width: 240px;
  padding: 10px 20px;
  background: #FFFFFF;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: opacity 0.2s ease;
}
.logo-container[_ngcontent-ng-c3806090911]:hover {
  opacity: 0.8;
}
.logo-image[_ngcontent-ng-c3806090911] {
  width: 150px;
  height: 37.4px;
  object-fit: contain;
}
.logo-placeholder[_ngcontent-ng-c3806090911] {
  font-size: 18px;
  font-family: "Urbane", sans-serif;
  font-weight: 600;
  color: #17181A;
}
.user-section[_ngcontent-ng-c3806090911] {
  position: relative;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 16px;
}
.user-info[_ngcontent-ng-c3806090911] {
  padding: 12px 20px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 12px;
}
.user-name[_ngcontent-ng-c3806090911] {
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  font-weight: 300;
  line-height: 20px;
  color: #17181A;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.user-avatar[_ngcontent-ng-c3806090911] {
  cursor: pointer;
  transition: transform 0.2s ease;
}
.user-avatar[_ngcontent-ng-c3806090911]:hover {
  transform: scale(1.05);
}
.avatar-circle[_ngcontent-ng-c3806090911] {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #F1F5F7;
  border: 1px solid #D9E1E7;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}
.avatar-circle[_ngcontent-ng-c3806090911]:hover {
  border-color: #547996;
  background: #FFFFFF;
}
.user-icon[_ngcontent-ng-c3806090911] {
  width: 20px;
  height: 20px;
  color: #547996;
  transition: color 0.2s ease;
}
.avatar-circle[_ngcontent-ng-c3806090911]:hover   .user-icon[_ngcontent-ng-c3806090911] {
  color: #17181A;
}
.dropdown-arrow[_ngcontent-ng-c3806090911] {
  cursor: pointer;
  transition: transform 0.2s ease;
  padding: 4px;
}
.dropdown-arrow.open[_ngcontent-ng-c3806090911] {
  transform: rotate(180deg);
}
.dropdown-arrow[_ngcontent-ng-c3806090911]:hover {
  background-color: #F1F5F7;
  border-radius: 4px;
}
.arrow-icon[_ngcontent-ng-c3806090911] {
  width: 16px;
  height: 16px;
  color: #547996;
  transition: color 0.2s ease;
}
.dropdown-arrow[_ngcontent-ng-c3806090911]:hover   .arrow-icon[_ngcontent-ng-c3806090911] {
  color: #17181A;
}
.user-dropdown[_ngcontent-ng-c3806090911] {
  position: absolute;
  top: 100%;
  right: 0;
  background: #FFFFFF;
  border-radius: 10px;
  border: 1px solid #D9E1E7;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1001;
  margin-top: 8px;
  min-width: 200px;
  overflow: hidden;
}
.dropdown-content[_ngcontent-ng-c3806090911] {
  padding: 8px 0;
}
.dropdown-item[_ngcontent-ng-c3806090911] {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  font-weight: 300;
  color: #17181A;
}
.dropdown-item[_ngcontent-ng-c3806090911]:hover {
  background-color: #F1F5F7;
}
.dropdown-item[_ngcontent-ng-c3806090911]:active {
  background-color: #D9E1E7;
}
.item-icon[_ngcontent-ng-c3806090911] {
  font-size: 14px;
  width: 16px;
  text-align: center;
}
.item-label[_ngcontent-ng-c3806090911] {
  flex: 1;
}
@media (min-width: 600px) {
  .menu-content[_ngcontent-ng-c3806090911] {
    padding: 12px 20px;
  }
  .logo-container[_ngcontent-ng-c3806090911] {
    width: 200px;
    padding: 8px 16px;
  }
  .logo-image[_ngcontent-ng-c3806090911] {
    width: 120px;
    height: 30px;
  }
}
@media (max-width: 599px) {
  .menu-content[_ngcontent-ng-c3806090911] {
    padding: 8px 16px;
  }
  .logo-container[_ngcontent-ng-c3806090911] {
    width: auto;
    padding: 4px 8px;
  }
  .logo-image[_ngcontent-ng-c3806090911] {
    width: 100px;
    height: 25px;
  }
  .user-name[_ngcontent-ng-c3806090911] {
    display: none;
  }
  .user-dropdown[_ngcontent-ng-c3806090911] {
    right: -16px;
    min-width: 180px;
  }
}
/*# sourceMappingURL=/menu.component.css.map */</style><style ng-app-id="ng">

.demographics-container[_ngcontent-ng-c1061394248] {
  width: 100%;
  padding: 20px;
  background: #FFFFFF;
  border-radius: 8px;
  border: 1px solid #F1F5F7;
}
.demographics-content[_ngcontent-ng-c1061394248] {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 48px;
  width: 100%;
}
.left-section[_ngcontent-ng-c1061394248] {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  gap: 48px;
}
.header-section[_ngcontent-ng-c1061394248] {
  display: flex;
  align-items: flex-start;
  gap: 36px;
}
.back-button[_ngcontent-ng-c1061394248] {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: opacity 0.2s ease;
}
.back-button[_ngcontent-ng-c1061394248]:hover {
  opacity: 0.8;
}
.back-icon[_ngcontent-ng-c1061394248] {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #0071BC;
}
.back-text[_ngcontent-ng-c1061394248] {
  color: #0071BC;
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  font-weight: 500;
  line-height: 20px;
}
.measure-info[_ngcontent-ng-c1061394248] {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.measure-title[_ngcontent-ng-c1061394248] {
  color: #17181A;
  font-size: 20px;
  font-family: "Urbane", sans-serif;
  font-weight: 600;
  line-height: 32px;
}
.measure-subtitle[_ngcontent-ng-c1061394248] {
  color: #547996;
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  font-weight: 500;
  line-height: 20px;
}
.right-section[_ngcontent-ng-c1061394248] {
  display: flex;
  align-items: center;
  gap: 60px;
}
.demographics-group[_ngcontent-ng-c1061394248] {
  display: flex;
  align-items: center;
}
.demographics-group.primary-group[_ngcontent-ng-c1061394248] {
  gap: 40px;
}
.demographics-group.provider-group[_ngcontent-ng-c1061394248] {
  gap: 24px;
}
.demographic-item[_ngcontent-ng-c1061394248] {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: fit-content;
}
.demographic-item[_ngcontent-ng-c1061394248]:nth-child(1) {
  width: 68px;
}
.demographic-item[_ngcontent-ng-c1061394248]:nth-child(2) {
  width: 57px;
  min-width: 57px;
}
.demographic-item[_ngcontent-ng-c1061394248]:nth-child(3) {
  width: 69px;
}
.demographic-item[_ngcontent-ng-c1061394248]:nth-child(4) {
  min-width: auto;
}
.demographic-item[_ngcontent-ng-c1061394248]:nth-child(5) {
  width: 51px;
}
.provider-group[_ngcontent-ng-c1061394248]   .demographic-item[_ngcontent-ng-c1061394248]:nth-child(1) {
  width: 115px;
  min-width: 115px;
}
.provider-group[_ngcontent-ng-c1061394248]   .demographic-item[_ngcontent-ng-c1061394248]:nth-child(2) {
  width: 68px;
}
.demographic-value[_ngcontent-ng-c1061394248] {
  color: #17181A;
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  font-weight: 600;
  line-height: 20px;
  word-wrap: break-word;
}
.demographic-label[_ngcontent-ng-c1061394248] {
  color: #547996;
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  font-weight: 500;
  line-height: 20px;
  word-wrap: break-word;
}
@media (max-width: 599px) {
  .demographics-content[_ngcontent-ng-c1061394248] {
    flex-direction: column;
    gap: 24px;
  }
  .right-section[_ngcontent-ng-c1061394248] {
    flex-direction: column;
    gap: 24px;
    align-items: flex-start;
  }
  .demographics-group[_ngcontent-ng-c1061394248] {
    flex-wrap: wrap;
    gap: 16px !important;
  }
  .demographic-item[_ngcontent-ng-c1061394248] {
    min-width: auto;
    width: auto !important;
  }
  .header-section[_ngcontent-ng-c1061394248] {
    gap: 20px;
  }
}
@media (min-width: 600px) {
  .demographics-content[_ngcontent-ng-c1061394248] {
    flex-direction: row;
  }
  .right-section[_ngcontent-ng-c1061394248] {
    flex-direction: row;
  }
}
/*# sourceMappingURL=/demographics.component.css.map */</style><style ng-app-id="ng">

.pdf-viewer-container[_ngcontent-ng-c1949021116] {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}
.test-controls[_ngcontent-ng-c1949021116] {
  background-color: #ffffff;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}
h2[_ngcontent-ng-c1949021116] {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 20px;
  color: #333;
}
.file-input-container[_ngcontent-ng-c1949021116] {
  margin-bottom: 16px;
}
.file-input-label[_ngcontent-ng-c1949021116] {
  display: inline-block;
  padding: 8px 16px;
  background-color: #2196f3;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}
.file-input-label[_ngcontent-ng-c1949021116]:hover {
  background-color: #1976d2;
}
.file-input[_ngcontent-ng-c1949021116] {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}
.config-controls[_ngcontent-ng-c1949021116] {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
}
.control-group[_ngcontent-ng-c1949021116] {
  display: flex;
  align-items: center;
  gap: 8px;
}
.control-group[_ngcontent-ng-c1949021116]   label[_ngcontent-ng-c1949021116] {
  font-size: 14px;
  color: #333;
}
.control-group[_ngcontent-ng-c1949021116]   button[_ngcontent-ng-c1949021116], 
.control-group[_ngcontent-ng-c1949021116]   select[_ngcontent-ng-c1949021116] {
  padding: 4px 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: #fff;
  color: #17181A;
  font-size: 14px;
  cursor: pointer;
}
.control-group[_ngcontent-ng-c1949021116]   button[_ngcontent-ng-c1949021116]:hover, 
.control-group[_ngcontent-ng-c1949021116]   select[_ngcontent-ng-c1949021116]:hover {
  background-color: #f0f0f0;
}
.debug-info[_ngcontent-ng-c1949021116] {
  background-color: #f0f0f0;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  font-family: monospace;
}
.debug-info[_ngcontent-ng-c1949021116]   h3[_ngcontent-ng-c1949021116] {
  margin-top: 0;
  margin-bottom: 8px;
  font-size: 14px;
}
.debug-info[_ngcontent-ng-c1949021116]   div[_ngcontent-ng-c1949021116] {
  margin-bottom: 4px;
}
.pdf-viewer-wrapper[_ngcontent-ng-c1949021116] {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 500px;
}
[_nghost-ng-c1949021116]     ngx-extended-pdf-viewer {
  display: block;
  width: 100%;
  height: 100%;
}
[_nghost-ng-c1949021116]     .viewer-container {
  overflow: auto !important;
}
[_nghost-ng-c1949021116]     .page {
  margin: 8px auto !important;
}
[_nghost-ng-c1949021116]     .textLayer .highlight {
  background-color: rgba(180, 235, 180, 0.4) !important;
  border-radius: 2px;
  box-shadow: 0 0 1px rgba(0, 0, 0, 0.1);
  mix-blend-mode: multiply;
}
[_nghost-ng-c1949021116]     .textLayer .highlight.selected {
  background-color: rgba(180, 235, 180, 0.6) !important;
}
.no-pdf-message[_ngcontent-ng-c1949021116], 
.ssr-placeholder[_ngcontent-ng-c1949021116] {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-size: 16px;
  color: #666;
  text-align: center;
  padding: 20px;
}
@media (max-width: 768px) {
  .config-controls[_ngcontent-ng-c1949021116] {
    flex-direction: column;
    align-items: flex-start;
  }
  .pdf-viewer-wrapper[_ngcontent-ng-c1949021116] {
    height: calc(100vh - 400px);
  }
}
/*# sourceMappingURL=/pdf-viewer.component.css.map */</style><style ng-app-id="ng">

.hits-container[_ngcontent-ng-c1281433528] {
  padding: 20px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #f1f5f7;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 0px;
  width: 100%;
  box-sizing: border-box;
}
.hits-header[_ngcontent-ng-c1281433528] {
  padding: 0px 0px 12px 0px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  gap: 20px;
  width: 100%;
  box-sizing: border-box;
}
.hits-title[_ngcontent-ng-c1281433528] {
  color: #17181A;
  font-family: Urbane;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 32px;
}
.hits-table[_ngcontent-ng-c1281433528] {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 0px;
  width: 100%;
  box-sizing: border-box;
}
.hits-table-columns[_ngcontent-ng-c1281433528] {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 0px;
  width: 100%;
  box-sizing: border-box;
}
.hits-row[_ngcontent-ng-c1281433528] {
  display: flex;
  align-self: stretch;
}
.hits-row.highlighted[_ngcontent-ng-c1281433528] {
  background: #D9E1E7;
}
.hits-row[_ngcontent-ng-c1281433528]:not(.highlighted) {
  background: #FFFFFF;
}
.hits-column[_ngcontent-ng-c1281433528] {
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  display: inline-flex;
  box-sizing: border-box;
}
.hits-column.dos-column[_ngcontent-ng-c1281433528] {
  width: 80px;
}
.hits-column.sys-column[_ngcontent-ng-c1281433528] {
  width: 60px;
}
.hits-column.dias-column[_ngcontent-ng-c1281433528] {
  width: 60px;
}
.hits-column.page-column[_ngcontent-ng-c1281433528] {
  width: 60px;
}
.hits-column.comment-column[_ngcontent-ng-c1281433528] {
  width: 216px;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  display: inline-flex;
}
.hits-column.include-column[_ngcontent-ng-c1281433528] {
  width: 60px;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  display: inline-flex;
}
.hits-column.include-column[_ngcontent-ng-c1281433528]   .include-column-inner[_ngcontent-ng-c1281433528] {
  width: 60px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  display: flex;
}
.header-item[_ngcontent-ng-c1281433528] {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 10px;
  border-bottom: 1px solid #f1f5f7;
  height: 40px;
  width: 100%;
  box-sizing: border-box;
}
.header-item[_ngcontent-ng-c1281433528]   .table-item[_ngcontent-ng-c1281433528] {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  gap: 12px;
  height: 40px;
  box-sizing: border-box;
}
.header-item[_ngcontent-ng-c1281433528]   .table-item[_ngcontent-ng-c1281433528]   .label[_ngcontent-ng-c1281433528] {
  color: #17181A;
  font-size: 12px;
  font-family: Urbane;
  font-weight: 500;
  line-height: 20px;
  word-wrap: break-word;
}
.comment-column[_ngcontent-ng-c1281433528]   .header-item[_ngcontent-ng-c1281433528] {
  align-items: center;
}
.comment-column[_ngcontent-ng-c1281433528]   .header-item[_ngcontent-ng-c1281433528]   .table-item[_ngcontent-ng-c1281433528] {
  justify-content: center;
  width: 100%;
}
.include-column[_ngcontent-ng-c1281433528]   .header-item[_ngcontent-ng-c1281433528] {
  align-items: center;
}
.include-column[_ngcontent-ng-c1281433528]   .header-item[_ngcontent-ng-c1281433528]   .table-item[_ngcontent-ng-c1281433528] {
  justify-content: center;
  width: 100%;
}
.table-item[_ngcontent-ng-c1281433528] {
  height: 40px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  width: 100%;
  padding: 10px 8px 10px 8px;
}
.table-item.highlighted[_ngcontent-ng-c1281433528] {
  background: #D9E1E7;
}
.table-item[_ngcontent-ng-c1281433528]:not(.highlighted) {
  background: #ffffff;
}
.comment-column[_ngcontent-ng-c1281433528]   .table-item[_ngcontent-ng-c1281433528] {
  padding: 2px 0px 2px 0px;
  justify-content: center;
}
.table-item[_ngcontent-ng-c1281433528]   .icon-text[_ngcontent-ng-c1281433528] {
  justify-content: flex-start;
  align-items: center;
  gap: 12px;
  display: flex;
  width: 100%;
}
.table-item[_ngcontent-ng-c1281433528]   .icon-text[_ngcontent-ng-c1281433528]   .label[_ngcontent-ng-c1281433528] {
  text-box-trim: trim-both;
  text-box-edge: cap alphabetic;
  color: #17181A;
  font-size: 12px;
  font-family: Urbane;
  font-weight: 300;
  line-height: 16px;
  word-wrap: break-word;
}
.dos-column[_ngcontent-ng-c1281433528]   .cell-content[_ngcontent-ng-c1281433528], 
.sys-column[_ngcontent-ng-c1281433528]   .cell-content[_ngcontent-ng-c1281433528], 
.dias-column[_ngcontent-ng-c1281433528]   .cell-content[_ngcontent-ng-c1281433528] {
  color: #17181A;
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  font-weight: 300;
  line-height: 16px;
}
.page-link[_ngcontent-ng-c1281433528] {
  background: none;
  border: none;
  color: #0071BC;
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  font-weight: 300;
  text-decoration: underline;
  line-height: 16px;
  cursor: pointer;
  padding: 0;
}
.page-link[_ngcontent-ng-c1281433528]:hover {
  opacity: 0.8;
}
.page-link[_ngcontent-ng-c1281433528]:focus {
  outline: 2px solid #3870B8;
  outline-offset: 2px;
}
.comment-box-component[_ngcontent-ng-c1281433528] {
  height: 30px;
  width: 100%;
}
.checkbox-wrapper[_ngcontent-ng-c1281433528] {
  width: 60px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.include-checkbox[_ngcontent-ng-c1281433528] {
  width: 16px;
  height: 16px;
  border-radius: 5px;
  border: 1px solid #D9E1E7;
  background-color: #FFFFFF;
  cursor: pointer;
  transition: all 0.2s ease;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  position: relative;
  width: 16px;
  height: 16px;
  margin: 0;
}
.include-checkbox[_ngcontent-ng-c1281433528]:checked {
  background-color: #17181A;
  border-color: #17181A;
}
.include-checkbox[_ngcontent-ng-c1281433528]:checked::after {
  content: "";
  position: absolute;
  left: 4px;
  top: 4px;
  width: 8.33px;
  height: 7.5px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8.33' height='7.5' viewBox='0 0 8.33 7.5'%3E%3Cpath d='M1 3.75L3.5 6.25L7.33 1.25' stroke='white' stroke-width='1.5' fill='none' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
.include-checkbox[_ngcontent-ng-c1281433528]:hover:not(:disabled) {
  border-color: #547996;
}
.include-checkbox[_ngcontent-ng-c1281433528]:focus {
  outline: 2px solid #3870B8;
  outline-offset: 2px;
}
.include-checkbox[_ngcontent-ng-c1281433528]:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.checkbox-label[_ngcontent-ng-c1281433528] {
  display: none;
}
@media (max-width: 599px) {
  .hits-container[_ngcontent-ng-c1281433528] {
    padding: 16px;
  }
  .hits-table[_ngcontent-ng-c1281433528] {
    overflow-x: auto;
  }
  .hits-column[_ngcontent-ng-c1281433528] {
    min-width: 60px;
    padding: 0;
  }
  .hits-column.dos-column[_ngcontent-ng-c1281433528] {
    width: 70px;
  }
  .hits-column.sys-column[_ngcontent-ng-c1281433528] {
    width: 50px;
  }
  .hits-column.dias-column[_ngcontent-ng-c1281433528] {
    width: 50px;
  }
  .hits-column.page-column[_ngcontent-ng-c1281433528] {
    width: 50px;
  }
  .hits-column.comment-column[_ngcontent-ng-c1281433528] {
    width: 180px;
  }
}
@media (min-width: 600px) {
  .hits-container[_ngcontent-ng-c1281433528] {
    padding: 20px;
  }
}
/*# sourceMappingURL=/hits.component.css.map */</style><style ng-app-id="ng">

.results-container[_ngcontent-ng-c2018085561] {
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: stretch;
  gap: 0px;
  border-radius: 8px;
  border: 1px solid #f1f5f7;
  background: #ffffff;
  width: 100%;
  box-sizing: border-box;
}
.results-container.disabled[_ngcontent-ng-c2018085561] {
  opacity: 0.6;
  pointer-events: none;
}
.results-container.has-error[_ngcontent-ng-c2018085561] {
  border-color: #F4454E;
}
.results-header[_ngcontent-ng-c2018085561] {
  padding: 0px 0px 12px 0px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  gap: 20px;
  width: 100%;
  box-sizing: border-box;
}
.results-title-section[_ngcontent-ng-c2018085561] {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 12px;
  width: 100%;
  box-sizing: border-box;
}
.results-title[_ngcontent-ng-c2018085561] {
  color: #17181A;
  font-family: Urbane;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 32px;
  text-align: left;
  text-wrap: nowrap;
  margin: 0;
}
.tab-navigation[_ngcontent-ng-c2018085561] {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  gap: 0px;
  border-bottom: 1px solid #d9e1e7;
  background: #ffffff;
  width: 100%;
  box-sizing: content-box;
}
.tab-button[_ngcontent-ng-c2018085561] {
  padding: 4px 8px 4px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  gap: 12px;
  height: 100%;
  background: transparent;
  border: none;
  border-bottom: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  box-sizing: border-box;
  color: #547996;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}
.tab-button[_ngcontent-ng-c2018085561]:hover:not(:disabled) {
  color: #17181a;
}
.tab-button.active[_ngcontent-ng-c2018085561] {
  color: #1976d2;
  border-bottom: 1px solid #1976d2;
}
.tab-button[_ngcontent-ng-c2018085561]:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}
.tab-content[_ngcontent-ng-c2018085561] {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  gap: 12px;
  width: 100%;
  box-sizing: border-box;
}
.error-message[_ngcontent-ng-c2018085561] {
  color: #F4454E;
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  font-weight: 300;
  line-height: 16px;
  margin-top: 4px;
}
@media (max-width: 768px) {
  .results-container[_ngcontent-ng-c2018085561] {
    padding: 16px;
  }
  .tab-navigation[_ngcontent-ng-c2018085561] {
    flex-wrap: wrap;
    gap: 8px;
  }
  .tab-button[_ngcontent-ng-c2018085561] {
    font-size: 12px;
    padding: 2px 6px;
  }
}
/*# sourceMappingURL=/results.component.css.map */</style><style ng-app-id="ng">

@font-face {
  font-family: "Urbane";
  src: url("./media/Urbane-Light.woff2") format("woff2"), url("./media/Urbane-Light.woff") format("woff");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Urbane";
  src: url("./media/Urbane-Medium.woff2") format("woff2"), url("./media/Urbane-Medium.woff") format("woff");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Urbane";
  src: url("./media/Urbane-Medium.woff2") format("woff2"), url("./media/Urbane-Medium.woff") format("woff");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Urbane";
  src: url("./media/Urbane-DemiBold.woff2") format("woff2"), url("./media/Urbane-DemiBold.woff") format("woff");
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}
.text-xs[_ngcontent-ng-c502759576] {
  font-size: 10px;
  font-family: "Urbane", sans-serif;
  line-height: 20px;
}
.text-sm[_ngcontent-ng-c502759576] {
  font-size: 11px;
  font-family: "Urbane", sans-serif;
  line-height: 20px;
}
.text-base[_ngcontent-ng-c502759576] {
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  line-height: 20px;
}
.text-md[_ngcontent-ng-c502759576] {
  font-size: 14px;
  font-family: "Urbane", sans-serif;
  line-height: 20px;
}
.text-lg[_ngcontent-ng-c502759576] {
  font-size: 16px;
  font-family: "Urbane", sans-serif;
  line-height: 20px;
}
.text-xl[_ngcontent-ng-c502759576] {
  font-size: 20px;
  font-family: "Urbane", sans-serif;
  line-height: 32px;
}
.text-xxl[_ngcontent-ng-c502759576] {
  font-size: 24px;
  font-family: "Urbane", sans-serif;
  line-height: 32px;
}
.font-light[_ngcontent-ng-c502759576] {
  font-weight: 300;
}
.font-regular[_ngcontent-ng-c502759576] {
  font-weight: 400;
}
.font-medium[_ngcontent-ng-c502759576] {
  font-weight: 500;
}
.font-semibold[_ngcontent-ng-c502759576] {
  font-weight: 600;
}
.label-text[_ngcontent-ng-c502759576] {
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  line-height: 20px;
  font-weight: 300;
  color: #17181A;
}
.link-text[_ngcontent-ng-c502759576] {
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  line-height: 20px;
  font-weight: 500;
  color: #0071BC;
}
.heading-text[_ngcontent-ng-c502759576] {
  font-size: 20px;
  font-family: "Urbane", sans-serif;
  line-height: 32px;
  font-weight: 600;
  color: #17181A;
}
.subheading-text[_ngcontent-ng-c502759576] {
  font-size: 14px;
  font-family: "Urbane", sans-serif;
  line-height: 20px;
  font-weight: 600;
  color: #17181A;
}
.caption-text[_ngcontent-ng-c502759576] {
  font-size: 10px;
  font-family: "Urbane", sans-serif;
  line-height: 20px;
  font-weight: 500;
  color: #547996;
}
body[_ngcontent-ng-c502759576] {
  font-family: "Urbane", sans-serif;
  font-size: 12px;
  line-height: 20px;
  color: #17181A;
}
.button[_ngcontent-ng-c502759576] {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border-radius: 8px;
  font-family: "Urbane", sans-serif;
  font-size: 12px;
  font-weight: 500;
  line-height: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  outline: none;
}
.button[_ngcontent-ng-c502759576]:focus {
  outline: none;
}
.button.button-with-icon[_ngcontent-ng-c502759576] {
  gap: 8px;
}
.button[_ngcontent-ng-c502759576]   .button-icon[_ngcontent-ng-c502759576] {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}
.button[_ngcontent-ng-c502759576]   app-refresh-icon[_ngcontent-ng-c502759576] {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  vertical-align: middle;
  flex-shrink: 0;
}
.button.btn-icon-right[_ngcontent-ng-c502759576] {
  flex-direction: row-reverse;
}
.button-primary[_ngcontent-ng-c502759576] {
  background-color: #3870B8;
  color: #FFFFFF;
  padding: 8px 16px;
}
.button-primary.figma-exact[_ngcontent-ng-c502759576] {
  padding: 10px 16px;
}
.button-primary.figma-state-inactive[_ngcontent-ng-c502759576] {
  background-color: #BFD0EE;
  color: #FFFFFF;
  cursor: not-allowed;
}
.button-primary.figma-state-inactive[_ngcontent-ng-c502759576]:hover {
  background-color: #BFD0EE;
}
.button-primary.figma-state-default[_ngcontent-ng-c502759576] {
  background-color: #3870B8;
  color: #FFFFFF;
}
.button-primary.figma-state-hover[_ngcontent-ng-c502759576] {
  background-color: #468CE7;
  color: #FFFFFF;
}
.button-primary.figma-state-click[_ngcontent-ng-c502759576] {
  background-color: #285082;
  color: #FFFFFF;
}
.button-primary[_ngcontent-ng-c502759576]:not(.figma-state-inactive):not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click):hover {
  background-color: #468CE7;
}
.button-primary[_ngcontent-ng-c502759576]:not(.figma-state-inactive):not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click):active {
  background-color: #285082;
}
.button-primary.figma-exact[_ngcontent-ng-c502759576]:not(.figma-state-inactive):not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click):hover {
  background-color: #468CE7;
}
.button-primary.figma-exact[_ngcontent-ng-c502759576]:not(.figma-state-inactive):not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click):active {
  background-color: #285082;
}
.button-primary.button-disabled[_ngcontent-ng-c502759576] {
  background-color: #BFD0EE;
  color: #FFFFFF;
  cursor: not-allowed;
}
.button-secondary[_ngcontent-ng-c502759576] {
  background-color: #FFFFFF;
  color: #17181A;
  border: 1px solid #D9E1E7;
  padding: 8px 12px;
}
.button-secondary.figma-exact[_ngcontent-ng-c502759576] {
  padding: 8px 14px;
}
.button-secondary.figma-state-default[_ngcontent-ng-c502759576] {
  background-color: #FFFFFF;
  color: #17181A;
  border: 1px solid #D9E1E7;
}
.button-secondary.figma-state-default[_ngcontent-ng-c502759576]   app-refresh-icon[_ngcontent-ng-c502759576] {
  color: #17181A;
}
.button-secondary.figma-state-hover[_ngcontent-ng-c502759576] {
  background-color: #F1F5F7;
  color: #17181A;
  border: 1px solid #D9E1E7;
}
.button-secondary.figma-state-hover[_ngcontent-ng-c502759576]   app-refresh-icon[_ngcontent-ng-c502759576] {
  color: #17181A;
}
.button-secondary.figma-state-click[_ngcontent-ng-c502759576] {
  background-color: #17181A;
  color: #FFFFFF;
  border: 1px solid #D9E1E7;
}
.button-secondary.figma-state-click[_ngcontent-ng-c502759576]   app-refresh-icon[_ngcontent-ng-c502759576] {
  color: #FFFFFF;
}
.button-secondary[_ngcontent-ng-c502759576]:not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click):hover {
  background-color: #F1F5F7;
}
.button-secondary[_ngcontent-ng-c502759576]:not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click):active {
  background-color: #17181A;
  color: #FFFFFF;
}
.button-secondary.figma-exact[_ngcontent-ng-c502759576]:not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click)   app-refresh-icon[_ngcontent-ng-c502759576] {
  color: #17181A;
}
.button-secondary.figma-exact[_ngcontent-ng-c502759576]:not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click):hover {
  background-color: #F1F5F7;
}
.button-secondary.figma-exact[_ngcontent-ng-c502759576]:not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click):hover   app-refresh-icon[_ngcontent-ng-c502759576] {
  color: #17181A;
}
.button-secondary.figma-exact[_ngcontent-ng-c502759576]:not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click):active {
  background-color: #17181A;
  color: #FFFFFF;
}
.button-secondary.figma-exact[_ngcontent-ng-c502759576]:not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click):active   app-refresh-icon[_ngcontent-ng-c502759576] {
  color: #FFFFFF;
}
.button-secondary.button-disabled[_ngcontent-ng-c502759576] {
  color: #547996;
  border-color: #F1F5F7;
  cursor: not-allowed;
}
.button-tertiary[_ngcontent-ng-c502759576] {
  background-color: transparent;
  color: #3870B8;
  box-shadow: none;
}
.button-tertiary[_ngcontent-ng-c502759576]:hover {
  background-color: rgba(56, 112, 184, 0.05);
}
.button-tertiary[_ngcontent-ng-c502759576]:active {
  background-color: rgba(56, 112, 184, 0.1);
}
.button-tertiary.button-disabled[_ngcontent-ng-c502759576] {
  color: #547996;
  cursor: not-allowed;
}
.button-sm[_ngcontent-ng-c502759576] {
  padding: 8px 12px;
  font-size: 11px;
}
.button-lg[_ngcontent-ng-c502759576] {
  padding: 16px 20px;
  font-size: 14px;
}
.button-block[_ngcontent-ng-c502759576] {
  width: 100%;
  display: flex;
}
/*# sourceMappingURL=/button.component.css.map */</style><style ng-app-id="ng">

.comment-box[_ngcontent-ng-c62406540] {
  flex: 1 1 0;
  height: 30px;
  padding: 4px;
  background: white !important;
  overflow: hidden;
  border-radius: 10px;
  outline-offset: -1px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  display: inline-flex;
  box-sizing: border-box;
}
.comment-box.default[_ngcontent-ng-c62406540] {
  outline: 1px #D9E1E7 solid;
}
.comment-box.active[_ngcontent-ng-c62406540] {
  outline: 1px #547996 solid;
}
.comment-box.entered[_ngcontent-ng-c62406540] {
  outline: 1px #D9E1E7 solid;
}
.comment-box.disabled[_ngcontent-ng-c62406540] {
  background-color: #F1F5F7;
  cursor: not-allowed;
}
.row[_ngcontent-ng-c62406540] {
  align-self: stretch;
  padding-left: 12px;
  padding-right: 12px;
  background: transparent;
  border-radius: 6px;
  justify-content: flex-start;
  align-items: center;
  gap: 12px;
  display: inline-flex;
}
.comment-input[_ngcontent-ng-c62406540] {
  flex: 1 1 0;
  border: none !important;
  outline: none !important;
  background: transparent !important;
  color: #17181A !important;
  font-size: 10px;
  font-family: "Urbane", sans-serif;
  font-weight: 300;
  line-height: 12px;
  word-wrap: break-word;
  padding: 0;
  width: 100%;
  height: 100%;
}
.comment-input[_ngcontent-ng-c62406540]::placeholder {
  color: #547996;
}
.active[_ngcontent-ng-c62406540]   .comment-input[_ngcontent-ng-c62406540], 
.entered[_ngcontent-ng-c62406540]   .comment-input[_ngcontent-ng-c62406540] {
  color: #17181A;
}
.default[_ngcontent-ng-c62406540]   .comment-input[_ngcontent-ng-c62406540] {
  color: #547996;
}
.comment-input[_ngcontent-ng-c62406540]:disabled {
  cursor: not-allowed;
  color: #547996;
}
.comment-input[_ngcontent-ng-c62406540]:focus {
  outline: none;
}
/*# sourceMappingURL=/comment-box.component.css.map */</style><style ng-app-id="ng">

.checkbox-container[_ngcontent-ng-c2693716353] {
  display: flex;
  align-items: center;
  gap: 8px;
}
.checkbox[_ngcontent-ng-c2693716353] {
  width: 16px;
  height: 16px;
  border-radius: 5px;
  border: 1px solid #D9E1E7;
  background-color: #FFFFFF;
  cursor: pointer;
  transition: all 0.2s ease;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  position: relative;
  margin: 0;
  cursor: pointer;
}
.checkbox[_ngcontent-ng-c2693716353]:checked {
  background-color: #17181A;
  border-color: #17181A;
}
.checkbox[_ngcontent-ng-c2693716353]:checked::after {
  content: "";
  position: absolute;
  left: 4px;
  top: 4px;
  width: 8.33px;
  height: 7.5px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8.33' height='7.5' viewBox='0 0 8.33 7.5'%3E%3Cpath d='M1 3.75L3.5 6.25L7.33 1.25' stroke='white' stroke-width='1.5' fill='none' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
.checkbox[_ngcontent-ng-c2693716353]:hover:not(:disabled) {
  border-color: #547996;
}
.checkbox[_ngcontent-ng-c2693716353]:focus {
  outline: 2px solid #3870B8;
  outline-offset: 2px;
}
.checkbox[_ngcontent-ng-c2693716353]:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.checkbox[_ngcontent-ng-c2693716353]:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.checkbox-label[_ngcontent-ng-c2693716353] {
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  font-weight: 300;
  color: #17181A;
  cursor: pointer;
}
/*# sourceMappingURL=/checkbox.component.css.map */</style><style ng-app-id="ng">

.inclusions-tab-content[_ngcontent-ng-c1722032080] {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 12px;
  box-sizing: border-box;
  width: 100%;
}
.frame-942[_ngcontent-ng-c1722032080] {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 8px;
  box-sizing: border-box;
}
.frame-936[_ngcontent-ng-c1722032080] {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 100%;
}
.frame-939[_ngcontent-ng-c1722032080] {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 100%;
}
.frame-940[_ngcontent-ng-c1722032080] {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 127px;
}
.frame-941[_ngcontent-ng-c1722032080] {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 127px;
}
.frame-937[_ngcontent-ng-c1722032080] {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 215px;
}
.frame-938[_ngcontent-ng-c1722032080] {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 100%;
}
.text-sys[_ngcontent-ng-c1722032080], 
.text-dias[_ngcontent-ng-c1722032080], 
.text-date-of-service[_ngcontent-ng-c1722032080], 
.text-notes[_ngcontent-ng-c1722032080] {
  color: #547996;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}
.dropdown-inclusion[_ngcontent-ng-c1722032080] {
  padding: 12px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 10px;
  border-color: #D9E1E7;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  height: 48px;
  width: 100%;
  outline: none;
  color: #547996;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  font-weight: 300;
  text-align: left;
}
.dropdown-inclusion[_ngcontent-ng-c1722032080]::placeholder {
  color: #547996;
}
.dropdown-inclusion[_ngcontent-ng-c1722032080]:focus {
  border-color: #547996;
}
.dropdown-inclusion[_ngcontent-ng-c1722032080]:disabled {
  background-color: #F1F5F7;
  cursor: not-allowed;
  color: #547996;
}
.calendar[_ngcontent-ng-c1722032080] {
  height: 48px;
  width: 100%;
}
.notes[_ngcontent-ng-c1722032080] {
  width: 100%;
}
@media (max-width: 768px) {
  .frame-939[_ngcontent-ng-c1722032080] {
    flex-direction: column;
    gap: 8px;
  }
  .frame-940[_ngcontent-ng-c1722032080], 
   .frame-941[_ngcontent-ng-c1722032080], 
   .frame-937[_ngcontent-ng-c1722032080] {
    width: 100%;
  }
}
/*# sourceMappingURL=/inclusions-tab.component.css.map */</style><style ng-app-id="ng">

.calendar-container[_ngcontent-ng-c1227210501] {
  position: relative;
  flex: 1 1 0;
  min-width: 0;
  box-sizing: border-box;
  width: 100%;
  max-width: 100%;
}
.calendar-container.disabled[_ngcontent-ng-c1227210501] {
  opacity: 0.5;
  pointer-events: none;
}
.calendar-label[_ngcontent-ng-c1227210501] {
  display: block;
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  font-weight: 300;
  color: #17181A;
  margin-bottom: 4px;
}
.calendar-label[_ngcontent-ng-c1227210501]   .required-indicator[_ngcontent-ng-c1227210501] {
  color: #3870B8;
  margin-left: 2px;
}
.date-input-container[_ngcontent-ng-c1227210501] {
  padding: 4px;
  overflow: hidden;
  border-radius: 10px;
  border: 1px solid #d9e1e7;
  background: #ffffff;
  height: 48px;
  width: 100%;
  flex: 1 1 0;
  min-width: 0;
  max-width: 100%;
  box-sizing: border-box;
  position: relative;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: stretch;
}
.date-input-container[_ngcontent-ng-c1227210501]:hover:not(.disabled) {
  border-color: #547996;
}
.date-input-container.focused[_ngcontent-ng-c1227210501] {
  border-color: #547996;
}
.date-input-container.has-error[_ngcontent-ng-c1227210501] {
  border-color: #F4454E;
}
.date-input-container.disabled[_ngcontent-ng-c1227210501] {
  background-color: #f1f5f7;
  cursor: not-allowed;
}
.date-input-content[_ngcontent-ng-c1227210501] {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  gap: 12px;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
  box-sizing: border-box;
}
.date-input[_ngcontent-ng-c1227210501] {
  flex: 1;
  color: #547996;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  font-weight: 300;
  text-align: left;
  border: none;
  outline: none;
  background: transparent;
}
.date-input[_ngcontent-ng-c1227210501]::placeholder {
  color: #547996;
}
.date-input[_ngcontent-ng-c1227210501]:disabled {
  cursor: not-allowed;
  color: #547996;
}
.date-input[_ngcontent-ng-c1227210501]:focus {
  color: #17181a;
}
.date-input-container.has-value[_ngcontent-ng-c1227210501]   .date-input[_ngcontent-ng-c1227210501] {
  color: #17181a;
}
.calendar-icon-button[_ngcontent-ng-c1227210501] {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  cursor: pointer;
  padding: 0;
  transition: color 0.2s ease;
  flex-shrink: 0;
}
.calendar-icon-button[_ngcontent-ng-c1227210501]:hover:not(:disabled) {
  color: #17181a;
}
.calendar-icon-button[_ngcontent-ng-c1227210501]:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}
.calendar-icon[_ngcontent-ng-c1227210501] {
  width: 16px;
  height: 18px;
  color: #547996;
  transition: color 0.2s ease;
}
.date-input-container.focused[_ngcontent-ng-c1227210501]   .calendar-icon[_ngcontent-ng-c1227210501], 
.date-input-container.has-value[_ngcontent-ng-c1227210501]   .calendar-icon[_ngcontent-ng-c1227210501] {
  color: #17181a;
}
.calendar-popup[_ngcontent-ng-c1227210501] {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #FFFFFF;
  border-radius: 10px;
  border: 1px solid #547996;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  margin-top: 4px;
  padding: 16px;
  min-width: 280px;
}
.calendar-header[_ngcontent-ng-c1227210501] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}
.nav-button[_ngcontent-ng-c1227210501] {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  color: #547996;
  transition: all 0.2s ease;
}
.nav-button[_ngcontent-ng-c1227210501]:hover {
  background-color: #F1F5F7;
  color: #17181A;
}
.nav-button[_ngcontent-ng-c1227210501]   svg[_ngcontent-ng-c1227210501] {
  width: 8px;
  height: 12px;
}
.month-year[_ngcontent-ng-c1227210501] {
  font-size: 14px;
  font-family: "Urbane", sans-serif;
  font-weight: 600;
  color: #17181A;
  text-align: center;
  flex: 1;
}
.days-header[_ngcontent-ng-c1227210501] {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 4px;
  margin-bottom: 8px;
}
.day-header[_ngcontent-ng-c1227210501] {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  font-size: 11px;
  font-family: "Urbane", sans-serif;
  font-weight: 600;
  color: #547996;
  text-transform: uppercase;
}
.calendar-days[_ngcontent-ng-c1227210501] {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 4px;
}
.calendar-day[_ngcontent-ng-c1227210501] {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  font-weight: 300;
  color: #17181A;
  transition: all 0.2s ease;
}
.calendar-day[_ngcontent-ng-c1227210501]:hover:not(:disabled):not(.empty) {
  background-color: #F1F5F7;
}
.calendar-day.selected[_ngcontent-ng-c1227210501] {
  background-color: #3870B8;
  color: #FFFFFF;
}
.calendar-day.selected[_ngcontent-ng-c1227210501]:hover {
  background-color: rgb(44.1, 88.2, 144.9);
}
.calendar-day.today[_ngcontent-ng-c1227210501] {
  font-weight: 600;
  color: #3870B8;
}
.calendar-day.today[_ngcontent-ng-c1227210501]:not(.selected) {
  background-color: rgba(56, 112, 184, 0.1);
}
.calendar-day.empty[_ngcontent-ng-c1227210501] {
  cursor: default;
  visibility: hidden;
}
.calendar-day[_ngcontent-ng-c1227210501]:disabled {
  cursor: not-allowed;
  opacity: 0.3;
}
.error-message[_ngcontent-ng-c1227210501] {
  margin-top: 4px;
  font-size: 11px;
  font-family: "Urbane", sans-serif;
  font-weight: 300;
  color: #F4454E;
}
@media (max-width: 599px) {
  .calendar-popup[_ngcontent-ng-c1227210501] {
    min-width: 260px;
    padding: 12px;
  }
  .calendar-day[_ngcontent-ng-c1227210501] {
    width: 28px;
    height: 28px;
    font-size: 11px;
  }
  .day-header[_ngcontent-ng-c1227210501] {
    height: 28px;
    font-size: 10px;
  }
}
[_nghost-ng-c1227210501] {
  display: block;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}
.calendar-container[_ngcontent-ng-c1227210501] {
  width: 100%;
  box-sizing: border-box;
}
.date-input-container[_ngcontent-ng-c1227210501]   input[_ngcontent-ng-c1227210501] {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}
/*# sourceMappingURL=/calendar.component.css.map */</style><style ng-app-id="ng">

.notes-container[_ngcontent-ng-c3668244963] {
  position: relative;
  width: 100%;
}
.notes-container.disabled[_ngcontent-ng-c3668244963] {
  opacity: 0.5;
  pointer-events: none;
}
.notes-label[_ngcontent-ng-c3668244963] {
  display: block;
  font-size: 10px;
  font-family: "Urbane", sans-serif;
  font-weight: 300;
  color: #547996;
  margin-bottom: 4px;
}
.notes-label[_ngcontent-ng-c3668244963]   .required-indicator[_ngcontent-ng-c3668244963] {
  color: #3870B8;
  margin-left: 2px;
}
.notes-input-wrapper[_ngcontent-ng-c3668244963] {
  position: relative;
  width: 100%;
  box-sizing: border-box;
}
.notes-textarea[_ngcontent-ng-c3668244963] {
  width: 100%;
  min-height: 120px;
  padding: 12px;
  border: 1px solid #D9E1E7;
  border-radius: 8px;
  background: #ffffff;
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  font-weight: 300;
  line-height: 16px;
  color: #547996;
  resize: vertical;
  box-sizing: border-box;
  outline: none;
  transition: all 0.2s ease;
}
.notes-textarea[_ngcontent-ng-c3668244963]::placeholder {
  color: #547996;
}
.notes-textarea[_ngcontent-ng-c3668244963]:focus {
  border-color: #547996;
  color: #17181A;
}
.notes-textarea.has-value[_ngcontent-ng-c3668244963] {
  color: #17181A;
}
.notes-textarea[_ngcontent-ng-c3668244963]:disabled {
  background-color: #F1F5F7;
  cursor: not-allowed;
  color: #547996;
}
.notes-textarea[_ngcontent-ng-c3668244963]:hover:not(:disabled) {
  border-color: #547996;
}
.notes-textarea[_ngcontent-ng-c3668244963]::-webkit-scrollbar {
  width: 6px;
}
.notes-textarea[_ngcontent-ng-c3668244963]::-webkit-scrollbar-track {
  background: transparent;
}
.notes-textarea[_ngcontent-ng-c3668244963]::-webkit-scrollbar-thumb {
  background: #D9E1E7;
  border-radius: 3px;
}
.notes-textarea[_ngcontent-ng-c3668244963]::-webkit-scrollbar-thumb:hover {
  background: #547996;
}
.character-counter[_ngcontent-ng-c3668244963] {
  position: absolute;
  bottom: 8px;
  right: 12px;
  font-size: 10px;
  font-family: "Urbane", sans-serif;
  font-weight: 300;
  line-height: 20px;
  color: #547996;
  background: rgba(255, 255, 255, 0.9);
  padding: 2px 4px;
  border-radius: 4px;
  pointer-events: none;
}
.character-counter.near-limit[_ngcontent-ng-c3668244963] {
  color: #F4A261;
}
.character-counter.over-limit[_ngcontent-ng-c3668244963] {
  color: #F4454E;
}
.error-message[_ngcontent-ng-c3668244963] {
  margin-top: 4px;
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  font-weight: 300;
  color: #F4454E;
  line-height: 16px;
}
@media (max-width: 599px) {
  .notes-textarea[_ngcontent-ng-c3668244963] {
    min-height: 100px;
    padding: 10px;
  }
  .character-counter[_ngcontent-ng-c3668244963] {
    font-size: 9px;
    bottom: 6px;
    right: 10px;
  }
}
@media (min-width: 600px) {
  .notes-textarea[_ngcontent-ng-c3668244963] {
    min-height: 120px;
    padding: 12px;
  }
  .character-counter[_ngcontent-ng-c3668244963] {
    font-size: 10px;
    bottom: 8px;
    right: 12px;
  }
}
/*# sourceMappingURL=/notes.component.css.map */</style></head>
<body class="mat-typography"><!--nghm--><script type="text/javascript" id="ng-event-dispatch-contract">(()=>{function p(t,n,r,o,e,i,f,m){return{eventType:t,event:n,targetElement:r,eic:o,timeStamp:e,eia:i,eirp:f,eiack:m}}function u(t){let n=[],r=e=>{n.push(e)};return{c:t,q:n,et:[],etc:[],d:r,h:e=>{r(p(e.type,e,e.target,t,Date.now()))}}}function s(t,n,r){for(let o=0;o<n.length;o++){let e=n[o];(r?t.etc:t.et).push(e),t.c.addEventListener(e,t.h,r)}}function c(t,n,r,o,e=window){let i=u(t);e._ejsas||(e._ejsas={}),e._ejsas[n]=i,s(i,r),s(i,o,!0)}window.__jsaction_bootstrap=c;})();
</script><script>window.__jsaction_bootstrap(document.body,"ng",["click","input","change","submit","compositionstart","compositionend","mousedown","mouseup"],["focus","blur"]);</script>
  <app-root ng-version="19.2.9" ngh="12" ng-server-context="ssg"><router-outlet></router-outlet><app-chart-review-page _nghost-ng-c2414578827="" ngh="11"><div _ngcontent-ng-c2414578827="" class="container"><app-menu _ngcontent-ng-c2414578827="" logosrc="assets/logos/Stellarus_logo_2C_blacktype.png?v=2024" logoalt="Stellarus Logo" _nghost-ng-c3806090911="" ng-reflect-logo-src="assets/logos/Stellarus_logo_2C" ng-reflect-logo-alt="Stellarus Logo" ng-reflect-user="[object Object]" ng-reflect-menu-items="[object Object],[object Object" ngh="0"><nav _ngcontent-ng-c3806090911="" class="menu-container"><div _ngcontent-ng-c3806090911="" class="menu-content"><div _ngcontent-ng-c3806090911="" class="logo-section"><div _ngcontent-ng-c3806090911="" class="logo-container" jsaction="click:;"><img _ngcontent-ng-c3806090911="" class="logo-image" src="assets/logos/Stellarus_logo_2C_blacktype.png?v=2024" alt="Stellarus Logo"><!--bindings={
  "ng-reflect-ng-if": "assets/logos/Stellarus_logo_2C"
}--><!--bindings={
  "ng-reflect-ng-if": "false"
}--></div></div><div _ngcontent-ng-c3806090911="" class="user-section"><div _ngcontent-ng-c3806090911="" class="user-info"><span _ngcontent-ng-c3806090911="" class="user-name">Jane Chu</span><div _ngcontent-ng-c3806090911="" class="user-avatar" jsaction="click:;"><div _ngcontent-ng-c3806090911="" class="avatar-circle"><svg _ngcontent-ng-c3806090911="" viewBox="0 0 20 20" fill="none" class="user-icon"><circle _ngcontent-ng-c3806090911="" cx="10" cy="7.5" r="2.75" stroke="currentColor" stroke-width="1.5" fill="none"></circle><path _ngcontent-ng-c3806090911="" d="M4.5 16.5c0-3 2.5-5.5 5.5-5.5s5.5 2.5 5.5 5.5" stroke="currentColor" stroke-width="1.5" fill="none"></path><circle _ngcontent-ng-c3806090911="" cx="10" cy="10" r="8.33" stroke="currentColor" stroke-width="1.5" fill="none"></circle></svg></div></div><div _ngcontent-ng-c3806090911="" class="dropdown-arrow" jsaction="click:;"><svg _ngcontent-ng-c3806090911="" viewBox="0 0 24 24" fill="none" class="arrow-icon"><path _ngcontent-ng-c3806090911="" d="M8 10l4 4 4-4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></div><!--bindings={
  "ng-reflect-ng-if": "true"
}--></div><!--bindings={
  "ng-reflect-ng-if": "false"
}--></div><!--bindings={
  "ng-reflect-ng-if": "[object Object]"
}--></div></nav></app-menu><div _ngcontent-ng-c2414578827="" class="main-content"><div _ngcontent-ng-c2414578827="" class="demographics-section"><app-demographics _ngcontent-ng-c2414578827="" backbuttontext="Back" _nghost-ng-c1061394248="" ng-reflect-back-button-text="Back" ng-reflect-data="[object Object]" ng-reflect-show-back-button="true" ngh="1"><div _ngcontent-ng-c1061394248="" class="demographics-container"><div _ngcontent-ng-c1061394248="" class="demographics-content"><div _ngcontent-ng-c1061394248="" class="left-section"><div _ngcontent-ng-c1061394248="" class="header-section"><div _ngcontent-ng-c1061394248="" class="back-button" jsaction="click:;"><div _ngcontent-ng-c1061394248="" class="back-icon"><svg _ngcontent-ng-c1061394248="" width="12" height="10" viewBox="0 0 12 10" fill="none" xmlns="http://www.w3.org/2000/svg"><path _ngcontent-ng-c1061394248="" d="M4.5 1L1 4.5L4.5 8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path _ngcontent-ng-c1061394248="" d="M1 4.5H11" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></div><span _ngcontent-ng-c1061394248="" class="back-text">Back</span></div><!--bindings={
  "ng-reflect-ng-if": "true"
}--><div _ngcontent-ng-c1061394248="" class="measure-info"><div _ngcontent-ng-c1061394248="" class="measure-title">Controlling Blood Pressure (CBP)</div><div _ngcontent-ng-c1061394248="" class="measure-subtitle">Measure</div></div></div></div><div _ngcontent-ng-c1061394248="" class="right-section"><div _ngcontent-ng-c1061394248="" class="demographics-group primary-group"><div _ngcontent-ng-c1061394248="" class="demographic-item"><div _ngcontent-ng-c1061394248="" class="demographic-value">pdf-test</div><div _ngcontent-ng-c1061394248="" class="demographic-label">Member ID</div></div><div _ngcontent-ng-c1061394248="" class="demographic-item"><div _ngcontent-ng-c1061394248="" class="demographic-value">Sample Patient</div><div _ngcontent-ng-c1061394248="" class="demographic-label">Member</div></div><div _ngcontent-ng-c1061394248="" class="demographic-item"><div _ngcontent-ng-c1061394248="" class="demographic-value">01/01/1970</div><div _ngcontent-ng-c1061394248="" class="demographic-label">DOB</div></div><div _ngcontent-ng-c1061394248="" class="demographic-item"><div _ngcontent-ng-c1061394248="" class="demographic-value">U</div><div _ngcontent-ng-c1061394248="" class="demographic-label">Gender</div></div><div _ngcontent-ng-c1061394248="" class="demographic-item"><div _ngcontent-ng-c1061394248="" class="demographic-value">HMO</div><div _ngcontent-ng-c1061394248="" class="demographic-label">LOB</div></div></div><div _ngcontent-ng-c1061394248="" class="demographics-group provider-group"><div _ngcontent-ng-c1061394248="" class="demographic-item"><div _ngcontent-ng-c1061394248="" class="demographic-value">Dr. Sample Provider</div><div _ngcontent-ng-c1061394248="" class="demographic-label">Provider</div></div><div _ngcontent-ng-c1061394248="" class="demographic-item"><div _ngcontent-ng-c1061394248="" class="demographic-value">123456789</div><div _ngcontent-ng-c1061394248="" class="demographic-label">NPI</div></div></div></div></div></div></app-demographics></div><div _ngcontent-ng-c2414578827="" class="content-layout"><div _ngcontent-ng-c2414578827="" class="pdf-column"><app-pdf-viewer _ngcontent-ng-c2414578827="" _nghost-ng-c1949021116="" ngh="2"><div _ngcontent-ng-c1949021116="" class="pdf-viewer-container"><div _ngcontent-ng-c1949021116="" class="pdf-viewer-wrapper"><!--bindings={
  "ng-reflect-ng-if": "false"
}--><!--bindings={
  "ng-reflect-ng-if": "false"
}--><div _ngcontent-ng-c1949021116="" class="ssr-placeholder"> PDF Viewer is not available during server-side rendering. </div><!--bindings={
  "ng-reflect-ng-if": "true"
}--></div></div></app-pdf-viewer></div><div _ngcontent-ng-c2414578827="" class="right-column"><div _ngcontent-ng-c2414578827="" class="hits-section"><app-hits _ngcontent-ng-c2414578827="" title="Hits" _nghost-ng-c1281433528="" ng-reflect-title="Hits" ng-reflect-data="[object Object],[object Object" ngh="5"><div _ngcontent-ng-c1281433528="" class="hits-container"><div _ngcontent-ng-c1281433528="" class="hits-header"><div _ngcontent-ng-c1281433528="" class="hits-title">Hits</div></div><!--bindings={
  "ng-reflect-ng-if": "true"
}--><div _ngcontent-ng-c1281433528="" class="hits-table"><div _ngcontent-ng-c1281433528="" class="hits-table-columns"><div _ngcontent-ng-c1281433528="" class="hits-column dos-column"><div _ngcontent-ng-c1281433528="" class="header-item"><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="icon-text"><div _ngcontent-ng-c1281433528="" class="label">DoS</div></div></div></div><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="icon-text"><div _ngcontent-ng-c1281433528="" class="label">07/22/24</div></div></div><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="icon-text"><div _ngcontent-ng-c1281433528="" class="label">07/22/24</div></div></div><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="icon-text"><div _ngcontent-ng-c1281433528="" class="label">05/22/24</div></div></div><!--bindings={
  "ng-reflect-ng-for-track-by": "trackByHitId(_index, hit) {\n  "
}--></div><div _ngcontent-ng-c1281433528="" class="hits-column sys-column"><div _ngcontent-ng-c1281433528="" class="header-item"><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="icon-text"><div _ngcontent-ng-c1281433528="" class="label">Sys</div></div></div></div><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="icon-text"><div _ngcontent-ng-c1281433528="" class="label">136</div></div></div><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="icon-text"><div _ngcontent-ng-c1281433528="" class="label">140</div></div></div><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="icon-text"><div _ngcontent-ng-c1281433528="" class="label">150</div></div></div><!--bindings={
  "ng-reflect-ng-for-track-by": "trackByHitId(_index, hit) {\n  "
}--></div><div _ngcontent-ng-c1281433528="" class="hits-column dias-column"><div _ngcontent-ng-c1281433528="" class="header-item"><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="icon-text"><div _ngcontent-ng-c1281433528="" class="label">Dias</div></div></div></div><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="icon-text"><div _ngcontent-ng-c1281433528="" class="label">82</div></div></div><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="icon-text"><div _ngcontent-ng-c1281433528="" class="label">82</div></div></div><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="icon-text"><div _ngcontent-ng-c1281433528="" class="label">90</div></div></div><!--bindings={
  "ng-reflect-ng-for-track-by": "trackByHitId(_index, hit) {\n  "
}--></div><div _ngcontent-ng-c1281433528="" class="hits-column page-column"><div _ngcontent-ng-c1281433528="" class="header-item"><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="icon-text"><div _ngcontent-ng-c1281433528="" class="label">Page</div></div></div></div><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="icon-text"><button _ngcontent-ng-c1281433528="" type="button" class="page-link" jsaction="click:;"><div _ngcontent-ng-c1281433528="" class="label">2</div></button></div></div><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="icon-text"><button _ngcontent-ng-c1281433528="" type="button" class="page-link" jsaction="click:;"><div _ngcontent-ng-c1281433528="" class="label">2</div></button></div></div><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="icon-text"><button _ngcontent-ng-c1281433528="" type="button" class="page-link" jsaction="click:;"><div _ngcontent-ng-c1281433528="" class="label">7</div></button></div></div><!--bindings={
  "ng-reflect-ng-for-track-by": "trackByHitId(_index, hit) {\n  "
}--></div><div _ngcontent-ng-c1281433528="" class="hits-column comment-column"><div _ngcontent-ng-c1281433528="" class="header-item"><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="icon-text"><div _ngcontent-ng-c1281433528="" class="label">Comment</div></div></div></div><div _ngcontent-ng-c1281433528="" class="table-item"><app-comment-box _ngcontent-ng-c1281433528="" class="comment-box-component ng-untouched ng-pristine ng-valid" _nghost-ng-c62406540="" ng-reflect-model="" ng-reflect-placeholder="Comment" aria-label="Comment for hit 1" ngh="3"><div _ngcontent-ng-c62406540="" class="comment-box default"><div _ngcontent-ng-c62406540="" class="row"><input _ngcontent-ng-c62406540="" type="text" class="comment-input" value="" placeholder="Comment" jsaction="input:;focus:;blur:;"></div></div></app-comment-box></div><div _ngcontent-ng-c1281433528="" class="table-item"><app-comment-box _ngcontent-ng-c1281433528="" class="comment-box-component ng-untouched ng-pristine ng-valid" _nghost-ng-c62406540="" ng-reflect-model="" ng-reflect-placeholder="Comment" aria-label="Comment for hit 2" ngh="3"><div _ngcontent-ng-c62406540="" class="comment-box default"><div _ngcontent-ng-c62406540="" class="row"><input _ngcontent-ng-c62406540="" type="text" class="comment-input" value="" placeholder="Comment" jsaction="input:;focus:;blur:;"></div></div></app-comment-box></div><div _ngcontent-ng-c1281433528="" class="table-item"><app-comment-box _ngcontent-ng-c1281433528="" class="comment-box-component ng-untouched ng-pristine ng-valid" _nghost-ng-c62406540="" ng-reflect-model="" ng-reflect-placeholder="Comment" aria-label="Comment for hit 3" ngh="3"><div _ngcontent-ng-c62406540="" class="comment-box default"><div _ngcontent-ng-c62406540="" class="row"><input _ngcontent-ng-c62406540="" type="text" class="comment-input" value="" placeholder="Comment" jsaction="input:;focus:;blur:;"></div></div></app-comment-box></div><!--bindings={
  "ng-reflect-ng-for-track-by": "trackByHitId(_index, hit) {\n  "
}--></div><div _ngcontent-ng-c1281433528="" class="hits-column include-column"><div _ngcontent-ng-c1281433528="" class="header-item"><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="icon-text"><div _ngcontent-ng-c1281433528="" class="label">Include</div></div></div></div><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="include-column-inner"><app-checkbox _ngcontent-ng-c1281433528="" class="include-checkbox ng-untouched ng-pristine ng-valid" _nghost-ng-c2693716353="" ng-reflect-model="false" aria-label="Include hit 1" ngh="4"><div _ngcontent-ng-c2693716353="" class="checkbox-container"><input _ngcontent-ng-c2693716353="" type="checkbox" class="checkbox" id="checkbox-9htm7lk" name="" jsaction="change:;blur:;"><!--bindings={
  "ng-reflect-ng-if": ""
}--></div></app-checkbox></div></div><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="include-column-inner"><app-checkbox _ngcontent-ng-c1281433528="" class="include-checkbox ng-untouched ng-pristine ng-valid" _nghost-ng-c2693716353="" ng-reflect-model="false" aria-label="Include hit 2" ngh="4"><div _ngcontent-ng-c2693716353="" class="checkbox-container"><input _ngcontent-ng-c2693716353="" type="checkbox" class="checkbox" id="checkbox-tkbvayb" name="" jsaction="change:;blur:;"><!--bindings={
  "ng-reflect-ng-if": ""
}--></div></app-checkbox></div></div><div _ngcontent-ng-c1281433528="" class="table-item"><div _ngcontent-ng-c1281433528="" class="include-column-inner"><app-checkbox _ngcontent-ng-c1281433528="" class="include-checkbox ng-untouched ng-pristine ng-valid" _nghost-ng-c2693716353="" ng-reflect-model="false" aria-label="Include hit 3" ngh="4"><div _ngcontent-ng-c2693716353="" class="checkbox-container"><input _ngcontent-ng-c2693716353="" type="checkbox" class="checkbox" id="checkbox-ag6h8t9" name="" jsaction="change:;blur:;"><!--bindings={
  "ng-reflect-ng-if": ""
}--></div></app-checkbox></div></div><!--bindings={
  "ng-reflect-ng-for-track-by": "trackByHitId(_index, hit) {\n  "
}--></div></div></div></div></app-hits></div><div _ngcontent-ng-c2414578827="" class="results-section"><app-results _ngcontent-ng-c2414578827="" title="Results" _nghost-ng-c2018085561="" ng-reflect-title="Results" ng-reflect-model="[object Object]" class="ng-untouched ng-pristine ng-valid" ngh="9"><div _ngcontent-ng-c2018085561="" class="results-container"><div _ngcontent-ng-c2018085561="" class="results-header"><div _ngcontent-ng-c2018085561="" class="results-title-section"><h1 _ngcontent-ng-c2018085561="" class="results-title">Results</h1><div _ngcontent-ng-c2018085561="" role="tablist" class="tab-navigation"><button _ngcontent-ng-c2018085561="" type="button" class="tab-button active" role="tab" aria-selected="true" aria-controls="results-5gl6177-inclusions-panel" id="results-5gl6177-inclusions-tab" jsaction="click:;"> Inclusions </button><button _ngcontent-ng-c2018085561="" type="button" class="tab-button" role="tab" aria-selected="false" aria-controls="results-5gl6177-exclusions-panel" id="results-5gl6177-exclusions-tab" jsaction="click:;"> Exclusions </button><button _ngcontent-ng-c2018085561="" type="button" class="tab-button" role="tab" aria-selected="false" aria-controls="results-5gl6177-none-found-panel" id="results-5gl6177-none-found-tab" jsaction="click:;"> None found </button><!--bindings={
  "ng-reflect-ng-for-of": "[object Object],[object Object"
}--></div></div></div><div _ngcontent-ng-c2018085561="" class="tab-content"><app-inclusions-tab _ngcontent-ng-c2018085561="" _nghost-ng-c1722032080="" ng-reflect-form-group="[object Object]" ng-reflect-form="[object Object]" ng-reflect-disabled="false" ng-reflect-id="results-5gl6177-inclusions" class="ng-untouched ng-pristine ng-valid" ngh="3" jsaction="submit:;"><div _ngcontent-ng-c1722032080="" class="inclusions-tab-content ng-untouched ng-pristine ng-valid" ng-reflect-form="[object Object]" jsaction="submit:;"><div _ngcontent-ng-c1722032080="" class="frame-942"><app-checkbox _ngcontent-ng-c1722032080="" label="Telehealth" formcontrolname="telehealth" _nghost-ng-c2693716353="" ng-reflect-label="Telehealth" ng-reflect-name="telehealth" ng-reflect-disabled="false" ng-reflect-is-disabled="false" class="ng-untouched ng-pristine ng-valid" ngh="6" jsaction="change:;"><div _ngcontent-ng-c2693716353="" class="checkbox-container"><input _ngcontent-ng-c2693716353="" type="checkbox" class="checkbox" id="checkbox-8l1fntg" name="" jsaction="change:;blur:;"><label _ngcontent-ng-c2693716353="" class="checkbox-label" for="checkbox-8l1fntg"> Telehealth </label><!--bindings={
  "ng-reflect-ng-if": "Telehealth"
}--></div></app-checkbox></div><div _ngcontent-ng-c1722032080="" class="frame-936"><div _ngcontent-ng-c1722032080="" class="frame-939"><div _ngcontent-ng-c1722032080="" class="frame-940"><span _ngcontent-ng-c1722032080="" class="text-sys">Sys</span><input _ngcontent-ng-c1722032080="" type="text" placeholder="Value" formcontrolname="sys" class="dropdown-inclusion ng-untouched ng-pristine ng-valid" ng-reflect-name="sys" ng-reflect-is-disabled="false" value="" jsaction="input:;blur:;compositionstart:;compositionend:;"></div><div _ngcontent-ng-c1722032080="" class="frame-941"><span _ngcontent-ng-c1722032080="" class="text-dias">Dias</span><input _ngcontent-ng-c1722032080="" type="text" placeholder="Value" formcontrolname="dias" class="dropdown-inclusion ng-untouched ng-pristine ng-valid" ng-reflect-name="dias" ng-reflect-is-disabled="false" value="" jsaction="input:;blur:;compositionstart:;compositionend:;"></div><div _ngcontent-ng-c1722032080="" class="frame-937"><span _ngcontent-ng-c1722032080="" class="text-date-of-service">Date of Service</span><app-calendar _ngcontent-ng-c1722032080="" placeholder="MM/DD/YY" formcontrolname="dateOfService" class="calendar ng-untouched ng-pristine ng-valid" _nghost-ng-c1227210501="" ng-reflect-placeholder="MM/DD/YY" ng-reflect-name="dateOfService" ng-reflect-disabled="false" ng-reflect-is-disabled="false" ngh="7"><div _ngcontent-ng-c1227210501="" class="calendar-container"><!--bindings={
  "ng-reflect-ng-if": ""
}--><div _ngcontent-ng-c1227210501="" class="date-input-container"><div _ngcontent-ng-c1227210501="" class="date-input-content"><input _ngcontent-ng-c1227210501="" type="text" class="date-input" id="calendar-6w3licg" name="" placeholder="MM/DD/YY" value="" min="" max="" aria-expanded="false" aria-haspopup="true" role="combobox" jsaction="input:;focus:;blur:;"><button _ngcontent-ng-c1227210501="" type="button" class="calendar-icon-button" aria-label="Open calendar" jsaction="click:;"><svg _ngcontent-ng-c1227210501="" width="16" height="18" viewBox="0 0 16 18" fill="none" xmlns="http://www.w3.org/2000/svg" class="calendar-icon"><rect _ngcontent-ng-c1227210501="" x="0.75" y="2.75" width="14.5" height="14.5" rx="1.25" stroke="currentColor" stroke-width="1.5"></rect><path _ngcontent-ng-c1227210501="" d="M4 1V4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path><path _ngcontent-ng-c1227210501="" d="M12 1V4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path><path _ngcontent-ng-c1227210501="" d="M1 7H15" stroke="currentColor" stroke-width="1.5"></path></svg></button></div></div><!--bindings={
  "ng-reflect-ng-if": "false"
}--><!--bindings={
  "ng-reflect-ng-if": ""
}--></div></app-calendar></div></div></div><div _ngcontent-ng-c1722032080="" class="frame-938"><span _ngcontent-ng-c1722032080="" class="text-notes">Notes</span><app-notes _ngcontent-ng-c1722032080="" label="" placeholder="Notes" formcontrolname="notes" class="notes ng-untouched ng-pristine ng-valid" _nghost-ng-c3668244963="" ng-reflect-label="" ng-reflect-placeholder="Notes" ng-reflect-name="notes" ng-reflect-disabled="false" ng-reflect-is-disabled="false" ngh="8"><div _ngcontent-ng-c3668244963="" class="notes-container"><!--bindings={
  "ng-reflect-ng-if": ""
}--><div _ngcontent-ng-c3668244963="" class="notes-input-wrapper"><textarea _ngcontent-ng-c3668244963="" class="notes-textarea" id="notes-hyzt7yi" name="" placeholder="Notes" maxlength="200" aria-describedby="notes-hyzt7yi-char-count" jsaction="input:;focus:;blur:;"></textarea><div _ngcontent-ng-c3668244963="" class="character-counter" id="notes-hyzt7yi-char-count"> 0/200 </div><!--bindings={
  "ng-reflect-ng-if": "true"
}--></div><!--bindings={
  "ng-reflect-ng-if": ""
}--></div></app-notes></div></div></app-inclusions-tab><!--bindings={
  "ng-reflect-ng-if": "true"
}--><!--bindings={
  "ng-reflect-ng-if": "false"
}--><!--bindings={
  "ng-reflect-ng-if": "false"
}--></div><!--bindings={
  "ng-reflect-ng-if": ""
}--></div></app-results></div><div _ngcontent-ng-c2414578827="" class="submit-section"><app-button _ngcontent-ng-c2414578827="" variant="primary" _nghost-ng-c502759576="" ng-reflect-variant="primary" ngh="10"><button _ngcontent-ng-c502759576="" type="button" ng-reflect-ng-class="button,button-primary,,,button" class="button button-primary button-icon-left" jsaction="click:;mousedown:;mouseup:;"><!--bindings={
  "ng-reflect-ng-if": null
}--> Submit <!--bindings={
  "ng-reflect-ng-if": null
}--></button></app-button></div></div></div></div></div></app-chart-review-page><!--container--></app-root>
<link rel="modulepreload" href="chunk-YLWVG4A5.js"><link rel="modulepreload" href="chunk-FVKW5FZS.js"><link rel="modulepreload" href="chunk-JRLQF6CE.js"><link rel="modulepreload" href="chunk-F5HTA5WY.js"><link rel="modulepreload" href="chunk-PC6IZSQ2.js"><script src="polyfills.js" type="module"></script><script src="main.js" type="module"></script>
<link rel="modulepreload" href="chunk-JQPK26PE.js">


<script id="ng-state" type="application/json">{"1562945894":{"b":{},"h":{},"s":200,"st":"OK","u":"assets/charts/CBP_Redacted_Usability.pdf","rt":"blob"},"__nghData__":[{"t":{"4":"t0","5":"t1","6":"t2"},"c":{"4":[{"i":"t0","r":1}],"5":[],"6":[{"i":"t2","r":1,"t":{"10":"t3","11":"t4"},"c":{"10":[{"i":"t3","r":1}],"11":[]}}]}},{"t":{"4":"t5"},"c":{"4":[{"i":"t5","r":1}]}},{"t":{"2":"t6","3":"t7","4":"t8"},"c":{"2":[],"3":[],"4":[{"i":"t8","r":1}]}},{},{"t":{"2":"t16"},"c":{"2":[]}},{"t":{"1":"t9","10":"t10","17":"t11","24":"t12","31":"t13","38":"t14","45":"t15"},"c":{"1":[{"i":"t9","r":1}],"10":[{"i":"t10","r":1,"x":3}],"17":[{"i":"t11","r":1,"x":3}],"24":[{"i":"t12","r":1,"x":3}],"31":[{"i":"t13","r":1,"x":3}],"38":[{"i":"t14","r":1,"x":3}],"45":[{"i":"t15","r":1,"x":3}]}},{"t":{"2":"t16"},"c":{"2":[{"i":"t16","r":1}]}},{"t":{"2":"t19","13":"t20","14":"t21"},"c":{"2":[],"13":[],"14":[]}},{"t":{"1":"t22","4":"t23","5":"t24"},"c":{"1":[],"4":[{"i":"t23","r":1}],"5":[]}},{"t":{"6":"t17","8":"t18","9":"t25","10":"t26","11":"t27"},"c":{"6":[{"i":"t17","r":1,"x":3}],"8":[{"i":"t18","r":1}],"9":[],"10":[],"11":[]}},{"t":{"1":"t28","3":"t29"},"c":{"1":[],"3":[]},"n":{"3":"0fn2"}},{"n":{"15":"14f2n"}},{"c":{"0":[{"i":"c2414578827","r":1}]}}]}</script></body></html>
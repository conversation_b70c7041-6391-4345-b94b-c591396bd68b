{"version": 3, "sources": ["src/app/features/auth/pages/login-page/login-page.component.ts", "src/app/features/auth/pages/login-page/login-page.component.html", "src/app/features/auth/auth-routing.module.ts", "src/app/features/auth/auth.module.ts"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-login-page',\r\n  imports: [],\r\n  templateUrl: './login-page.component.html',\r\n  styleUrl: './login-page.component.scss'\r\n})\r\nexport class LoginPageComponent {\r\n\r\n}\r\n", "<p>login-page works!</p>\r\n", "import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { LoginPageComponent } from '@features/auth/pages/login-page/login-page.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    redirectTo: 'login',\r\n    pathMatch: 'full'\r\n  },\r\n  {\r\n    path: 'login',\r\n    component: LoginPageComponent\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class AuthRoutingModule { }\r\n", "import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { AuthRoutingModule } from './auth-routing.module';\r\n\r\n\r\n@NgModule({\r\n  declarations: [],\r\n  imports: [\r\n    CommonModule,\r\n    AuthRoutingModule\r\n  ]\r\n})\r\nexport class AuthModule { }\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAQM,IAAO,qBAAP,MAAO,oBAAkB;;qCAAlB,qBAAkB;EAAA;yEAAlB,qBAAkB,WAAA,CAAA,CAAA,gBAAA,CAAA,GAAA,OAAA,GAAA,MAAA,GAAA,UAAA,SAAA,4BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACR/B,MAAA,yBAAA,GAAA,GAAA;AAAG,MAAA,iBAAA,GAAA,mBAAA;AAAiB,MAAA,uBAAA;;;;;sEDQP,oBAAkB,CAAA;UAN9B;uBACW,kBAAgB,SACjB,CAAA,GAAE,UAAA,+BAAA,CAAA;;;;6EAIA,oBAAkB,EAAA,WAAA,sBAAA,UAAA,kEAAA,YAAA,EAAA,CAAA;AAAA,GAAA;;;AEJ/B,IAAM,SAAiB;EACrB;IACE,MAAM;IACN,YAAY;IACZ,WAAW;;EAEb;IACE,MAAM;IACN,WAAW;;;AAQT,IAAO,oBAAP,MAAO,mBAAiB;;qCAAjB,oBAAiB;EAAA;wEAAjB,mBAAiB,CAAA;4EAHlB,aAAa,SAAS,MAAM,GAC5B,YAAY,EAAA,CAAA;;;sEAEX,mBAAiB,CAAA;UAJ7B;WAAS;MACR,SAAS,CAAC,aAAa,SAAS,MAAM,CAAC;MACvC,SAAS,CAAC,YAAY;KACvB;;;;;ACNK,IAAO,aAAP,MAAO,YAAU;;qCAAV,aAAU;EAAA;wEAAV,YAAU,CAAA;;IAJnB;IACA;EAAiB,EAAA,CAAA;;;sEAGR,YAAU,CAAA;UAPtB;WAAS;MACR,cAAc,CAAA;MACd,SAAS;QACP;QACA;;KAEH;;;", "names": []}
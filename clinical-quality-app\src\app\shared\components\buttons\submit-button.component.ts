import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-sumbit-button',
  standalone: true,
  imports: [CommonModule],
  template: `
    <button 
      [class]="getButtonClass()"
      [disabled]="property1 === 'Inactive'"
      (click)="onClick($event)">
      {{ getButtonText() }}
    </button>
  `,
  styleUrls: ['./submit-button.component.scss']
})
export class SubmitButtonComponent {
  @Input() property1: 'Default' | 'Inactive' = 'Default';
  @Output() click = new EventEmitter<MouseEvent>();

  onClick(event: MouseEvent): void {
    if (this.property1 !== 'Inactive') {
      this.click.emit(event);
    }
  }

  getButtonClass(): string {
    return this.property1 === 'Default' ? 'submit-button-default' : 'submit-button-inactive';
  }

  getButtonText(): string {
    return this.property1 === 'Default' ? 'Review' : 'Inactive';
  }
}

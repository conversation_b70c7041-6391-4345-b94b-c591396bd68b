<div class="assigned-table-container">
  <div class="assigned-table-header">
    <div *ngFor="let column of columns"
         class="assigned-table-header-cell"
         [ngStyle]="{'width': column.width}">
      {{ column.header }}
    </div>
  </div>

  <div class="assigned-table-body">
    <ng-container *ngIf="filteredCharts.length > 0; else noCharts">
      <div *ngFor="let chart of filteredCharts" class="assigned-table-row">
        <div *ngFor="let column of columns"
             class="assigned-table-cell"
             [ngStyle]="{'width': column.width}">
          <ng-container *ngIf="column.field !== 'status'; else statusTemplate">
            {{ getFieldValue(chart, column.field) }}
          </ng-container>

          <ng-template #statusTemplate>
            <app-sumbit-button
              [property1]="chart.status === 'Review' ? 'Default' : 'Inactive'"
              (click)="navigateToChartReview(chart)">
            </app-sumbit-button>
          </ng-template>
        </div>
      </div>
    </ng-container>

    <ng-template #noCharts>
      <div class="no-charts-message">
        No charts found matching your criteria.
      </div>
    </ng-template>
  </div>
</div>

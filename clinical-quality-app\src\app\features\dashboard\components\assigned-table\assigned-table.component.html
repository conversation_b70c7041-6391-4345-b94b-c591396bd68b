<div class="assigned-table_1134-1191">
  <div class="table_1134-1192">
    <div class="table_1134-1208">
      <div class="columns_1134-1209">
        <!-- Member ID Column -->
        <div class="column_1134-1210">
          <div class="header-item_1134-1211">
            <div class="table-item_1134-1212">
              <span class="text-label_1134-1214">Member ID</span>
            </div>
          </div>
          <ng-container *ngFor="let chart of filteredCharts">
            <div class="table-item_1134-1215">
              <span class="text-label_1134-1216">{{ chart.memberId }}</span>
            </div>
          </ng-container>
        </div>

        <!-- First Name Column -->
        <div class="column_1235-930">
          <div class="header-item_1235-931">
            <div class="table-item_1235-932">
              <span class="text-label_1235-934">First name</span>
            </div>
          </div>
          <ng-container *ngFor="let chart of filteredCharts">
            <div class="table-item_1235-939">
              <div class="icon-text_1235-940">
                <span class="text-label_1235-941">{{ chart.firstName }}</span>
              </div>
            </div>
          </ng-container>
        </div>

        <!-- Last Name Column -->
        <div class="column_1134-1235">
          <div class="header-item_1134-1236">
            <div class="table-item_1134-1237">
              <span class="text-label_1134-1239">Last name</span>
            </div>
          </div>
          <ng-container *ngFor="let chart of filteredCharts">
            <div class="table-item_1134-1240">
              <div class="icon-text_1134-1241">
                <span class="text-label_1134-1242">{{ chart.lastName }}</span>
              </div>
            </div>
          </ng-container>
        </div>

        <!-- Middle Name Column -->
        <div class="column_1235-969">
          <div class="header-item_1235-970">
            <div class="table-item_1235-971">
              <span class="text-label_1235-973">Middle name</span>
            </div>
          </div>
          <ng-container *ngFor="let chart of filteredCharts">
            <div class="table-item_1235-974">
              <div class="icon-text_1235-975">
                <span *ngIf="chart.middleName" class="text-label_1235-979">{{ chart.middleName }}</span>
              </div>
            </div>
          </ng-container>
        </div>

        <!-- DOB Column -->
        <div class="column_1134-1270">
          <div class="header-item_1134-1271">
            <div class="table-item_1134-1272">
              <span class="text-label_1134-1274">DOB</span>
            </div>
          </div>
          <ng-container *ngFor="let chart of filteredCharts">
            <div class="table-item_1134-1275">
              <div class="icon-text_1134-1276">
                <span class="text-label_1134-1277">{{ chart.dob }}</span>
              </div>
            </div>
          </ng-container>
        </div>

        <!-- LOB Column -->
        <div class="column_1134-1305">
          <div class="header-item_1134-1306">
            <div class="table-item_1134-1307">
              <span class="text-label_1134-1309">LOB</span>
            </div>
          </div>
          <ng-container *ngFor="let chart of filteredCharts">
            <div class="table-item_1134-1310">
              <div class="icon-text_1134-1311">
                <span class="text-label_1134-1312">{{ chart.lob }}</span>
              </div>
            </div>
          </ng-container>
        </div>

        <!-- Measure Column -->
        <div class="column_1134-1340">
          <div class="header-item_1134-1341">
            <div class="table-item_1134-1342">
              <span class="text-label_1134-1344">Measure</span>
            </div>
          </div>
          <ng-container *ngFor="let chart of filteredCharts">
            <div class="table-item_1134-1345">
              <div class="icon-text_1134-1346">
                <span class="text-label_1134-1347">{{ chart.measure }}</span>
              </div>
            </div>
          </ng-container>
        </div>

        <!-- Review 1 Column -->
        <div class="column_1134-1375">
          <div class="header-item_1134-1376">
            <div class="table-item_1134-1377">
              <span class="text-label_1134-1379">Review 1</span>
            </div>
          </div>
          <ng-container *ngFor="let chart of filteredCharts">
            <div class="table-item_1134-1380">
              <span class="text-label_1134-1381">{{ chart.review1 }}</span>
            </div>
          </ng-container>
        </div>

        <!-- Review 2 Column -->
        <div class="column_1134-1400">
          <div class="header-item_1134-1401">
            <div class="table-item_1134-1402">
              <span class="text-label_1134-1404">Review 2</span>
            </div>
          </div>
          <ng-container *ngFor="let chart of filteredCharts">
            <div class="table-item_1134-1405">
              <span class="text-label_1134-1406">{{ chart.review2 }}</span>
            </div>
          </ng-container>
        </div>

        <!-- Assigned Column -->
        <div class="column_1134-1425">
          <div class="header-item_1134-1426">
            <div class="table-item_1134-1427">
              <span class="text-label_1134-1429">Assigned</span>
            </div>
          </div>
          <ng-container *ngFor="let chart of filteredCharts">
            <div class="table-item_1134-1430">
              <span class="text-label_1134-1431">{{ chart.assigned }}</span>
            </div>
          </ng-container>
        </div>

        <!-- Status Column -->
        <div class="column_1134-1450">
          <div class="header-item_1134-1451">
            <div class="table-item_1134-1452">
              <span class="text-label_1134-1454">Status</span>
            </div>
          </div>
          <ng-container *ngFor="let chart of filteredCharts">
            <div class="table-item_1134-1455">
              <app-sumbit-button
                [property1]="chart.status === 'Review' ? 'Default' : 'Inactive'"
                class="sumbit-button_1134-1462"
                (click)="navigateToChartReview(chart)">
              </app-sumbit-button>
            </div>
          </ng-container>
        </div>
      </div>
    </div>

    <!-- No Charts Message -->
    <div *ngIf="filteredCharts.length === 0" class="no-charts-message">
      No charts found matching your criteria.
    </div>
  </div>
</div>

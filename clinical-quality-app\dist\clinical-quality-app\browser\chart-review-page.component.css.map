{"version": 3, "sources": ["src/app/features/chart-review/pages/chart-review-page/chart-review-page.component.scss"], "sourcesContent": ["// Import design system variables and mixins\r\n@use 'styles/variables' as variables;\r\n@use 'styles/mixins' as mixins;\r\n\r\n.container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100vh;\r\n}\r\n\r\n// Main Content Layout - responsive width with max constraint\r\n.main-content {\r\n  width: 100%; // Use full available width\r\n  max-width: 1800px; // Increased from 1440px for better screen utilization\r\n  margin: 0 auto; // Center the content\r\n  background: #F6F6F6;\r\n  flex-direction: column;\r\n  justify-content: flex-start;\r\n  align-items: flex-start;\r\n  display: flex;\r\n  padding: 20px 30px;\r\n  gap: 20px;\r\n  box-sizing: border-box;\r\n\r\n  // Responsive padding\r\n  @media (max-width: 768px) {\r\n    padding: 15px 20px;\r\n  }\r\n\r\n  @media (max-width: 480px) {\r\n    padding: 10px 15px;\r\n  }\r\n}\r\n\r\n// Demographics section - constrained to match two-column layout width\r\n.demographics-section {\r\n  width: 100%;\r\n  margin-bottom: 0; // Remove margin, gap is handled by main-content\r\n\r\n  // Match the responsive behavior of the content-layout below\r\n  @media (max-width: 1200px) {\r\n    width: 100%; // Full width when columns stack vertically\r\n  }\r\n}\r\n\r\n// Override demographics component styling to match content layout constraints\r\n:host ::ng-deep .demographics-section app-demographics .demographics-container {\r\n  // The demographics component should align with the visual boundaries of the content below\r\n  // Since both the PDF column and demographics have 20px padding, they should align naturally\r\n  // But if there's still overflow, we constrain it here\r\n  max-width: 100%;\r\n  box-sizing: border-box;\r\n}\r\n\r\n// Two-column layout - responsive with improved breakpoints\r\n.content-layout {\r\n  width: 100%;\r\n  justify-content: flex-start;\r\n  align-items: flex-start;\r\n  gap: 20px;\r\n  display: flex;\r\n\r\n  // Enhanced responsive breakpoints\r\n  @media (max-width: 1400px) {\r\n    gap: 15px; // Reduce gap on medium screens\r\n  }\r\n\r\n  @media (max-width: 1200px) {\r\n    flex-direction: column;\r\n    gap: 20px;\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    gap: 15px;\r\n  }\r\n}\r\n\r\n// Left Column: PDF Viewer - responsive and flexible\r\n.pdf-column {\r\n  flex: 1; // Takes remaining space after right column\r\n  min-width: 500px; // Reduced minimum width for better responsiveness\r\n  padding: 20px;\r\n  background: white;\r\n  border-radius: 8px;\r\n  outline: 1px #F1F5F7 solid;\r\n  outline-offset: -1px;\r\n  flex-direction: column;\r\n  justify-content: flex-start;\r\n  align-items: flex-start;\r\n  display: flex;\r\n\r\n  // Enhanced responsive adjustments\r\n  @media (max-width: 1400px) {\r\n    min-width: 450px;\r\n  }\r\n\r\n  @media (max-width: 1200px) {\r\n    flex: none;\r\n    width: 100%;\r\n    min-width: auto;\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    padding: 15px;\r\n  }\r\n\r\n  @media (max-width: 480px) {\r\n    padding: 10px;\r\n  }\r\n}\r\n\r\n// Right Column: Hits and Results - responsive width\r\n.right-column {\r\n  width: 517px; // Optimal width from Figma\r\n  max-width: 600px; // Allow slight expansion on larger screens\r\n  min-width: 400px; // Minimum width for usability\r\n  flex-shrink: 0; // Don't shrink below minimum\r\n  flex-direction: column;\r\n  justify-content: flex-start;\r\n  align-items: flex-end;\r\n  gap: 20px;\r\n  display: flex;\r\n\r\n  // Enhanced responsive adjustments\r\n  @media (max-width: 1600px) {\r\n    width: 500px; // Slightly smaller on medium-large screens\r\n  }\r\n\r\n  @media (max-width: 1400px) {\r\n    width: 480px;\r\n    min-width: 380px;\r\n  }\r\n\r\n  @media (max-width: 1200px) {\r\n    width: 100%;\r\n    max-width: none;\r\n    min-width: auto;\r\n    align-items: stretch;\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    gap: 15px;\r\n  }\r\n}\r\n\r\n// Hits section - container for hits component\r\n.hits-section {\r\n  align-self: stretch;\r\n  display: flex;\r\n  flex-direction: column;\r\n  // Remove padding, background, borders - let the component handle its own styling\r\n}\r\n\r\n// Results section - container for results component\r\n.results-section {\r\n  align-self: stretch;\r\n  display: flex;\r\n  flex-direction: column;\r\n  // Remove padding, background, borders - let the component handle its own styling\r\n}\r\n\r\n// Submit section - exact Figma specs\r\n.submit-section {\r\n  border-radius: 8px;\r\n  justify-content: flex-end;\r\n  align-items: flex-end;\r\n  display: inline-flex;\r\n}\r\n\r\n// PDF viewer - enhanced responsive design\r\n:host ::ng-deep app-pdf-viewer {\r\n  align-self: stretch;\r\n  height: 979px; // Optimal height from Figma\r\n  min-height: 600px; // Minimum height for usability\r\n  position: relative;\r\n  background: #E8E8EB;\r\n  overflow: hidden;\r\n\r\n  // Enhanced responsive adjustments\r\n  @media (max-width: 1600px) {\r\n    height: 850px; // Slightly smaller on large screens\r\n  }\r\n\r\n  @media (max-width: 1400px) {\r\n    height: 750px;\r\n    min-height: 550px;\r\n  }\r\n\r\n  @media (max-width: 1200px) {\r\n    height: 70vh; // Use viewport height on smaller screens\r\n    min-height: 500px;\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    height: 60vh;\r\n    min-height: 400px;\r\n  }\r\n\r\n  @media (max-width: 480px) {\r\n    height: 50vh;\r\n    min-height: 350px;\r\n  }\r\n}\r\n\r\n:host ::ng-deep .pdf-viewer-container {\r\n  width: 100%;\r\n  height: 100%;\r\n  background: #E8E8EB;\r\n  overflow: auto; // Allow scrolling\r\n}\r\n\r\n:host ::ng-deep ngx-extended-pdf-viewer {\r\n  width: 100%;\r\n  height: 100%;\r\n}"], "mappings": ";AAIA,CAAA;AACE,WAAA;AACA,kBAAA;AACA,UAAA;;AAIF,CAAA;AACE,SAAA;AACA,aAAA;AACA,UAAA,EAAA;AACA,cAAA;AACA,kBAAA;AACA,mBAAA;AACA,eAAA;AACA,WAAA;AACA,WAAA,KAAA;AACA,OAAA;AACA,cAAA;;AAGA,OAAA,CAAA,SAAA,EAAA;AAdF,GAAA;AAeI,aAAA,KAAA;;;AAGF,OAAA,CAAA,SAAA,EAAA;AAlBF,GAAA;AAmBI,aAAA,KAAA;;;AAKJ,CAAA;AACE,SAAA;AACA,iBAAA;;AAGA,OAAA,CAAA,SAAA,EAAA;AALF,GAAA;AAMI,WAAA;;;AAKJ,MAAA,UAAA,CAXA,qBAWA,iBAAA,CAAA;AAIE,aAAA;AACA,cAAA;;AAIF,CAAA;AACE,SAAA;AACA,mBAAA;AACA,eAAA;AACA,OAAA;AACA,WAAA;;AAGA,OAAA,CAAA,SAAA,EAAA;AARF,GAAA;AASI,SAAA;;;AAGF,OAAA,CAAA,SAAA,EAAA;AAZF,GAAA;AAaI,oBAAA;AACA,SAAA;;;AAGF,OAAA,CAAA,SAAA,EAAA;AAjBF,GAAA;AAkBI,SAAA;;;AAKJ,CAAA;AACE,QAAA;AACA,aAAA;AACA,WAAA;AACA,cAAA;AACA,iBAAA;AACA,WAAA,IAAA,QAAA;AACA,kBAAA;AACA,kBAAA;AACA,mBAAA;AACA,eAAA;AACA,WAAA;;AAGA,OAAA,CAAA,SAAA,EAAA;AAdF,GAAA;AAeI,eAAA;;;AAGF,OAAA,CAAA,SAAA,EAAA;AAlBF,GAAA;AAmBI,UAAA;AACA,WAAA;AACA,eAAA;;;AAGF,OAAA,CAAA,SAAA,EAAA;AAxBF,GAAA;AAyBI,aAAA;;;AAGF,OAAA,CAAA,SAAA,EAAA;AA5BF,GAAA;AA6BI,aAAA;;;AAKJ,CAAA;AACE,SAAA;AACA,aAAA;AACA,aAAA;AACA,eAAA;AACA,kBAAA;AACA,mBAAA;AACA,eAAA;AACA,OAAA;AACA,WAAA;;AAGA,OAAA,CAAA,SAAA,EAAA;AAZF,GAAA;AAaI,WAAA;;;AAGF,OAAA,CAAA,SAAA,EAAA;AAhBF,GAAA;AAiBI,WAAA;AACA,eAAA;;;AAGF,OAAA,CAAA,SAAA,EAAA;AArBF,GAAA;AAsBI,WAAA;AACA,eAAA;AACA,eAAA;AACA,iBAAA;;;AAGF,OAAA,CAAA,SAAA,EAAA;AA5BF,GAAA;AA6BI,SAAA;;;AAKJ,CAAA;AACE,cAAA;AACA,WAAA;AACA,kBAAA;;AAKF,CAAA;AACE,cAAA;AACA,WAAA;AACA,kBAAA;;AAKF,CAAA;AACE,iBAAA;AACA,mBAAA;AACA,eAAA;AACA,WAAA;;AAIF,MAAA,UAAA;AACE,cAAA;AACA,UAAA;AACA,cAAA;AACA,YAAA;AACA,cAAA;AACA,YAAA;;AAGA,OAAA,CAAA,SAAA,EAAA;AATF,QAAA,UAAA;AAUI,YAAA;;;AAGF,OAAA,CAAA,SAAA,EAAA;AAbF,QAAA,UAAA;AAcI,YAAA;AACA,gBAAA;;;AAGF,OAAA,CAAA,SAAA,EAAA;AAlBF,QAAA,UAAA;AAmBI,YAAA;AACA,gBAAA;;;AAGF,OAAA,CAAA,SAAA,EAAA;AAvBF,QAAA,UAAA;AAwBI,YAAA;AACA,gBAAA;;;AAGF,OAAA,CAAA,SAAA,EAAA;AA5BF,QAAA,UAAA;AA6BI,YAAA;AACA,gBAAA;;;AAIJ,MAAA,UAAA,CAAA;AACE,SAAA;AACA,UAAA;AACA,cAAA;AACA,YAAA;;AAGF,MAAA,UAAA;AACE,SAAA;AACA,UAAA;;", "names": []}
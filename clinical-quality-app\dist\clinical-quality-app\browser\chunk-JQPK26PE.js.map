{"version": 3, "sources": ["src/app/features/chart-review/services/pdf.service.ts", "src/app/features/chart-review/components/pdf-viewer/pdf-viewer.component.ts", "src/app/features/chart-review/components/pdf-viewer/pdf-viewer.component.html", "src/app/features/chart-review/pages/chart-review-page/chart-review-page.component.ts", "src/app/features/chart-review/pages/chart-review-page/chart-review-page.component.html", "src/app/features/chart-review/chart-review-routing.module.ts", "src/app/features/chart-review/components/annotation/annotation.component.ts", "src/app/features/chart-review/components/annotation/annotation.component.html", "src/app/features/chart-review/components/validation/validation.component.ts", "src/app/features/chart-review/components/validation/validation.component.html", "src/app/features/chart-review/chart-review.module.ts"], "sourcesContent": ["import { Injectable, Inject, PLATFORM_ID } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { BehaviorSubject, Observable, Subject, from, of } from 'rxjs';\r\nimport { catchError, map, shareReplay, switchMap, tap } from 'rxjs/operators';\r\nimport { isPlatformBrowser } from '@angular/common';\r\n\r\n// We'll dynamically import PDF.js only in browser environments\r\n// Type definition for PDF.js library\r\ntype PdfJsLib = typeof import('pdfjs-dist');\r\n// Specific types from pdfjs-dist\r\ntype PDFDocumentProxy = import('pdfjs-dist').PDFDocumentProxy;\r\ntype PDFPageProxy = import('pdfjs-dist').PDFPageProxy;\r\n\r\n// Define our own TextContent interface based on what pdfjs-dist provides\r\ninterface TextContent {\r\n  items: Array<any>;\r\n  styles: Record<string, any>;\r\n}\r\n\r\n// Define interfaces for PDF document and page\r\nexport interface PdfDocument {\r\n  numPages: number;\r\n  getPage: (pageNumber: number) => Promise<PDFPageProxy>;\r\n}\r\n\r\nexport interface PdfSearchResult {\r\n  pageNumber: number;\r\n  matchIndex: number;\r\n  text: string;\r\n  position?: {\r\n    left: number;\r\n    top: number;\r\n    right: number;\r\n    bottom: number;\r\n  };\r\n}\r\n\r\n/**\r\n * Interface for PDF text item\r\n * This is a custom interface to match the structure returned by PDF.js\r\n */\r\nexport interface TextItem {\r\n  str: string;\r\n  transform: number[];\r\n  width: number;\r\n  height: number;\r\n  dir: string;\r\n  fontName?: string;\r\n}\r\n\r\n/**\r\n * Interface for PDF text content\r\n */\r\nexport interface PdfTextContent {\r\n  pageNumber: number;\r\n  items: TextItem[];\r\n  styles: object;\r\n}\r\n\r\n/**\r\n * Interface for highlight information\r\n */\r\nexport interface PdfHighlight {\r\n  id: string;\r\n  pageNumber: number;\r\n  position: {\r\n    left: number;\r\n    top: number;\r\n    right: number;\r\n    bottom: number;\r\n  };\r\n  color: string;\r\n  text: string;\r\n}\r\n\r\n/**\r\n * Service for handling PDF operations including loading, text extraction,\r\n * searching, and highlighting\r\n */\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class PdfService {\r\n  private pdfDocument: PDFDocumentProxy | null = null;\r\n  private pdfDocumentSubject = new BehaviorSubject<PDFDocumentProxy | null>(null);\r\n  private pdfInfoSubject = new BehaviorSubject<PdfDocument | null>(null);\r\n  private textContentCache = new Map<number, PdfTextContent>();\r\n  private highlightsSubject = new BehaviorSubject<PdfHighlight[]>([]);\r\n  private pdfBase64DataUrlSubject = new BehaviorSubject<string | null>(null); // New subject for base64 data URL\r\n  private loadingSubject = new BehaviorSubject<boolean>(false);\r\n  private errorSubject = new Subject<string>();\r\n  private isBrowser: boolean;\r\n  private pdfjsLib: PdfJsLib | null = null;\r\n  private pdfJsLibPromise: Promise<PdfJsLib | null> | null = null; // Promise for PDF.js library loading\r\n\r\n  /**\r\n   * Observable for the current PDF document\r\n   */\r\n  public pdfDocument$ = this.pdfDocumentSubject.asObservable();\r\n\r\n  /**\r\n   * Observable for the PDF document information\r\n   */\r\n  public pdfInfo$ = this.pdfInfoSubject.asObservable();\r\n\r\n  /**\r\n   * Observable for the highlights\r\n   */\r\n  public highlights$ = this.highlightsSubject.asObservable();\r\n\r\n  /**\r\n   * Observable for the PDF data as a base64 data URL\r\n   */\r\n  public pdfBase64DataUrl$ = this.pdfBase64DataUrlSubject.asObservable();\r\n\r\n  /**\r\n   * Observable for loading state\r\n   */\r\n  public loading$ = this.loadingSubject.asObservable();\r\n\r\n  /**\r\n   * Observable for error messages\r\n   */\r\n  public error$ = this.errorSubject.asObservable();\r\n\r\n  constructor(\r\n    private http: HttpClient,\r\n    @Inject(PLATFORM_ID) private platformId: Object\r\n  ) {\r\n    this.isBrowser = isPlatformBrowser(this.platformId);\r\n\r\n    // Initialize PDF.js only in browser environment\r\n    if (this.isBrowser) {\r\n      this.pdfJsLibPromise = this.loadPdfJsLibrary();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Dynamically loads the PDF.js library.\r\n   * This ensures it's only loaded in browser environments.\r\n   * @returns A promise that resolves with the PDF.js library or null if an error occurs.\r\n   */\r\n  private async loadPdfJsLibrary(): Promise<PdfJsLib | null> {\r\n    try {\r\n      console.log('[PdfService] Starting to load PDF.js library...');\r\n      // Dynamic import of PDF.js\r\n      const pdfjs = await import('pdfjs-dist');\r\n      this.pdfjsLib = pdfjs;\r\n\r\n      // Set the worker source path\r\n      this.pdfjsLib.GlobalWorkerOptions.workerSrc = 'assets/pdf.worker.mjs';\r\n      console.log('[PdfService] PDF.js library loaded and workerSrc set.');\r\n      return this.pdfjsLib;\r\n    } catch (error) {\r\n      console.error('[PdfService] Error loading PDF.js library:', error);\r\n      const errorMessage = error instanceof Error ? error.message : String(error);\r\n      this.errorSubject.next(`Critical: PDF.js library failed to load: ${errorMessage}`);\r\n      this.pdfjsLib = null;\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Ensures PDF.js library is loaded before proceeding.\r\n   * @returns A promise that resolves when PDF.js is ready, or rejects if it fails to load.\r\n   */\r\n  private async ensurePdfJsLibLoaded(): Promise<void> {\r\n    if (this.pdfjsLib) {\r\n      return Promise.resolve(); // Already loaded\r\n    }\r\n    if (this.pdfJsLibPromise) {\r\n      await this.pdfJsLibPromise; // Wait for the loading attempt to complete\r\n      if (!this.pdfjsLib) { // Check if it was successfully loaded and set\r\n        const message = 'PDF.js library was not available after loading attempt.';\r\n        console.error(`[PdfService] ${message}`);\r\n        this.errorSubject.next(`Critical: ${message}`);\r\n        throw new Error(message);\r\n      }\r\n      return Promise.resolve();\r\n    }\r\n    // Should not happen if constructor logic is correct, but as a fallback:\r\n    console.error('[PdfService] PDF.js library initialization not started.');\r\n    throw new Error('PDF.js library initialization not started.');\r\n  }\r\n\r\n  /**\r\n   * Clears the text content cache\r\n   */\r\n  public clearCache(): void {\r\n    this.textContentCache.clear();\r\n    this.pdfBase64DataUrlSubject.next(null); // Clear base64 data as well\r\n  }\r\n\r\n  /**\r\n   * Loads a PDF document from a URL\r\n   * @param url URL of the PDF document\r\n   * @returns Observable of the PDF document\r\n   */\r\n  loadPdf(url: string): Observable<PdfDocument> {\r\n    console.log('[PdfService] loadPdf called with URL:', url);\r\n\r\n    if (!this.isBrowser) {\r\n      console.log('[PdfService] Not in browser environment, returning empty document');\r\n      return of({ numPages: 0, getPage: () => Promise.reject('PDF.js not available in server environment') });\r\n    }\r\n\r\n    this.loadingSubject.next(true);\r\n    this.clearCache();\r\n\r\n    return from(this.ensurePdfJsLibLoaded()).pipe(\r\n      switchMap(() => {\r\n        // At this point, this.pdfjsLib should be loaded by ensurePdfJsLibLoaded\r\n        if (!this.pdfjsLib) {\r\n          // This case should ideally not be reached if ensurePdfJsLibLoaded works correctly.\r\n          console.error('[PdfService] loadPdf: PDF.js library still not loaded after ensurePdfJsLibLoaded resolved.');\r\n          throw new Error('PDF.js library failed to initialize properly.');\r\n        }\r\n        console.log('[PdfService] Attempting to fetch PDF from URL:', url);\r\n        return this.http.get(url, { responseType: 'arraybuffer' }).pipe(\r\n          tap(arrayBuffer => {\r\n            // Convert ArrayBuffer to base64 data URL and emit\r\n            const dataUrl = this.arrayBufferToDataUrl(arrayBuffer);\r\n            this.pdfBase64DataUrlSubject.next(dataUrl);\r\n          })\r\n        );\r\n      }),\r\n      switchMap(data => { // data here is the ArrayBuffer\r\n        // this.pdfjsLib is guaranteed to be non-null here if the previous switchMap didn't throw.\r\n        try {\r\n          return from(this.pdfjsLib!.getDocument({ data }).promise);\r\n        } catch (error) {\r\n          console.error('[PdfService] Error creating PDF document in getDocument:', error);\r\n          this.errorSubject.next('Failed to process PDF data');\r\n          throw error; // Rethrow to be caught by outer catchError\r\n        }\r\n      }), // Emits PDFDocumentProxy\r\n      map((pdfDocProxy: PDFDocumentProxy) => { // Renamed for clarity\r\n        this.pdfDocument = pdfDocProxy; // Set the internal full proxy\r\n        this.pdfDocumentSubject.next(pdfDocProxy);\r\n\r\n        // Create a simplified PdfDocument object that matches the interface\r\n        const documentInfo: PdfDocument = {\r\n          numPages: pdfDocProxy.numPages,\r\n          getPage: (pageNumber: number) => pdfDocProxy.getPage(pageNumber)\r\n        };\r\n\r\n        this.pdfInfoSubject.next(documentInfo);\r\n        this.loadingSubject.next(false);\r\n        return documentInfo; // This is the transformed value for the observable stream\r\n      }),\r\n      catchError((error: Error) => {\r\n        console.error('Error loading PDF:', error);\r\n        this.errorSubject.next(`Failed to load PDF: ${error.message}`);\r\n        this.loadingSubject.next(false);\r\n        // Rethrow the error to allow components to handle it\r\n        throw error;\r\n      }),\r\n      // Share the PDF document among multiple subscribers\r\n      shareReplay(1)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Loads a PDF document from a base64 data URL\r\n   * @param dataUrl Base64 data URL containing the PDF\r\n   * @returns Observable of the PDF document\r\n   */\r\n  loadPdfFromDataUrl(dataUrl: string): Observable<PdfDocument> {\r\n    if (!this.isBrowser) {\r\n      return of({ numPages: 0, getPage: () => Promise.reject('PDF.js not available in server environment') });\r\n    }\r\n\r\n    this.loadingSubject.next(true);\r\n    this.clearCache();\r\n\r\n    // Set the base64 data URL immediately\r\n    this.pdfBase64DataUrlSubject.next(dataUrl);\r\n\r\n    return from(this.ensurePdfJsLibLoaded()).pipe(\r\n      switchMap(() => {\r\n        if (!this.pdfjsLib) {\r\n          console.error('[PdfService] loadPdfFromDataUrl: PDF.js library still not loaded after ensurePdfJsLibLoaded resolved.');\r\n          throw new Error('PDF.js library failed to initialize properly.');\r\n        }\r\n\r\n        // Convert data URL to ArrayBuffer\r\n        const base64Data = dataUrl.split(',')[1];\r\n        const binaryString = window.atob(base64Data);\r\n        const arrayBuffer = new ArrayBuffer(binaryString.length);\r\n        const uint8Array = new Uint8Array(arrayBuffer);\r\n\r\n        for (let i = 0; i < binaryString.length; i++) {\r\n          uint8Array[i] = binaryString.charCodeAt(i);\r\n        }\r\n\r\n        console.log('[PdfService] Calling pdfjsLib.getDocument... (from data URL)');\r\n        return from(this.pdfjsLib!.getDocument({ data: arrayBuffer }).promise);\r\n      }),\r\n      map((pdfDocument: PDFDocumentProxy) => {\r\n        console.log('[PdfService] PDF loaded from data URL with', pdfDocument.numPages, 'pages');\r\n        this.pdfDocument = pdfDocument;\r\n        this.pdfDocumentSubject.next(pdfDocument);\r\n\r\n        // Create a simplified PdfDocument object that matches the interface\r\n        const documentInfo: PdfDocument = {\r\n          numPages: pdfDocument.numPages,\r\n          getPage: (pageNumber: number) => pdfDocument.getPage(pageNumber)\r\n        };\r\n\r\n        this.pdfInfoSubject.next(documentInfo);\r\n        this.loadingSubject.next(false);\r\n        return documentInfo;\r\n      }),\r\n      catchError((error: Error) => {\r\n        console.error('[PdfService] Error loading PDF from data URL:', error);\r\n        this.errorSubject.next(`Failed to load PDF: ${error.message}`);\r\n        this.loadingSubject.next(false);\r\n        throw error;\r\n      }),\r\n      shareReplay(1)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Loads a PDF document from a File object\r\n   * @param file File object containing the PDF\r\n   * @returns Observable of the PDF document\r\n   */\r\n  loadPdfFromFile(file: File): Observable<PdfDocument> {\r\n    if (!this.isBrowser) {\r\n      return of({ numPages: 0, getPage: () => Promise.reject('PDF.js not available in server environment') });\r\n    }\r\n\r\n    this.loadingSubject.next(true);\r\n    this.clearCache();\r\n\r\n    return from(this.ensurePdfJsLibLoaded()).pipe(\r\n      switchMap(() => {\r\n        // At this point, this.pdfjsLib should be loaded by ensurePdfJsLibLoaded\r\n        if (!this.pdfjsLib) {\r\n          console.error('[PdfService] loadPdfFromFile: PDF.js library still not loaded after ensurePdfJsLibLoaded resolved.');\r\n          throw new Error('PDF.js library failed to initialize properly.');\r\n        }\r\n        return new Observable<PdfDocument>(observer => {\r\n          const fileReader = new FileReader();\r\n\r\n          fileReader.onload = () => {\r\n            console.log('[PdfService] FileReader onload triggered for loadPdfFromFile.');\r\n            const arrayBuffer = fileReader.result;\r\n\r\n            if (!(arrayBuffer instanceof ArrayBuffer)) {\r\n              console.error('[PdfService] FileReader result is not an ArrayBuffer.');\r\n              observer.error(new Error('File read result was not an ArrayBuffer.'));\r\n              return;\r\n            }\r\n\r\n            // Convert ArrayBuffer to base64 data URL and emit\r\n            const dataUrl = this.arrayBufferToDataUrl(arrayBuffer);\r\n            this.pdfBase64DataUrlSubject.next(dataUrl);\r\n\r\n            // this.pdfjsLib is guaranteed to be non-null here.\r\n            console.log('[PdfService] Calling pdfjsLib.getDocument... (from file)');\r\n            this.pdfjsLib!.getDocument({ data: arrayBuffer }).promise\r\n              .then((pdfDocument: PDFDocumentProxy) => {\r\n                console.log('[PdfService] pdfjsLib.getDocument resolved successfully.'); // Added log\r\n                console.log(`[PdfService] PDF loaded with ${pdfDocument.numPages} pages.`); // Added log\r\n                this.pdfDocument = pdfDocument;\r\n                this.pdfDocumentSubject.next(pdfDocument);\r\n\r\n                // Create a simplified PdfDocument object that matches the interface\r\n                const documentInfo: PdfDocument = {\r\n                  numPages: pdfDocument.numPages,\r\n                  getPage: (pageNumber: number) => pdfDocument.getPage(pageNumber)\r\n                };\r\n\r\n                this.pdfInfoSubject.next(documentInfo);\r\n                this.loadingSubject.next(false);\r\n\r\n                observer.next(documentInfo);\r\n                observer.complete();\r\n              })\r\n              .catch((error: Error) => {\r\n                console.error('[PdfService] Error loading PDF from file:', error); // Modified log\r\n                this.errorSubject.next(`Failed to load PDF: ${error.message}`);\r\n                this.loadingSubject.next(false);\r\n                observer.error(error);\r\n              });\r\n          };\r\n\r\n          fileReader.onerror = (event) => {\r\n            console.error('[PdfService] Error reading file:', event); // Modified log\r\n            this.errorSubject.next('Failed to read the file');\r\n            this.loadingSubject.next(false);\r\n            observer.error(new Error('Failed to read the file'));\r\n          };\r\n\r\n          // Read the file as an ArrayBuffer\r\n          fileReader.readAsArrayBuffer(file);\r\n        });\r\n      }),\r\n      // Share the PDF document among multiple subscribers\r\n      shareReplay(1)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Gets text content for a specific page\r\n   * @param pageNumber Page number (1-based)\r\n   * @returns Observable of the text content\r\n   */\r\n  getTextContent(pageNumber: number): Observable<PdfTextContent> {\r\n    if (!this.isBrowser) {\r\n      return of({ pageNumber, items: [], styles: {} }); // Align with PdfTextContent.styles: object\r\n    }\r\n\r\n    // Check if the text content is already cached\r\n    if (this.textContentCache.has(pageNumber)) {\r\n      return of(this.textContentCache.get(pageNumber)!);\r\n    }\r\n\r\n    return from(this.ensurePdfJsLibLoaded()).pipe(\r\n      switchMap(() => {\r\n        if (!this.pdfjsLib) {\r\n           console.error('[PdfService] getTextContent: PDF.js library not loaded.');\r\n           throw new Error('PDF.js library not loaded for getTextContent');\r\n        }\r\n        if (!this.pdfDocument) {\r\n          console.error('[PdfService] getTextContent: PDF document not loaded.');\r\n          // Throw an error to be caught by the main catchError, which returns the empty state\r\n          throw new Error('PDF document not loaded for getTextContent');\r\n        }\r\n        return from(this.pdfDocument.getPage(pageNumber));\r\n      }), // This switchMap now correctly expects Observable<PDFPageProxy>\r\n      switchMap((page: PDFPageProxy) => { // Type page as PDFPageProxy\r\n        // if (!page) return of({ pageNumber, items: [], styles: {} }); // This check might be redundant if getPage always resolves or rejects\r\n        return from(page.getTextContent());\r\n      }),\r\n      map((textContent: TextContent) => { // Type textContent from pdfjs-dist\r\n        const result: PdfTextContent = {\r\n          pageNumber,\r\n          items: textContent.items as TextItem[],\r\n          styles: textContent.styles\r\n        };\r\n        this.textContentCache.set(pageNumber, result);\r\n        return result;\r\n      }),\r\n      catchError((error: Error) => {\r\n        console.error(`[PdfService] Error getting text content for page ${pageNumber}:`, error);\r\n        this.errorSubject.next(`Failed to extract text from page ${pageNumber}: ${error.message}`);\r\n        return of({ pageNumber, items: [], styles: {} });\r\n      })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Searches for text across all pages of the PDF\r\n   * @param searchText Text to search for\r\n   * @param options Search options (case sensitivity, etc.)\r\n   * @returns Observable of search results\r\n   */\r\n  searchText(searchText: string, options: { caseSensitive?: boolean, wholeWord?: boolean } = {}): Observable<PdfSearchResult[]> {\r\n    if (!this.isBrowser || !searchText.trim()) {\r\n      return of([]);\r\n    }\r\n\r\n    return from(this.ensurePdfJsLibLoaded()).pipe(\r\n      switchMap(() => {\r\n        if (!this.pdfjsLib) {\r\n          console.error('[PdfService] searchText: PDF.js library not loaded.');\r\n          throw new Error('PDF.js library not loaded for searchText');\r\n        }\r\n        if (!this.pdfDocument) {\r\n          console.error('[PdfService] searchText: PDF document not loaded.');\r\n          return of([]); // Or throw an error\r\n        }\r\n\r\n        const results: PdfSearchResult[] = [];\r\n        const searchTextRegex = this.createSearchRegex(searchText, options.caseSensitive, options.wholeWord);\r\n        const pagePromises: Promise<void>[] = [];\r\n\r\n        for (let i = 1; i <= this.pdfDocument.numPages; i++) {\r\n          const pageNum = i;\r\n          // Pass the loaded pdfjsLib instance to searchInPage\r\n          const pagePromise = this.searchInPage(pageNum, searchTextRegex) // Removed pdfjsLib and pdfDocument params\r\n            .then(pageResults => {\r\n              results.push(...pageResults);\r\n            })\r\n            .catch((error: Error) => {\r\n              console.error(`[PdfService] Error searching in page ${pageNum}:`, error);\r\n            });\r\n          pagePromises.push(pagePromise);\r\n        }\r\n        return from(Promise.all(pagePromises).then(() => results));\r\n      }),\r\n      catchError((error: Error) => {\r\n        console.error('[PdfService] Error in searchText observable chain:', error);\r\n        this.errorSubject.next(`Failed to search text: ${error.message}`);\r\n        return of([]);\r\n      })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Creates a regular expression for searching\r\n   * @param searchText The text to search for\r\n   * @param caseSensitive Whether the search is case-sensitive\r\n   * @param wholeWord Whether to match whole words only\r\n   * @returns A RegExp object\r\n   */\r\n  private createSearchRegex(searchText: string, caseSensitive?: boolean, wholeWord?: boolean): RegExp {\r\n    let regexPattern = searchText.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'); // Escape special characters\r\n\r\n    if (wholeWord) {\r\n      regexPattern = `\\\\b${regexPattern}\\\\b`;\r\n    }\r\n\r\n    return new RegExp(regexPattern, caseSensitive ? 'g' : 'gi');\r\n  }\r\n\r\n  /**\r\n   * Searches for text in a specific page\r\n   * @param pageNumber The page number (1-based)\r\n   * @param searchRegex The regular expression to search with\r\n   * @returns A Promise of search results\r\n   */\r\n  private async searchInPage(\r\n    pageNumber: number,\r\n    searchRegex: RegExp\r\n  ): Promise<PdfSearchResult[]> {\r\n    // Use this.pdfjsLib and this.pdfDocument directly\r\n    if (!this.pdfjsLib || !this.pdfDocument) {\r\n      console.error('[PdfService] searchInPage: PDF.js library or document not loaded.');\r\n      throw new Error('PDF.js library or document not loaded for searchInPage');\r\n    }\r\n    const pageResults: PdfSearchResult[] = [];\r\n    try {\r\n      // Get the text content for the page\r\n      let textContent: PdfTextContent;\r\n      if (this.textContentCache.has(pageNumber)) {\r\n        textContent = this.textContentCache.get(pageNumber)!;\r\n      } else {\r\n        const pageForText = await this.pdfDocument.getPage(pageNumber);\r\n        const content = await pageForText.getTextContent();\r\n        textContent = {\r\n          pageNumber,\r\n          items: content.items as TextItem[],\r\n          styles: content.styles\r\n        };\r\n        this.textContentCache.set(pageNumber, textContent);\r\n      }\r\n\r\n      // Get the viewport for the page to calculate positions\r\n      const pageForViewport = await this.pdfDocument.getPage(pageNumber);\r\n      const viewport = pageForViewport.getViewport({ scale: 1.0 });\r\n\r\n      // Combine text items to form lines of text\r\n      const lines: { text: string; items: TextItem[] }[] = [];\r\n      let currentLine: TextItem[] = [];\r\n      let lastY: number | null = null;\r\n\r\n      for (const item of textContent.items) {\r\n        // 'str' in item check is redundant as TextItem guarantees 'str'\r\n        if (item.str.trim() === '') continue;\r\n        if (lastY === null || Math.abs(item.transform[5] - lastY) < 2) {\r\n          // Same line\r\n          currentLine.push(item);\r\n        } else {\r\n          // New line\r\n          if (currentLine.length > 0) {\r\n            lines.push({\r\n              text: currentLine.map(i => i.str).join(''),\r\n              items: [...currentLine]\r\n            });\r\n          }\r\n          currentLine = [item]; // Always start the new line with the current item\r\n        }\r\n        lastY = item.transform[5];\r\n      }\r\n\r\n      // Add the last line\r\n      if (currentLine.length > 0) {\r\n        lines.push({\r\n          text: currentLine.map(i => i.str).join(''),\r\n          items: [...currentLine]\r\n        });\r\n      }\r\n\r\n      // Search in each line\r\n      let matchIndex = 0;\r\n      for (const line of lines) {\r\n        let match: RegExpExecArray | null;\r\n        searchRegex.lastIndex = 0; // Reset regex state\r\n\r\n        while ((match = searchRegex.exec(line.text)) !== null) {\r\n          const startIndex = match.index;\r\n          const endIndex = startIndex + match[0].length;\r\n\r\n          // Find the text items that contain the match\r\n          let currentPos = 0;\r\n          let startItem: TextItem | null = null;\r\n          let endItem: TextItem | null = null;\r\n\r\n          for (const item of line.items) {\r\n            const itemStart = currentPos;\r\n            const itemEnd = currentPos + item.str.length;\r\n\r\n            if (startItem === null && startIndex < itemEnd) {\r\n              startItem = item;\r\n            }\r\n\r\n            if (endItem === null && endIndex <= itemEnd) {\r\n              endItem = item;\r\n            }\r\n\r\n            if (startItem !== null && endItem !== null) {\r\n              break;\r\n            }\r\n\r\n            currentPos = itemEnd;\r\n          }\r\n\r\n          if (startItem && endItem) {\r\n            // Calculate the position of the match\r\n            const position = {\r\n              left: startItem.transform[4],\r\n              top: viewport.height - startItem.transform[5] - startItem.height,\r\n              right: endItem.transform[4] + endItem.width,\r\n              bottom: viewport.height - endItem.transform[5]\r\n            };\r\n\r\n            pageResults.push({\r\n              pageNumber,\r\n              matchIndex: matchIndex++,\r\n              text: match[0],\r\n              position\r\n            });\r\n          }\r\n        }\r\n      }\r\n\r\n      return pageResults;\r\n    } catch (error) {\r\n      console.error(`[PdfService] Error in searchInPage for page ${pageNumber}:`, error);\r\n      return [];\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Adds a highlight to the PDF\r\n   * @param pageNumber The page number (1-based)\r\n   * @param position The position of the highlight\r\n   * @param text The text being highlighted\r\n   * @param color The color of the highlight\r\n   * @returns The ID of the highlight\r\n   */\r\n  addHighlight(pageNumber: number, position: { left: number; top: number; right: number; bottom: number }, text: string, color: string = 'rgba(255, 255, 0, 0.3)'): string {\r\n    const id = `highlight-${Date.now()}-${Math.floor(Math.random() * 1000)}`;\r\n    const highlight: PdfHighlight = {\r\n      id,\r\n      pageNumber,\r\n      position,\r\n      color,\r\n      text\r\n    };\r\n\r\n    const currentHighlights = this.highlightsSubject.value;\r\n    this.highlightsSubject.next([...currentHighlights, highlight]);\r\n\r\n    return id;\r\n  }\r\n\r\n  /**\r\n   * Removes a highlight from the PDF\r\n   * @param id The ID of the highlight to remove\r\n   */\r\n  removeHighlight(id: string): void {\r\n    const currentHighlights = this.highlightsSubject.value;\r\n    this.highlightsSubject.next(currentHighlights.filter(h => h.id !== id));\r\n  }\r\n\r\n  /**\r\n   * Gets all highlights for a specific page\r\n   * @param pageNumber The page number (1-based)\r\n   * @returns An Observable of highlights for the page\r\n   */\r\n  getPageHighlights(pageNumber: number): Observable<PdfHighlight[]> {\r\n    return this.highlights$.pipe(\r\n      map(highlights => highlights.filter(h => h.pageNumber === pageNumber))\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Clears all highlights\r\n   */\r\n  clearHighlights(): void {\r\n    this.highlightsSubject.next([]);\r\n  }\r\n\r\n  /**\r\n   * Closes the current PDF document\r\n   */\r\n  closePdf(): void {\r\n    if (this.pdfDocument) {\r\n      this.pdfDocument.destroy();\r\n      this.pdfDocument = null;\r\n    }\r\n    this.pdfDocumentSubject.next(null);\r\n    this.pdfInfoSubject.next(null);\r\n    this.highlightsSubject.next([]); // Ensure highlights are cleared\r\n    this.pdfBase64DataUrlSubject.next(null); // Clear base64 data as well\r\n    this.clearCache(); // Clears text content cache and also pdfBase64DataUrlSubject\r\n    this.loadingSubject.next(false); // Reset loading state\r\n    // this.errorSubject.next(''); // Optionally clear errors, or let them persist\r\n    console.log('[PdfService] PDF closed and resources released.');\r\n  }\r\n\r\n  /**\r\n   * Converts an ArrayBuffer to a base64 data URL string.\r\n   * @param buffer The ArrayBuffer to convert.\r\n   * @returns A string representing the base64 data URL.\r\n   */\r\n  private arrayBufferToDataUrl(buffer: ArrayBuffer): string {\r\n    let binary = '';\r\n    const bytes = new Uint8Array(buffer);\r\n    const len = bytes.byteLength;\r\n    for (let i = 0; i < len; i++) {\r\n      binary += String.fromCharCode(bytes[i]);\r\n    }\r\n    const base64 = window.btoa(binary);\r\n    return `data:application/pdf;base64,${base64}`;\r\n  }\r\n}\r\n", "import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, AfterViewInit, Inject, PLATFORM_ID, NO_ERRORS_SCHEMA } from '@angular/core';\r\nimport { CommonModule, isPlatformBrowser } from '@angular/common';\r\nimport { NgxExtendedPdfViewerModule, NgxExtendedPdfViewerService, pdfDefaultOptions } from 'ngx-extended-pdf-viewer';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { PdfService } from '../../services/pdf.service'; // Removed PdfSearchResult as it's not used here\r\nimport { Subscription } from 'rxjs';\r\n\r\n@Component({\r\n  selector: 'app-pdf-viewer',\r\n  standalone: true,\r\n  imports: [CommonModule, NgxExtendedPdfViewerModule, FormsModule],\r\n  templateUrl: './pdf-viewer.component.html',\r\n  styleUrl: './pdf-viewer.component.scss',\r\n  schemas: [NO_ERRORS_SCHEMA] // Add this to suppress property binding errors\r\n})\r\nexport class PdfViewerComponent implements OnInit, AfterViewInit, OnDestroy {\r\n  // PDF viewer state\r\n  currentPage: number = 1;\r\n  totalPages: number = 0;\r\n  zoom: string | number = 'page-fit'; // Set to page-fit for default zoom\r\n\r\n  // Search state\r\n  searchText: string = '';\r\n  caseSensitive: boolean = false;\r\n  wholeWord: boolean = false;\r\n  // searchResults: PdfSearchResult[] = []; // Commented out as search functionality is not fully implemented here\r\n  // currentSearchResult: number = -1; // Commented out\r\n  isSearching: boolean = false;\r\n\r\n  // File state\r\n  currentFileName: string = '';\r\n\r\n  private subscriptions: Subscription = new Subscription();\r\n  public isBrowser: boolean = false; // Initialize with a default value\r\n  public pdfBase64Source: string | null = null; // Store the base64 source\r\n\r\n  // Configuration options\r\n  public scrollMode: number = 0; // 0 = vertical (default), 1 = horizontal, 2 = wrapped\r\n  public minZoom: number = 0.5; // Already in decimal format (50%)\r\n  public maxZoom: number = 2.0; // Already in decimal format (200%)\r\n  public renderTextMode: number = 2; // 0 = disabled, 1 = enabled, 2 = enhanced\r\n  public renderInteractiveForms: boolean = true;\r\n  public viewerPositionTop: number = 0;\r\n  public viewerHeight: string = '100%';\r\n\r\n  constructor(\r\n    private extPdfViewerService: NgxExtendedPdfViewerService, // Renamed to avoid conflict if PdfService is also named pdfService\r\n    private pdfServiceInstance: PdfService, // Injected PdfService\r\n    @Inject(PLATFORM_ID) private platformId: Object\r\n  ) {\r\n    this.isBrowser = isPlatformBrowser(this.platformId);\r\n\r\n    // Configure global PDF.js options\r\n    if (this.isBrowser) {\r\n      pdfDefaultOptions.disableStream = false;\r\n      pdfDefaultOptions.disableAutoFetch = false;\r\n      pdfDefaultOptions.defaultZoomValue = 'page-fit'; // Set default zoom to page-fit\r\n\r\n      // Configure annotation colors using type assertion to bypass TypeScript checking\r\n      // since annotationEditorParams might not be in the type definition but is supported at runtime\r\n      const annotationParams = {\r\n        // Text editor color (for adding text annotations)\r\n        defaultTextColor: '#FF0000',  // Red text\r\n\r\n        // Highlight color\r\n        defaultHighlightColor: '#FFFF00',  // Yellow highlight\r\n\r\n        // Drawing/pen color\r\n        defaultDrawColor: '#0000FF',  // Blue pen\r\n\r\n        // Ink thickness for drawing\r\n        defaultInkThickness: 3,\r\n\r\n        // Ink opacity (0-1)\r\n        defaultInkOpacity: 0.8\r\n      };\r\n\r\n      (pdfDefaultOptions as any).annotationEditorParams = annotationParams;\r\n\r\n      console.log('[PdfViewer] Configured annotation colors:', annotationParams);\r\n      console.log('[PdfViewer] Set default zoom to page-fit');\r\n    }\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    console.log('[PdfViewer] ngOnInit called');\r\n    console.log('[PdfViewer] Browser environment:', this.isBrowser ? 'Browser' : 'Server');\r\n    console.log('[PdfViewer] Initial zoom value:', this.zoom, '(decimal format)');\r\n    console.log('[PdfViewer] Scroll mode:', this.getScrollModeName(this.scrollMode));\r\n    console.log('[PdfViewer] Forms enabled:', this.renderInteractiveForms);\r\n\r\n    // Check if PDF.js worker is configured\r\n    if (this.isBrowser) {\r\n      console.log('[PdfViewer] PDF worker configuration:', (window as any).pdfWorkerSrc);\r\n\r\n      // Subscribe to the base64 data URL from PdfService\r\n      this.subscriptions.add(\r\n        this.pdfServiceInstance.pdfBase64DataUrl$.subscribe(dataUrl => {\r\n          if (dataUrl) {\r\n            console.log('[PdfViewer] Received base64 data URL from PdfService.');\r\n            this.pdfBase64Source = dataUrl;\r\n            // Extract file name if possible (optional, might need more robust parsing)\r\n            // For now, let's set a generic name or leave it as is if onFileSelected handles it.\r\n            if (!this.currentFileName && dataUrl.startsWith('data:application/pdf;base64,')) {\r\n                this.currentFileName = \"loaded_from_service.pdf\";\r\n            }\r\n          } else {\r\n            // Potentially clear the viewer if dataUrl is null (e.g., on pdfService.closePdf())\r\n            // However, ngx-extended-pdf-viewer might handle null src by showing nothing.\r\n            // If explicit clearing is needed:\r\n            // this.pdfBase64Source = null;\r\n            // this.currentFileName = '';\r\n            // this.totalPages = 0;\r\n            console.log('[PdfViewer] Received null base64 data URL from PdfService.');\r\n          }\r\n        })\r\n      );\r\n    }\r\n\r\n    // Log the version of ngx-extended-pdf-viewer being used\r\n    console.log('[PdfViewer] Using ngx-extended-pdf-viewer version 23.1.1');\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    console.log('[PdfViewer] ngAfterViewInit called');\r\n    console.log('[PdfViewer] PDF viewer ready for rendering');\r\n\r\n    // Add a small delay to check if the viewer is properly initialized\r\n    setTimeout(() => {\r\n      console.log('[PdfViewer] Delayed check - Zoom value:', this.zoom, '(decimal format)');\r\n      console.log('[PdfViewer] Viewer container dimensions:', this.getViewerContainerDimensions());\r\n\r\n      // Set default colors for annotation tools by directly manipulating the color inputs\r\n      this.setDefaultAnnotationColors();\r\n    }, 1000);\r\n  }\r\n\r\n  /**\r\n   * Sets the default colors for annotation tools by directly manipulating the color inputs\r\n   */\r\n  private setDefaultAnnotationColors(): void {\r\n    if (!this.isBrowser) return;\r\n\r\n    try {\r\n      // Text color\r\n      const textColorInput = document.getElementById('editorFreeTextColor') as HTMLInputElement;\r\n      if (textColorInput) {\r\n        textColorInput.value = '#FF0000'; // Red\r\n        console.log('[PdfViewer] Set text color input to:', textColorInput.value);\r\n\r\n        // Trigger change event to ensure the PDF viewer recognizes the change\r\n        textColorInput.dispatchEvent(new Event('input', { bubbles: true }));\r\n      } else {\r\n        console.warn('[PdfViewer] Text color input element not found');\r\n      }\r\n\r\n      // Draw/ink color\r\n      const drawColorInput = document.getElementById('editorInkColor') as HTMLInputElement;\r\n      if (drawColorInput) {\r\n        drawColorInput.value = '#0000FF'; // Blue\r\n        console.log('[PdfViewer] Set draw color input to:', drawColorInput.value);\r\n\r\n        // Trigger change event\r\n        drawColorInput.dispatchEvent(new Event('input', { bubbles: true }));\r\n      } else {\r\n        console.warn('[PdfViewer] Draw color input element not found');\r\n      }\r\n\r\n      // Highlight color\r\n      const highlightColorInput = document.getElementById('editorHighlightColor') as HTMLInputElement;\r\n      if (highlightColorInput) {\r\n        highlightColorInput.value = '#FFFF00'; // Yellow\r\n        console.log('[PdfViewer] Set highlight color input to:', highlightColorInput.value);\r\n\r\n        // Trigger change event\r\n        highlightColorInput.dispatchEvent(new Event('input', { bubbles: true }));\r\n      } else {\r\n        console.warn('[PdfViewer] Highlight color input element not found');\r\n      }\r\n    } catch (error) {\r\n      console.error('[PdfViewer] Error setting default annotation colors:', error);\r\n    }\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.subscriptions.unsubscribe();\r\n  }\r\n\r\n  /**\r\n   * Handles file selection from the file input\r\n   * @param event The file input change event\r\n   */\r\n  onFileSelected(event: Event): void {\r\n    // Ensure this only runs in the browser\r\n    if (!this.isBrowser) {\r\n      console.error('[PdfViewer] PDF loading attempted during server-side rendering');\r\n      return;\r\n    }\r\n\r\n    console.log('[PdfViewer] File selection event triggered');\r\n\r\n    const input = event.target as HTMLInputElement;\r\n    if (input.files && input.files.length > 0) {\r\n      const file = input.files[0];\r\n      console.log(`[PdfViewer] File selected: ${file.name}, type: ${file.type}, size: ${file.size} bytes`);\r\n\r\n      if (file.type === 'application/pdf') {\r\n        this.currentFileName = file.name;\r\n        this.pdfBase64Source = null; // Reset while loading\r\n        console.log('[PdfViewer] Valid PDF file selected, beginning conversion to base64');\r\n\r\n        // Read file as base64\r\n        const reader = new FileReader();\r\n\r\n        reader.onloadstart = () => {\r\n          console.log('[PdfViewer] FileReader: Started loading file');\r\n        };\r\n\r\n        reader.onprogress = (event) => {\r\n          if (event.lengthComputable) {\r\n            const percentLoaded = Math.round((event.loaded / event.total) * 100);\r\n            console.log(`[PdfViewer] FileReader: Loading progress: ${percentLoaded}%`);\r\n          }\r\n        };\r\n\r\n        reader.onload = (e) => {\r\n          console.log('[PdfViewer] FileReader: File successfully loaded');\r\n          const rawResult = e.target?.result as string;\r\n\r\n          this.pdfBase64Source = rawResult;\r\n\r\n          // Log the first 100 chars and length of the assigned base64 string\r\n          const base64Preview = this.pdfBase64Source?.substring(0, 100) + '...';\r\n          console.log(`[PdfViewer] Assigned pdfBase64Source length: ${this.pdfBase64Source?.length ?? 'null'}`);\r\n          console.log(`[PdfViewer] Assigned pdfBase64Source (preview): ${base64Preview}`);\r\n\r\n          // Check if it starts with the correct data URL prefix for PDFs\r\n          if (this.pdfBase64Source?.startsWith('data:application/pdf;base64,')) {\r\n            console.log('[PdfViewer] Base64 data has correct PDF MIME type prefix');\r\n          } else {\r\n            console.warn('[PdfViewer] Base64 data does not have expected PDF MIME type prefix');\r\n          }\r\n\r\n          console.log('[PdfViewer] PDF base64 source set, viewer should update');\r\n        };\r\n\r\n        reader.onerror = (error) => {\r\n          console.error('[PdfViewer] FileReader error:', error);\r\n          this.pdfBase64Source = null;\r\n          this.currentFileName = '';\r\n          this.totalPages = 0;\r\n          alert('Error reading the selected PDF file.');\r\n        };\r\n\r\n        console.log('[PdfViewer] Starting FileReader.readAsDataURL...');\r\n        reader.readAsDataURL(file);\r\n      } else {\r\n        this.pdfBase64Source = null;\r\n        this.currentFileName = '';\r\n        this.totalPages = 0;\r\n        console.error(`[PdfViewer] Invalid file type: ${file.type}, expected application/pdf`);\r\n        alert('Please select a valid PDF file.');\r\n      }\r\n    } else {\r\n      console.warn('[PdfViewer] No file selected or file input event without files');\r\n      this.pdfBase64Source = null;\r\n      this.currentFileName = '';\r\n      this.totalPages = 0;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Loads a PDF from a File object\r\n   * @param file The PDF file to load\r\n   */\r\n  /**\r\n   * Handles PDF loaded event\r\n   * @param pdf The loaded PDF document\r\n   */\r\n  onPdfLoaded(pdf: any): void {\r\n    console.log('[PdfViewer] PDF loaded event triggered');\r\n    console.log('[PdfViewer] PDF document:', pdf);\r\n\r\n    if (pdf && pdf.numPages) {\r\n      this.totalPages = pdf.numPages;\r\n      console.log(`[PdfViewer] PDF loaded with ${this.totalPages} pages`);\r\n    }\r\n\r\n    // Log viewer dimensions after PDF is loaded\r\n    setTimeout(() => {\r\n      console.log('[PdfViewer] Viewer dimensions after PDF load:', this.getViewerContainerDimensions());\r\n      console.log('[PdfViewer] Current zoom after PDF load:', this.zoom, '(decimal format)');\r\n    }, 500);\r\n  }\r\n\r\n  /**\r\n   * Handles page change events\r\n   * @param pageNumber The new page number\r\n   */\r\n  onPageChange(pageNumber: number): void {\r\n    console.log(`[PdfViewer] Page changed to ${pageNumber}`);\r\n    this.currentPage = pageNumber;\r\n  }\r\n\r\n  /**\r\n   * Handles zoom change events from the PDF viewer\r\n   * @param newZoom New zoom level (as a decimal, e.g., 1.0 for 100%)\r\n   */\r\n  onZoomChange(newZoom: string | number): void {\r\n    // We still need to update our internal zoom property for the [zoom] binding\r\n    // but we no longer display it in the UI or provide external controls\r\n    const zoomValue = typeof newZoom === 'string' ? parseFloat(newZoom) : newZoom;\r\n    console.log(`[PdfViewer] PDF viewer zoom changed to ${zoomValue}`);\r\n\r\n    // Store the zoom value without multiplying by 100\r\n    // This prevents the scaling issue where 1.9 became 190%\r\n    this.zoom = zoomValue;\r\n  }\r\n\r\n  /**\r\n   * Performs a search across the PDF\r\n   */\r\n\r\n  /**\r\n   * Handles PDF viewer errors\r\n   * @param error The error object from the PDF viewer\r\n   */\r\n  /**\r\n   * Handles PDF viewer errors\r\n   * @param error The error object from the PDF viewer\r\n   */\r\n  onPdfViewerError(error: any): void {\r\n    console.error('[PdfViewer] PDF Viewer Error:', error);\r\n    // Log additional context that might help diagnose the issue\r\n    console.error('[PdfViewer] PDF Viewer Context:', {\r\n      hasBase64Source: !!this.pdfBase64Source,\r\n      base64SourceLength: this.pdfBase64Source ? this.pdfBase64Source.length : 0,\r\n      base64SourcePrefix: this.pdfBase64Source ? this.pdfBase64Source.substring(0, 50) : 'N/A',\r\n      fileName: this.currentFileName,\r\n      totalPages: this.totalPages,\r\n      workerSrc: this.isBrowser ? (window as any).pdfWorkerSrc : 'N/A',\r\n      viewerDimensions: this.getViewerContainerDimensions(),\r\n      scrollMode: this.scrollMode,\r\n      scrollModeName: this.getScrollModeName(this.scrollMode),\r\n      renderInteractiveForms: this.renderInteractiveForms,\r\n      ngxExtendedPdfViewerVersion: '23.1.1'\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Gets the name of the scroll mode\r\n   * @param mode The scroll mode number\r\n   * @returns The name of the scroll mode\r\n   */\r\n  private getScrollModeName(mode: number): string {\r\n    switch (mode) {\r\n      case 0: return 'Vertical';\r\n      case 1: return 'Horizontal';\r\n      case 2: return 'Wrapped';\r\n      default: return 'Unknown';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Gets the dimensions of the viewer container\r\n   * @returns An object with the width and height of the viewer container\r\n   */\r\n  private getViewerContainerDimensions(): { width: string, height: string } {\r\n    if (!this.isBrowser) {\r\n      return { width: 'N/A', height: 'N/A' };\r\n    }\r\n\r\n    const container = document.querySelector('.pdf-viewer-wrapper') as HTMLElement;\r\n    if (!container) {\r\n      return { width: 'Container not found', height: 'Container not found' };\r\n    }\r\n\r\n    return {\r\n      width: `${container.clientWidth}px`,\r\n      height: `${container.clientHeight}px`\r\n    };\r\n  }\r\n}\r\n", "<div class=\"pdf-viewer-container\">\r\n  <!-- PDF Viewer -->\r\n  <div class=\"pdf-viewer-wrapper\">\r\n    <ngx-extended-pdf-viewer *ngIf=\"isBrowser && pdfBase64Source\"\r\n      [src]=\"pdfBase64Source\"\r\n      [(page)]=\"currentPage\"\r\n      [zoom]=\"zoom\"\r\n      [autoScale]=\"'page-fit'\"\r\n      [defaultZoom]=\"'page-fit'\"\r\n      (zoomChange)=\"onZoomChange($event)\"\r\n      (pdfLoaded)=\"onPdfLoaded($event)\"\r\n      (pageChange)=\"onPageChange($event)\"\r\n      [height]=\"viewerHeight\"\r\n      [language]=\"'en-US'\"\r\n      [showToolbar]=\"true\"\r\n      [showSidebarButton]=\"true\"\r\n      [showFindButton]=\"true\"\r\n      [showPagingButtons]=\"true\"\r\n      [showZoomButtons]=\"true\"\r\n      [showPresentationModeButton]=\"true\"\r\n      [showOpenFileButton]=\"true\"\r\n      [showPrintButton]=\"true\"\r\n      [showDownloadButton]=\"true\"\r\n      [showBookmodeButton]=\"'always-in-secondary-menu'\"\r\n      [showSecondaryToolbarButton]=\"true\"\r\n      [showRotateButton]=\"true\"\r\n      [showHandToolButton]=\"true\"\r\n      [showScrollingButtons]=\"'always-in-secondary-menu'\"\r\n      [showSpreadButton]=\"true\"\r\n      [showPropertiesButton]=\"true\"\r\n      [scrollMode]=\"scrollMode\"\r\n      [disableForms]=\"!renderInteractiveForms\"\r\n      [minZoom]=\"minZoom\"\r\n      [maxZoom]=\"maxZoom\"\r\n      (error)=\"onPdfViewerError($event)\"\r\n    ></ngx-extended-pdf-viewer>\r\n\r\n    <!-- Message when no PDF is loaded -->\r\n    <div *ngIf=\"isBrowser && !pdfBase64Source\" class=\"no-pdf-message\">\r\n      <p>Please select a PDF file to view.</p>\r\n    </div>\r\n\r\n    <!-- Message when not in browser -->\r\n    <div *ngIf=\"!isBrowser\" class=\"ssr-placeholder\">\r\n      PDF Viewer is not available during server-side rendering.\r\n    </div>\r\n  </div>\r\n</div>\r\n", "import { Component, OnInit, inject, ViewChild, PLATFORM_ID } from '@angular/core'; // Import inject, ViewChild, PLATFORM_ID\r\nimport { ActivatedRoute, RouterModule, Router } from '@angular/router'; // Import RouterModule and Router\r\nimport { CommonModule, isPlatformBrowser } from '@angular/common'; // Import isPlatformBrowser\r\nimport { FormsModule } from '@angular/forms';\r\nimport { PdfViewerComponent } from '@features/chart-review/components/pdf-viewer/pdf-viewer.component';\r\nimport { MenuComponent, UserProfile, MenuItem } from '@shared/components/menu/menu.component';\r\nimport { HitsComponent, HitData } from '@shared/components/hits/hits.component';\r\nimport { ResultsComponent, ResultsData } from '@shared/components/form-controls/results/results.component';\r\nimport { ButtonComponent } from '@shared/components/buttons/button.component';\r\nimport { DemographicsComponent, DemographicsData } from '@shared/components/demographics/demographics.component';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { PdfService } from '@features/chart-review/services/pdf.service'; // Import PdfService\r\n\r\ninterface Finding {\r\n  id: string;\r\n  type: string;\r\n  text: string;\r\n  confidence: number;\r\n  pageNumber: number;\r\n  validated: boolean;\r\n  isValid?: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-chart-review-page',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    PdfViewerComponent,\r\n    FormsModule,\r\n    MenuComponent,\r\n    HitsComponent,\r\n    ResultsComponent,\r\n    ButtonComponent,\r\n    DemographicsComponent,\r\n    RouterModule\r\n  ],\r\n  templateUrl: './chart-review-page.component.html',\r\n  styleUrl: './chart-review-page.component.scss'\r\n})\r\nexport class ChartReviewPageComponent implements OnInit {\r\n  @ViewChild(PdfViewerComponent) pdfViewerComponent!: PdfViewerComponent;\r\n\r\n  chartId: string = '';\r\n  selectedFinding: Finding | null = null;\r\n\r\n  // Demographics data for the patient info header (will be updated with route data)\r\n  demographicsData: DemographicsData = {\r\n    measureTitle: 'Controlling Blood Pressure (CBP)',\r\n    measureSubtitle: 'Measure',\r\n    memberId: '',\r\n    memberName: '',\r\n    dateOfBirth: '',\r\n    gender: '',\r\n    lob: '',\r\n    providerName: '',\r\n    npi: ''\r\n  };\r\n\r\n  // Navigation data\r\n  userProfile: UserProfile = {\r\n    name: 'Jane Chu',\r\n    avatar: ''\r\n  };\r\n\r\n  menuItems: MenuItem[] = [\r\n    { label: 'Dashboard', route: '/dashboard', icon: '🏠' },\r\n    { label: 'Profile', route: '/profile', icon: '👤' },\r\n    { label: 'Settings', route: '/settings', icon: '⚙️' },\r\n    { label: 'Help', route: '/help', icon: '❓' },\r\n    { label: 'Logout', action: () => this.logout(), icon: '🚪' }\r\n  ];\r\n\r\n  // Hits data for the hits component\r\n  hitsData: HitData[] = [\r\n    {\r\n      id: 'hit-1',\r\n      dateOfService: '07/22/24',\r\n      systolic: 136,\r\n      diastolic: 82,\r\n      page: 2,\r\n      comment: '',\r\n      include: false\r\n    },\r\n    {\r\n      id: 'hit-2',\r\n      dateOfService: '07/22/24',\r\n      systolic: 140,\r\n      diastolic: 82,\r\n      page: 2,\r\n      comment: '',\r\n      include: false\r\n    },\r\n    {\r\n      id: 'hit-3',\r\n      dateOfService: '05/22/24',\r\n      systolic: 150,\r\n      diastolic: 90,\r\n      page: 7,\r\n      comment: '',\r\n      include: false\r\n    }\r\n  ];\r\n\r\n  // Results data for the results component\r\n  resultsData: ResultsData = {\r\n    category: 'inclusions',\r\n    telehealth: false,\r\n    sys: '',\r\n    dias: '',\r\n    dateOfService: '',\r\n    notes: ''\r\n  };\r\n\r\n\r\n  // zoom property removed as it's no longer needed\r\n  // TODO: Determine how selectedFinding should be set now that the sidebar is removed.\r\n  // It might come from interactions with the PDF viewer or another source.\r\n\r\n  private route = inject(ActivatedRoute);\r\n  private router = inject(Router);\r\n  private pdfService = inject(PdfService); // Inject PdfService\r\n  private http = inject(HttpClient); // Inject HttpClient for loading default PDF\r\n  private platformId = inject(PLATFORM_ID); // Inject platform ID for browser detection\r\n\r\n  // Remove the constructor if only used for DI before\r\n\r\n  ngOnInit(): void {\r\n    // Get chart ID from route parameters\r\n    this.chartId = this.route.snapshot.paramMap.get('id') || '';\r\n\r\n    if (this.chartId) {\r\n      console.log(`[ChartReviewPage] Attempting to load chart with ID: ${this.chartId}`);\r\n      // Construct the path to the PDF within the application's assets folder.\r\n      // This is how local files bundled with the app are accessed.\r\n      const pdfUrl = `assets/charts/${this.chartId}.pdf`;\r\n\r\n      // Use pdfService.loadPdf(url) for loading PDFs from the assets folder.\r\n      // HttpClient (used by loadPdf) can fetch these local asset files.\r\n      this.pdfService.loadPdf(pdfUrl).subscribe({\r\n        next: (pdfDoc) => {\r\n          console.log(`[ChartReviewPage] PDF loaded successfully from asset path: ${pdfUrl}, Pages: ${pdfDoc.numPages}`);\r\n          // PdfViewerComponent should automatically react to the PdfService's BehaviorSubject update.\r\n        },\r\n        error: (err) => {\r\n          console.error(`[ChartReviewPage] Error loading PDF from asset path (${pdfUrl}):`, err);\r\n          // TODO: Display a user-friendly error message in the UI.\r\n          // For example, set an error message property and bind it in the template.\r\n        }\r\n      });\r\n    } else {\r\n      console.warn('[ChartReviewPage] No chart ID found in route parameters. Cannot load PDF.');\r\n      // TODO: Optionally, display a message to the user that no chart ID was provided.\r\n    }\r\n\r\n    // Update demographics data with member information\r\n    this.updateDemographicsData();\r\n\r\n    console.log('[ChartReviewPage] Demographics data initialized:', this.demographicsData);\r\n\r\n    // Load PDF based on member ID from route\r\n    this.loadPdfForMember();\r\n\r\n    // TODO: Initialize selectedFinding based on the new selection mechanism if needed.\r\n    // this.selectedFinding = ...;\r\n  }\r\n\r\n  /**\r\n   * Handles the file selection event from an input element.\r\n   * This method is for manual file uploads by the user, not for loading charts by ID.\r\n   * Commenting out for now as the primary task is to load by chartId.\r\n   */\r\n  /*\r\n  onFileSelected(event: Event): void {\r\n    console.log('[ChartReviewPage] onFileSelected method called.');\r\n    const input = event.target as HTMLInputElement;\r\n    if (input.files && input.files.length > 0) {\r\n      const file = input.files[0];\r\n      console.log(`[ChartReviewPage] File selected: ${file.name}`);\r\n      // loadPdfFromFile is used when a user explicitly selects a file using a file input.\r\n      this.pdfService.loadPdfFromFile(file).subscribe({\r\n        next: (pdfDoc) => {\r\n          console.log(`[ChartReviewPage] PDF loaded successfully by service (from file input): ${pdfDoc.numPages} pages`);\r\n        },\r\n        error: (err) => {\r\n          console.error('[ChartReviewPage] Error loading PDF via service (from file input):', err);\r\n        }\r\n      });\r\n    } else {\r\n      console.log('[ChartReviewPage] No file selected via file input.');\r\n    }\r\n  }\r\n  */\r\n\r\n  // Zoom methods removed as requested\r\n\r\n  /**\r\n   * Navigates the PDF viewer to a specific page.\r\n   * @param pageNumber The page number to navigate to.\r\n   */\r\n  public goToPdfPage(pageNumber: number): void {\r\n    if (this.pdfViewerComponent) {\r\n      console.log(`[ChartReviewPage] Navigating to PDF page: ${pageNumber}`);\r\n      this.pdfViewerComponent.currentPage = pageNumber;\r\n    } else {\r\n      console.warn('[ChartReviewPage] goToPdfPage called, but pdfViewerComponent is not available.');\r\n    }\r\n  }\r\n\r\n  // Navigation event handlers\r\n  onLogoClick(): void {\r\n    console.log('Logo clicked');\r\n    this.router.navigate(['/dashboard']);\r\n  }\r\n\r\n  onUserClick(): void {\r\n    console.log('User clicked');\r\n  }\r\n\r\n  onDropdownToggle(isOpen: boolean): void {\r\n    console.log('Dropdown toggled:', isOpen);\r\n  }\r\n\r\n  onMenuItemClick(item: MenuItem): void {\r\n    console.log('Menu item clicked:', item);\r\n    if (item.route) {\r\n      this.router.navigate([item.route]);\r\n    } else if (item.action) {\r\n      item.action();\r\n    }\r\n  }\r\n\r\n  logout(): void {\r\n    console.log('Logout clicked');\r\n    this.router.navigate(['/']);\r\n  }\r\n\r\n  // Demographics component event handler\r\n  onDemographicsBackClick(): void {\r\n    console.log('Demographics back button clicked');\r\n    this.router.navigate(['/dashboard']);\r\n  }\r\n\r\n  // Update demographics data based on member ID\r\n  private updateDemographicsData(): void {\r\n    const memberId = this.chartId;\r\n    console.log(`[ChartReviewPage] Updating demographics for member ID: ${memberId}`);\r\n\r\n    // Update member ID\r\n    this.demographicsData.memberId = memberId || 'Unknown';\r\n\r\n    // Set member-specific data based on member ID\r\n    if (memberId === '55820474') {\r\n      this.demographicsData.memberName = 'John Dey';\r\n      this.demographicsData.dateOfBirth = '01/05/1972';\r\n      this.demographicsData.gender = 'M';\r\n      this.demographicsData.lob = 'PPO';\r\n      this.demographicsData.providerName = 'Nicolas Dejong PA';\r\n      this.demographicsData.npi = '882716229';\r\n    } else {\r\n      // Default data for other member IDs\r\n      this.demographicsData.memberName = 'Sample Patient';\r\n      this.demographicsData.dateOfBirth = '01/01/1970';\r\n      this.demographicsData.gender = 'U';\r\n      this.demographicsData.lob = 'HMO';\r\n      this.demographicsData.providerName = 'Dr. Sample Provider';\r\n      this.demographicsData.npi = '123456789';\r\n    }\r\n\r\n    console.log('[ChartReviewPage] Demographics updated:', this.demographicsData);\r\n  }\r\n\r\n  // Load PDF based on member ID from route parameters\r\n  private loadPdfForMember(): void {\r\n    const memberId = this.chartId; // chartId is set from route parameters\r\n    console.log(`[ChartReviewPage] Loading PDF for member ID: ${memberId}`);\r\n\r\n    if (!memberId) {\r\n      console.warn('[ChartReviewPage] No member ID available, loading default PDF');\r\n      this.loadFallbackPdf();\r\n      return;\r\n    }\r\n\r\n    // Try to load the specific PDF for this member ID\r\n    const pdfPath = `assets/charts/${memberId}.pdf`;\r\n    console.log(`[ChartReviewPage] Attempting to load PDF from: ${pdfPath}`);\r\n\r\n    this.http.get(pdfPath, { responseType: 'blob' })\r\n      .subscribe({\r\n        next: (blob) => {\r\n          console.log(`[ChartReviewPage] PDF for member ${memberId} loaded successfully`);\r\n          this.convertBlobToDataUrlAndLoad(blob);\r\n        },\r\n        error: (error) => {\r\n          console.error(`[ChartReviewPage] Error loading PDF for member ${memberId}:`, error);\r\n          console.log('[ChartReviewPage] Falling back to default PDF...');\r\n          this.loadFallbackPdf();\r\n        }\r\n      });\r\n  }\r\n\r\n  // Load fallback PDF when member-specific PDF is not available\r\n  private loadFallbackPdf(): void {\r\n    console.log('[ChartReviewPage] Loading fallback PDF...');\r\n\r\n    // Load the CBP redacted usability PDF as fallback\r\n    this.http.get('assets/charts/CBP_Redacted_Usability.pdf', { responseType: 'blob' })\r\n      .subscribe({\r\n        next: (blob) => {\r\n          console.log('[ChartReviewPage] Fallback PDF loaded successfully');\r\n          this.convertBlobToDataUrlAndLoad(blob);\r\n        },\r\n        error: (error) => {\r\n          console.error('[ChartReviewPage] Error loading fallback PDF:', error);\r\n          console.log('[ChartReviewPage] Trying final fallback PDF...');\r\n\r\n          // Try the other PDF as final fallback\r\n          this.http.get('assets/charts/55820474.pdf', { responseType: 'blob' })\r\n            .subscribe({\r\n              next: (blob) => {\r\n                console.log('[ChartReviewPage] Final fallback PDF loaded successfully');\r\n                this.convertBlobToDataUrlAndLoad(blob);\r\n              },\r\n              error: (altError) => {\r\n                console.error('[ChartReviewPage] Error loading final fallback PDF:', altError);\r\n                console.error('[ChartReviewPage] No PDFs available to load');\r\n              }\r\n            });\r\n        }\r\n      });\r\n  }\r\n\r\n  // Helper method to convert blob to data URL and load into PDF service\r\n  private convertBlobToDataUrlAndLoad(blob: Blob): void {\r\n    // Check if we're in a browser environment before using FileReader\r\n    if (!isPlatformBrowser(this.platformId)) {\r\n      console.log('[ChartReviewPage] Not in browser environment, skipping FileReader operation');\r\n      return;\r\n    }\r\n\r\n    // FileReader is only available in browser environment\r\n    if (typeof FileReader === 'undefined') {\r\n      console.error('[ChartReviewPage] FileReader is not available in this environment');\r\n      return;\r\n    }\r\n\r\n    const reader = new FileReader();\r\n    reader.onload = () => {\r\n      const dataUrl = reader.result as string;\r\n      console.log('[ChartReviewPage] PDF converted to base64, loading into viewer');\r\n\r\n      // Use the PDF service to load the PDF\r\n      this.pdfService.loadPdfFromDataUrl(dataUrl).subscribe({\r\n        next: (pdfDoc) => {\r\n          console.log(`[ChartReviewPage] PDF loaded successfully: ${pdfDoc.numPages} pages`);\r\n        },\r\n        error: (error) => {\r\n          console.error('[ChartReviewPage] Error loading PDF via service:', error);\r\n        }\r\n      });\r\n    };\r\n    reader.readAsDataURL(blob);\r\n  }\r\n\r\n  // Hits component event handlers\r\n  onHitsDataChange(data: HitData[]): void {\r\n    console.log('Hits data changed:', data);\r\n    this.hitsData = data;\r\n  }\r\n\r\n  onHitsPageClick(event: { hit: HitData, page: number }): void {\r\n    console.log('Hits page clicked:', event);\r\n    this.goToPdfPage(event.page);\r\n  }\r\n\r\n  onHitsCommentChange(event: { hit: HitData, comment: string }): void {\r\n    console.log('Hits comment changed:', event);\r\n    const hitIndex = this.hitsData.findIndex(hit => hit.id === event.hit.id);\r\n    if (hitIndex !== -1) {\r\n      this.hitsData[hitIndex].comment = event.comment;\r\n    }\r\n  }\r\n\r\n  onHitsIncludeChange(event: { hit: HitData, include: boolean }): void {\r\n    console.log('Hits include changed:', event);\r\n    const hitIndex = this.hitsData.findIndex(hit => hit.id === event.hit.id);\r\n    if (hitIndex !== -1) {\r\n      this.hitsData[hitIndex].include = event.include;\r\n    }\r\n  }\r\n\r\n  // Results component event handlers\r\n  onResultsDataChange(data: ResultsData): void {\r\n    console.log('Results data changed:', data);\r\n    this.resultsData = data;\r\n  }\r\n\r\n  // Complete review action\r\n  onCompleteReview(): void {\r\n    console.log('Complete review clicked');\r\n    console.log('Results data:', this.resultsData);\r\n    console.log('Hits data:', this.hitsData);\r\n    // Add logic to save/submit the review\r\n  }\r\n}\r\n", "<div class=\"container\">\r\n  <!-- Menu -->\r\n  <app-menu\r\n    logoSrc=\"assets/logos/Stellarus_logo_2C_blacktype.png?v=2024\"\r\n    logoAlt=\"Stellarus Logo\"\r\n    [user]=\"userProfile\"\r\n    [menuItems]=\"menuItems\"\r\n    (logoClick)=\"onLogoClick()\"\r\n    (userClick)=\"onUserClick()\"\r\n    (dropdownToggle)=\"onDropdownToggle($event)\"\r\n    (menuItemClick)=\"onMenuItemClick($event)\">\r\n  </app-menu>\r\n\r\n  <div class=\"main-content\">\r\n    <!-- Patient Demographics Header - spans full width -->\r\n    <div class=\"demographics-section\">\r\n      <app-demographics\r\n        [data]=\"demographicsData\"\r\n        [showBackButton]=\"true\"\r\n        backButtonText=\"Back\"\r\n        (backClick)=\"onDemographicsBackClick()\">\r\n      </app-demographics>\r\n    </div>\r\n\r\n    <!-- Two-column layout matching Figma mockup -->\r\n    <div class=\"content-layout\">\r\n      <!-- Left Column: PDF Viewer -->\r\n      <div class=\"pdf-column\">\r\n        <app-pdf-viewer></app-pdf-viewer>\r\n      </div>\r\n\r\n      <!-- Right Column: Hits and Results -->\r\n      <div class=\"right-column\">\r\n        <!-- Hits Section -->\r\n        <div class=\"hits-section\">\r\n          <app-hits\r\n            title=\"Hits\"\r\n            [data]=\"hitsData\"\r\n            (dataChange)=\"onHitsDataChange($event)\"\r\n            (pageClick)=\"onHitsPageClick($event)\"\r\n            (commentChange)=\"onHitsCommentChange($event)\"\r\n            (includeChange)=\"onHitsIncludeChange($event)\">\r\n          </app-hits>\r\n        </div>\r\n\r\n        <!-- Results Section -->\r\n        <div class=\"results-section\">\r\n          <app-results\r\n            title=\"Results\"\r\n            [(ngModel)]=\"resultsData\"\r\n            (dataChange)=\"onResultsDataChange($event)\">\r\n          </app-results>\r\n        </div>\r\n\r\n        <!-- Submit Button -->\r\n        <div class=\"submit-section\">\r\n          <app-button\r\n            variant=\"primary\"\r\n            (buttonClick)=\"onCompleteReview()\">\r\n            Submit\r\n          </app-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n", "import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { ChartReviewPageComponent } from '@features/chart-review/pages/chart-review-page/chart-review-page.component';\r\nimport { PdfViewerTestComponent } from '@features/chart-review/components/pdf-viewer-test/pdf-viewer-test.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: ':id', // Changed from '' to ':id' to accept a route parameter\r\n    component: ChartReviewPageComponent\r\n  },\r\n  {\r\n    path: 'pdf-test',\r\n    component: PdfViewerTestComponent\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class ChartReviewRoutingModule { }\r\n", "import { Component } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\n@Component({\r\n  selector: 'app-annotation',\r\n  standalone: true,\r\n  imports: [CommonModule],\r\n  templateUrl: './annotation.component.html',\r\n  styleUrl: './annotation.component.scss'\r\n})\r\nexport class AnnotationComponent {\r\n\r\n}\r\n", "<p>annotation works!</p>\r\n", "import { Component } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\n@Component({\r\n  selector: 'app-validation',\r\n  standalone: true,\r\n  imports: [CommonModule],\r\n  templateUrl: './validation.component.html',\r\n  styleUrl: './validation.component.scss'\r\n})\r\nexport class ValidationComponent {\r\n\r\n}\r\n", "<p>validation works!</p>\r\n", "import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NgxExtendedPdfViewerModule } from 'ngx-extended-pdf-viewer';\r\n\r\nimport { ChartReviewRoutingModule } from './chart-review-routing.module';\r\nimport { PdfViewerComponent } from './components/pdf-viewer/pdf-viewer.component';\r\nimport { PdfViewerTestComponent } from './components/pdf-viewer-test/pdf-viewer-test.component';\r\nimport { AnnotationComponent } from './components/annotation/annotation.component';\r\nimport { ValidationComponent } from './components/validation/validation.component';\r\nimport { ChartReviewPageComponent } from './pages/chart-review-page/chart-review-page.component';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    // All components are now standalone, so no declarations needed\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    NgxExtendedPdfViewerModule,\r\n    ChartReviewRoutingModule,\r\n    // Import all standalone components\r\n    PdfViewerComponent,\r\n    PdfViewerTestComponent,\r\n    AnnotationComponent,\r\n    ValidationComponent,\r\n    ChartReviewPageComponent\r\n  ],\r\n  exports: [\r\n    // No need to export components that are imported\r\n  ]\r\n})\r\nexport class ChartReviewModule { }\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkFM,IAAO,aAAP,MAAO,YAAU;EA4CX;EACqB;EA5CvB,cAAuC;EACvC,qBAAqB,IAAI,gBAAyC,IAAI;EACtE,iBAAiB,IAAI,gBAAoC,IAAI;EAC7D,mBAAmB,oBAAI,IAAG;EAC1B,oBAAoB,IAAI,gBAAgC,CAAA,CAAE;EAC1D,0BAA0B,IAAI,gBAA+B,IAAI;;EACjE,iBAAiB,IAAI,gBAAyB,KAAK;EACnD,eAAe,IAAI,QAAO;EAC1B;EACA,WAA4B;EAC5B,kBAAmD;;;;;EAKpD,eAAe,KAAK,mBAAmB,aAAY;;;;EAKnD,WAAW,KAAK,eAAe,aAAY;;;;EAK3C,cAAc,KAAK,kBAAkB,aAAY;;;;EAKjD,oBAAoB,KAAK,wBAAwB,aAAY;;;;EAK7D,WAAW,KAAK,eAAe,aAAY;;;;EAK3C,SAAS,KAAK,aAAa,aAAY;EAE9C,YACU,MACqB,YAAkB;AADvC,SAAA,OAAA;AACqB,SAAA,aAAA;AAE7B,SAAK,YAAY,kBAAkB,KAAK,UAAU;AAGlD,QAAI,KAAK,WAAW;AAClB,WAAK,kBAAkB,KAAK,iBAAgB;IAC9C;EACF;;;;;;EAOc,mBAAgB;;AAC5B,UAAI;AACF,gBAAQ,IAAI,iDAAiD;AAE7D,cAAM,QAAQ,MAAM,OAAO,qBAAY;AACvC,aAAK,WAAW;AAGhB,aAAK,SAAS,oBAAoB,YAAY;AAC9C,gBAAQ,IAAI,uDAAuD;AACnE,eAAO,KAAK;MACd,SAAS,OAAO;AACd,gBAAQ,MAAM,8CAA8C,KAAK;AACjE,cAAM,eAAe,iBAAiB,QAAQ,MAAM,UAAU,OAAO,KAAK;AAC1E,aAAK,aAAa,KAAK,4CAA4C,YAAY,EAAE;AACjF,aAAK,WAAW;AAChB,eAAO;MACT;IACF;;;;;;EAMc,uBAAoB;;AAChC,UAAI,KAAK,UAAU;AACjB,eAAO,QAAQ,QAAO;MACxB;AACA,UAAI,KAAK,iBAAiB;AACxB,cAAM,KAAK;AACX,YAAI,CAAC,KAAK,UAAU;AAClB,gBAAM,UAAU;AAChB,kBAAQ,MAAM,gBAAgB,OAAO,EAAE;AACvC,eAAK,aAAa,KAAK,aAAa,OAAO,EAAE;AAC7C,gBAAM,IAAI,MAAM,OAAO;QACzB;AACA,eAAO,QAAQ,QAAO;MACxB;AAEA,cAAQ,MAAM,yDAAyD;AACvE,YAAM,IAAI,MAAM,4CAA4C;IAC9D;;;;;EAKO,aAAU;AACf,SAAK,iBAAiB,MAAK;AAC3B,SAAK,wBAAwB,KAAK,IAAI;EACxC;;;;;;EAOA,QAAQ,KAAW;AACjB,YAAQ,IAAI,yCAAyC,GAAG;AAExD,QAAI,CAAC,KAAK,WAAW;AACnB,cAAQ,IAAI,mEAAmE;AAC/E,aAAO,GAAG,EAAE,UAAU,GAAG,SAAS,MAAM,QAAQ,OAAO,4CAA4C,EAAC,CAAE;IACxG;AAEA,SAAK,eAAe,KAAK,IAAI;AAC7B,SAAK,WAAU;AAEf,WAAO,KAAK,KAAK,qBAAoB,CAAE,EAAE;MACvC,UAAU,MAAK;AAEb,YAAI,CAAC,KAAK,UAAU;AAElB,kBAAQ,MAAM,4FAA4F;AAC1G,gBAAM,IAAI,MAAM,+CAA+C;QACjE;AACA,gBAAQ,IAAI,kDAAkD,GAAG;AACjE,eAAO,KAAK,KAAK,IAAI,KAAK,EAAE,cAAc,cAAa,CAAE,EAAE,KACzD,IAAI,iBAAc;AAEhB,gBAAM,UAAU,KAAK,qBAAqB,WAAW;AACrD,eAAK,wBAAwB,KAAK,OAAO;QAC3C,CAAC,CAAC;MAEN,CAAC;MACD,UAAU,UAAO;AAEf,YAAI;AACF,iBAAO,KAAK,KAAK,SAAU,YAAY,EAAE,KAAI,CAAE,EAAE,OAAO;QAC1D,SAAS,OAAO;AACd,kBAAQ,MAAM,4DAA4D,KAAK;AAC/E,eAAK,aAAa,KAAK,4BAA4B;AACnD,gBAAM;QACR;MACF,CAAC;;MACD,IAAI,CAAC,gBAAiC;AACpC,aAAK,cAAc;AACnB,aAAK,mBAAmB,KAAK,WAAW;AAGxC,cAAM,eAA4B;UAChC,UAAU,YAAY;UACtB,SAAS,CAAC,eAAuB,YAAY,QAAQ,UAAU;;AAGjE,aAAK,eAAe,KAAK,YAAY;AACrC,aAAK,eAAe,KAAK,KAAK;AAC9B,eAAO;MACT,CAAC;MACD,WAAW,CAAC,UAAgB;AAC1B,gBAAQ,MAAM,sBAAsB,KAAK;AACzC,aAAK,aAAa,KAAK,uBAAuB,MAAM,OAAO,EAAE;AAC7D,aAAK,eAAe,KAAK,KAAK;AAE9B,cAAM;MACR,CAAC;;MAED,YAAY,CAAC;IAAC;EAElB;;;;;;EAOA,mBAAmB,SAAe;AAChC,QAAI,CAAC,KAAK,WAAW;AACnB,aAAO,GAAG,EAAE,UAAU,GAAG,SAAS,MAAM,QAAQ,OAAO,4CAA4C,EAAC,CAAE;IACxG;AAEA,SAAK,eAAe,KAAK,IAAI;AAC7B,SAAK,WAAU;AAGf,SAAK,wBAAwB,KAAK,OAAO;AAEzC,WAAO,KAAK,KAAK,qBAAoB,CAAE,EAAE,KACvC,UAAU,MAAK;AACb,UAAI,CAAC,KAAK,UAAU;AAClB,gBAAQ,MAAM,uGAAuG;AACrH,cAAM,IAAI,MAAM,+CAA+C;MACjE;AAGA,YAAM,aAAa,QAAQ,MAAM,GAAG,EAAE,CAAC;AACvC,YAAM,eAAe,OAAO,KAAK,UAAU;AAC3C,YAAM,cAAc,IAAI,YAAY,aAAa,MAAM;AACvD,YAAM,aAAa,IAAI,WAAW,WAAW;AAE7C,eAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC5C,mBAAW,CAAC,IAAI,aAAa,WAAW,CAAC;MAC3C;AAEA,cAAQ,IAAI,8DAA8D;AAC1E,aAAO,KAAK,KAAK,SAAU,YAAY,EAAE,MAAM,YAAW,CAAE,EAAE,OAAO;IACvE,CAAC,GACD,IAAI,CAAC,gBAAiC;AACpC,cAAQ,IAAI,8CAA8C,YAAY,UAAU,OAAO;AACvF,WAAK,cAAc;AACnB,WAAK,mBAAmB,KAAK,WAAW;AAGxC,YAAM,eAA4B;QAChC,UAAU,YAAY;QACtB,SAAS,CAAC,eAAuB,YAAY,QAAQ,UAAU;;AAGjE,WAAK,eAAe,KAAK,YAAY;AACrC,WAAK,eAAe,KAAK,KAAK;AAC9B,aAAO;IACT,CAAC,GACD,WAAW,CAAC,UAAgB;AAC1B,cAAQ,MAAM,iDAAiD,KAAK;AACpE,WAAK,aAAa,KAAK,uBAAuB,MAAM,OAAO,EAAE;AAC7D,WAAK,eAAe,KAAK,KAAK;AAC9B,YAAM;IACR,CAAC,GACD,YAAY,CAAC,CAAC;EAElB;;;;;;EAOA,gBAAgB,MAAU;AACxB,QAAI,CAAC,KAAK,WAAW;AACnB,aAAO,GAAG,EAAE,UAAU,GAAG,SAAS,MAAM,QAAQ,OAAO,4CAA4C,EAAC,CAAE;IACxG;AAEA,SAAK,eAAe,KAAK,IAAI;AAC7B,SAAK,WAAU;AAEf,WAAO,KAAK,KAAK,qBAAoB,CAAE,EAAE;MACvC,UAAU,MAAK;AAEb,YAAI,CAAC,KAAK,UAAU;AAClB,kBAAQ,MAAM,oGAAoG;AAClH,gBAAM,IAAI,MAAM,+CAA+C;QACjE;AACA,eAAO,IAAI,WAAwB,cAAW;AAC5C,gBAAM,aAAa,IAAI,WAAU;AAEjC,qBAAW,SAAS,MAAK;AACvB,oBAAQ,IAAI,+DAA+D;AAC3E,kBAAM,cAAc,WAAW;AAE/B,gBAAI,EAAE,uBAAuB,cAAc;AACzC,sBAAQ,MAAM,uDAAuD;AACrE,uBAAS,MAAM,IAAI,MAAM,0CAA0C,CAAC;AACpE;YACF;AAGA,kBAAM,UAAU,KAAK,qBAAqB,WAAW;AACrD,iBAAK,wBAAwB,KAAK,OAAO;AAGzC,oBAAQ,IAAI,0DAA0D;AACtE,iBAAK,SAAU,YAAY,EAAE,MAAM,YAAW,CAAE,EAAE,QAC/C,KAAK,CAAC,gBAAiC;AACtC,sBAAQ,IAAI,0DAA0D;AACtE,sBAAQ,IAAI,gCAAgC,YAAY,QAAQ,SAAS;AACzE,mBAAK,cAAc;AACnB,mBAAK,mBAAmB,KAAK,WAAW;AAGxC,oBAAM,eAA4B;gBAChC,UAAU,YAAY;gBACtB,SAAS,CAAC,eAAuB,YAAY,QAAQ,UAAU;;AAGjE,mBAAK,eAAe,KAAK,YAAY;AACrC,mBAAK,eAAe,KAAK,KAAK;AAE9B,uBAAS,KAAK,YAAY;AAC1B,uBAAS,SAAQ;YACnB,CAAC,EACA,MAAM,CAAC,UAAgB;AACtB,sBAAQ,MAAM,6CAA6C,KAAK;AAChE,mBAAK,aAAa,KAAK,uBAAuB,MAAM,OAAO,EAAE;AAC7D,mBAAK,eAAe,KAAK,KAAK;AAC9B,uBAAS,MAAM,KAAK;YACtB,CAAC;UACL;AAEA,qBAAW,UAAU,CAAC,UAAS;AAC7B,oBAAQ,MAAM,oCAAoC,KAAK;AACvD,iBAAK,aAAa,KAAK,yBAAyB;AAChD,iBAAK,eAAe,KAAK,KAAK;AAC9B,qBAAS,MAAM,IAAI,MAAM,yBAAyB,CAAC;UACrD;AAGA,qBAAW,kBAAkB,IAAI;QACnC,CAAC;MACH,CAAC;;MAED,YAAY,CAAC;IAAC;EAElB;;;;;;EAOA,eAAe,YAAkB;AAC/B,QAAI,CAAC,KAAK,WAAW;AACnB,aAAO,GAAG,EAAE,YAAY,OAAO,CAAA,GAAI,QAAQ,CAAA,EAAE,CAAE;IACjD;AAGA,QAAI,KAAK,iBAAiB,IAAI,UAAU,GAAG;AACzC,aAAO,GAAG,KAAK,iBAAiB,IAAI,UAAU,CAAE;IAClD;AAEA,WAAO,KAAK,KAAK,qBAAoB,CAAE,EAAE;MACvC,UAAU,MAAK;AACb,YAAI,CAAC,KAAK,UAAU;AACjB,kBAAQ,MAAM,yDAAyD;AACvE,gBAAM,IAAI,MAAM,8CAA8C;QACjE;AACA,YAAI,CAAC,KAAK,aAAa;AACrB,kBAAQ,MAAM,uDAAuD;AAErE,gBAAM,IAAI,MAAM,4CAA4C;QAC9D;AACA,eAAO,KAAK,KAAK,YAAY,QAAQ,UAAU,CAAC;MAClD,CAAC;;MACD,UAAU,CAAC,SAAsB;AAE/B,eAAO,KAAK,KAAK,eAAc,CAAE;MACnC,CAAC;MACD,IAAI,CAAC,gBAA4B;AAC/B,cAAM,SAAyB;UAC7B;UACA,OAAO,YAAY;UACnB,QAAQ,YAAY;;AAEtB,aAAK,iBAAiB,IAAI,YAAY,MAAM;AAC5C,eAAO;MACT,CAAC;MACD,WAAW,CAAC,UAAgB;AAC1B,gBAAQ,MAAM,oDAAoD,UAAU,KAAK,KAAK;AACtF,aAAK,aAAa,KAAK,oCAAoC,UAAU,KAAK,MAAM,OAAO,EAAE;AACzF,eAAO,GAAG,EAAE,YAAY,OAAO,CAAA,GAAI,QAAQ,CAAA,EAAE,CAAE;MACjD,CAAC;IAAC;EAEN;;;;;;;EAQA,WAAW,YAAoB,UAA4D,CAAA,GAAE;AAC3F,QAAI,CAAC,KAAK,aAAa,CAAC,WAAW,KAAI,GAAI;AACzC,aAAO,GAAG,CAAA,CAAE;IACd;AAEA,WAAO,KAAK,KAAK,qBAAoB,CAAE,EAAE,KACvC,UAAU,MAAK;AACb,UAAI,CAAC,KAAK,UAAU;AAClB,gBAAQ,MAAM,qDAAqD;AACnE,cAAM,IAAI,MAAM,0CAA0C;MAC5D;AACA,UAAI,CAAC,KAAK,aAAa;AACrB,gBAAQ,MAAM,mDAAmD;AACjE,eAAO,GAAG,CAAA,CAAE;MACd;AAEA,YAAM,UAA6B,CAAA;AACnC,YAAM,kBAAkB,KAAK,kBAAkB,YAAY,QAAQ,eAAe,QAAQ,SAAS;AACnG,YAAM,eAAgC,CAAA;AAEtC,eAAS,IAAI,GAAG,KAAK,KAAK,YAAY,UAAU,KAAK;AACnD,cAAM,UAAU;AAEhB,cAAM,cAAc,KAAK,aAAa,SAAS,eAAe,EAC3D,KAAK,iBAAc;AAClB,kBAAQ,KAAK,GAAG,WAAW;QAC7B,CAAC,EACA,MAAM,CAAC,UAAgB;AACtB,kBAAQ,MAAM,wCAAwC,OAAO,KAAK,KAAK;QACzE,CAAC;AACH,qBAAa,KAAK,WAAW;MAC/B;AACA,aAAO,KAAK,QAAQ,IAAI,YAAY,EAAE,KAAK,MAAM,OAAO,CAAC;IAC3D,CAAC,GACD,WAAW,CAAC,UAAgB;AAC1B,cAAQ,MAAM,sDAAsD,KAAK;AACzE,WAAK,aAAa,KAAK,0BAA0B,MAAM,OAAO,EAAE;AAChE,aAAO,GAAG,CAAA,CAAE;IACd,CAAC,CAAC;EAEN;;;;;;;;EASQ,kBAAkB,YAAoB,eAAyB,WAAmB;AACxF,QAAI,eAAe,WAAW,QAAQ,uBAAuB,MAAM;AAEnE,QAAI,WAAW;AACb,qBAAe,MAAM,YAAY;IACnC;AAEA,WAAO,IAAI,OAAO,cAAc,gBAAgB,MAAM,IAAI;EAC5D;;;;;;;EAQc,aACZ,YACA,aAAmB;;AAGnB,UAAI,CAAC,KAAK,YAAY,CAAC,KAAK,aAAa;AACvC,gBAAQ,MAAM,mEAAmE;AACjF,cAAM,IAAI,MAAM,wDAAwD;MAC1E;AACA,YAAM,cAAiC,CAAA;AACvC,UAAI;AAEF,YAAI;AACJ,YAAI,KAAK,iBAAiB,IAAI,UAAU,GAAG;AACzC,wBAAc,KAAK,iBAAiB,IAAI,UAAU;QACpD,OAAO;AACL,gBAAM,cAAc,MAAM,KAAK,YAAY,QAAQ,UAAU;AAC7D,gBAAM,UAAU,MAAM,YAAY,eAAc;AAChD,wBAAc;YACZ;YACA,OAAO,QAAQ;YACf,QAAQ,QAAQ;;AAElB,eAAK,iBAAiB,IAAI,YAAY,WAAW;QACnD;AAGA,cAAM,kBAAkB,MAAM,KAAK,YAAY,QAAQ,UAAU;AACjE,cAAM,WAAW,gBAAgB,YAAY,EAAE,OAAO,EAAG,CAAE;AAG3D,cAAM,QAA+C,CAAA;AACrD,YAAI,cAA0B,CAAA;AAC9B,YAAI,QAAuB;AAE3B,mBAAW,QAAQ,YAAY,OAAO;AAEpC,cAAI,KAAK,IAAI,KAAI,MAAO;AAAI;AAC5B,cAAI,UAAU,QAAQ,KAAK,IAAI,KAAK,UAAU,CAAC,IAAI,KAAK,IAAI,GAAG;AAE7D,wBAAY,KAAK,IAAI;UACvB,OAAO;AAEL,gBAAI,YAAY,SAAS,GAAG;AAC1B,oBAAM,KAAK;gBACT,MAAM,YAAY,IAAI,OAAK,EAAE,GAAG,EAAE,KAAK,EAAE;gBACzC,OAAO,CAAC,GAAG,WAAW;eACvB;YACH;AACA,0BAAc,CAAC,IAAI;UACrB;AACA,kBAAQ,KAAK,UAAU,CAAC;QAC1B;AAGA,YAAI,YAAY,SAAS,GAAG;AAC1B,gBAAM,KAAK;YACT,MAAM,YAAY,IAAI,OAAK,EAAE,GAAG,EAAE,KAAK,EAAE;YACzC,OAAO,CAAC,GAAG,WAAW;WACvB;QACH;AAGA,YAAI,aAAa;AACjB,mBAAW,QAAQ,OAAO;AACxB,cAAI;AACJ,sBAAY,YAAY;AAExB,kBAAQ,QAAQ,YAAY,KAAK,KAAK,IAAI,OAAO,MAAM;AACrD,kBAAM,aAAa,MAAM;AACzB,kBAAM,WAAW,aAAa,MAAM,CAAC,EAAE;AAGvC,gBAAI,aAAa;AACjB,gBAAI,YAA6B;AACjC,gBAAI,UAA2B;AAE/B,uBAAW,QAAQ,KAAK,OAAO;AAC7B,oBAAM,YAAY;AAClB,oBAAM,UAAU,aAAa,KAAK,IAAI;AAEtC,kBAAI,cAAc,QAAQ,aAAa,SAAS;AAC9C,4BAAY;cACd;AAEA,kBAAI,YAAY,QAAQ,YAAY,SAAS;AAC3C,0BAAU;cACZ;AAEA,kBAAI,cAAc,QAAQ,YAAY,MAAM;AAC1C;cACF;AAEA,2BAAa;YACf;AAEA,gBAAI,aAAa,SAAS;AAExB,oBAAM,WAAW;gBACf,MAAM,UAAU,UAAU,CAAC;gBAC3B,KAAK,SAAS,SAAS,UAAU,UAAU,CAAC,IAAI,UAAU;gBAC1D,OAAO,QAAQ,UAAU,CAAC,IAAI,QAAQ;gBACtC,QAAQ,SAAS,SAAS,QAAQ,UAAU,CAAC;;AAG/C,0BAAY,KAAK;gBACf;gBACA,YAAY;gBACZ,MAAM,MAAM,CAAC;gBACb;eACD;YACH;UACF;QACF;AAEA,eAAO;MACT,SAAS,OAAO;AACd,gBAAQ,MAAM,+CAA+C,UAAU,KAAK,KAAK;AACjF,eAAO,CAAA;MACT;IACF;;;;;;;;;;EAUA,aAAa,YAAoB,UAAwE,MAAc,QAAgB,0BAAwB;AAC7J,UAAM,KAAK,aAAa,KAAK,IAAG,CAAE,IAAI,KAAK,MAAM,KAAK,OAAM,IAAK,GAAI,CAAC;AACtE,UAAM,YAA0B;MAC9B;MACA;MACA;MACA;MACA;;AAGF,UAAM,oBAAoB,KAAK,kBAAkB;AACjD,SAAK,kBAAkB,KAAK,CAAC,GAAG,mBAAmB,SAAS,CAAC;AAE7D,WAAO;EACT;;;;;EAMA,gBAAgB,IAAU;AACxB,UAAM,oBAAoB,KAAK,kBAAkB;AACjD,SAAK,kBAAkB,KAAK,kBAAkB,OAAO,OAAK,EAAE,OAAO,EAAE,CAAC;EACxE;;;;;;EAOA,kBAAkB,YAAkB;AAClC,WAAO,KAAK,YAAY,KACtB,IAAI,gBAAc,WAAW,OAAO,OAAK,EAAE,eAAe,UAAU,CAAC,CAAC;EAE1E;;;;EAKA,kBAAe;AACb,SAAK,kBAAkB,KAAK,CAAA,CAAE;EAChC;;;;EAKA,WAAQ;AACN,QAAI,KAAK,aAAa;AACpB,WAAK,YAAY,QAAO;AACxB,WAAK,cAAc;IACrB;AACA,SAAK,mBAAmB,KAAK,IAAI;AACjC,SAAK,eAAe,KAAK,IAAI;AAC7B,SAAK,kBAAkB,KAAK,CAAA,CAAE;AAC9B,SAAK,wBAAwB,KAAK,IAAI;AACtC,SAAK,WAAU;AACf,SAAK,eAAe,KAAK,KAAK;AAE9B,YAAQ,IAAI,iDAAiD;EAC/D;;;;;;EAOQ,qBAAqB,QAAmB;AAC9C,QAAI,SAAS;AACb,UAAM,QAAQ,IAAI,WAAW,MAAM;AACnC,UAAM,MAAM,MAAM;AAClB,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,gBAAU,OAAO,aAAa,MAAM,CAAC,CAAC;IACxC;AACA,UAAM,SAAS,OAAO,KAAK,MAAM;AACjC,WAAO,+BAA+B,MAAM;EAC9C;;qCAxoBW,aAAU,mBAAA,UAAA,GAAA,mBA6CX,WAAW,CAAA;EAAA;4EA7CV,aAAU,SAAV,YAAU,WAAA,YAFT,OAAM,CAAA;;;sEAEP,YAAU,CAAA;UAHtB;WAAW;MACV,YAAY;KACb;;UA8CI;WAAO,WAAW;;;;;;;;AE5HnB,IAAA,yBAAA,GAAA,2BAAA,CAAA;AAEE,IAAA,2BAAA,cAAA,SAAA,oGAAA,QAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,MAAA,6BAAA,OAAA,aAAA,MAAA,MAAA,OAAA,cAAA;AAAA,aAAA,sBAAA,MAAA;IAAA,CAAA;AAIA,IAAA,qBAAA,cAAA,SAAA,oGAAA,QAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAc,OAAA,aAAA,MAAA,CAAoB;IAAA,CAAA,EAAC,aAAA,SAAA,mGAAA,QAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBACtB,OAAA,YAAA,MAAA,CAAmB;IAAA,CAAA,EAAC,cAAA,SAAA,oGAAA,QAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBACnB,OAAA,aAAA,MAAA,CAAoB;IAAA,CAAA,EAAC,SAAA,SAAA,+FAAA,QAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAuB1B,OAAA,iBAAA,MAAA,CAAwB;IAAA,CAAA;AAClC,IAAA,uBAAA;;;;AA/BC,IAAA,qBAAA,OAAA,OAAA,eAAA;AACA,IAAA,2BAAA,QAAA,OAAA,WAAA;AACA,IAAA,qBAAA,QAAA,OAAA,IAAA,EAAa,aAAA,UAAA,EACW,eAAA,UAAA,EACE,UAAA,OAAA,YAAA,EAIH,YAAA,OAAA,EACH,eAAA,IAAA,EACA,qBAAA,IAAA,EACM,kBAAA,IAAA,EACH,qBAAA,IAAA,EACG,mBAAA,IAAA,EACF,8BAAA,IAAA,EACW,sBAAA,IAAA,EACR,mBAAA,IAAA,EACH,sBAAA,IAAA,EACG,sBAAA,0BAAA,EACsB,8BAAA,IAAA,EACd,oBAAA,IAAA,EACV,sBAAA,IAAA,EACE,wBAAA,0BAAA,EACwB,oBAAA,IAAA,EAC1B,wBAAA,IAAA,EACI,cAAA,OAAA,UAAA,EACJ,gBAAA,CAAA,OAAA,sBAAA,EACe,WAAA,OAAA,OAAA,EACrB,WAAA,OAAA,OAAA;;;;;AAMrB,IAAA,yBAAA,GAAA,OAAA,CAAA,EAAkE,GAAA,GAAA;AAC7D,IAAA,iBAAA,GAAA,mCAAA;AAAiC,IAAA,uBAAA,EAAI;;;;;AAI1C,IAAA,yBAAA,GAAA,OAAA,CAAA;AACE,IAAA,iBAAA,GAAA,6DAAA;AACF,IAAA,uBAAA;;;AD9BE,IAAO,qBAAP,MAAO,oBAAkB;EA+BnB;EACA;EACqB;;EA/B/B,cAAsB;EACtB,aAAqB;EACrB,OAAwB;;;EAGxB,aAAqB;EACrB,gBAAyB;EACzB,YAAqB;;;EAGrB,cAAuB;;EAGvB,kBAA0B;EAElB,gBAA8B,IAAI,aAAY;EAC/C,YAAqB;;EACrB,kBAAiC;;;EAGjC,aAAqB;;EACrB,UAAkB;;EAClB,UAAkB;;EAClB,iBAAyB;;EACzB,yBAAkC;EAClC,oBAA4B;EAC5B,eAAuB;EAE9B,YACU,qBACA,oBACqB,YAAkB;AAFvC,SAAA,sBAAA;AACA,SAAA,qBAAA;AACqB,SAAA,aAAA;AAE7B,SAAK,YAAY,kBAAkB,KAAK,UAAU;AAGlD,QAAI,KAAK,WAAW;AAClB,wBAAkB,gBAAgB;AAClC,wBAAkB,mBAAmB;AACrC,wBAAkB,mBAAmB;AAIrC,YAAM,mBAAmB;;QAEvB,kBAAkB;;;QAGlB,uBAAuB;;;QAGvB,kBAAkB;;;QAGlB,qBAAqB;;QAGrB,mBAAmB;;AAGpB,wBAA0B,yBAAyB;AAEpD,cAAQ,IAAI,6CAA6C,gBAAgB;AACzE,cAAQ,IAAI,0CAA0C;IACxD;EACF;EAEA,WAAQ;AACN,YAAQ,IAAI,6BAA6B;AACzC,YAAQ,IAAI,oCAAoC,KAAK,YAAY,YAAY,QAAQ;AACrF,YAAQ,IAAI,mCAAmC,KAAK,MAAM,kBAAkB;AAC5E,YAAQ,IAAI,4BAA4B,KAAK,kBAAkB,KAAK,UAAU,CAAC;AAC/E,YAAQ,IAAI,8BAA8B,KAAK,sBAAsB;AAGrE,QAAI,KAAK,WAAW;AAClB,cAAQ,IAAI,yCAA0C,OAAe,YAAY;AAGjF,WAAK,cAAc,IACjB,KAAK,mBAAmB,kBAAkB,UAAU,aAAU;AAC5D,YAAI,SAAS;AACX,kBAAQ,IAAI,uDAAuD;AACnE,eAAK,kBAAkB;AAGvB,cAAI,CAAC,KAAK,mBAAmB,QAAQ,WAAW,8BAA8B,GAAG;AAC7E,iBAAK,kBAAkB;UAC3B;QACF,OAAO;AAOL,kBAAQ,IAAI,4DAA4D;QAC1E;MACF,CAAC,CAAC;IAEN;AAGA,YAAQ,IAAI,0DAA0D;EACxE;EAEA,kBAAe;AACb,YAAQ,IAAI,oCAAoC;AAChD,YAAQ,IAAI,4CAA4C;AAGxD,eAAW,MAAK;AACd,cAAQ,IAAI,2CAA2C,KAAK,MAAM,kBAAkB;AACpF,cAAQ,IAAI,4CAA4C,KAAK,6BAA4B,CAAE;AAG3F,WAAK,2BAA0B;IACjC,GAAG,GAAI;EACT;;;;EAKQ,6BAA0B;AAChC,QAAI,CAAC,KAAK;AAAW;AAErB,QAAI;AAEF,YAAM,iBAAiB,SAAS,eAAe,qBAAqB;AACpE,UAAI,gBAAgB;AAClB,uBAAe,QAAQ;AACvB,gBAAQ,IAAI,wCAAwC,eAAe,KAAK;AAGxE,uBAAe,cAAc,IAAI,MAAM,SAAS,EAAE,SAAS,KAAI,CAAE,CAAC;MACpE,OAAO;AACL,gBAAQ,KAAK,gDAAgD;MAC/D;AAGA,YAAM,iBAAiB,SAAS,eAAe,gBAAgB;AAC/D,UAAI,gBAAgB;AAClB,uBAAe,QAAQ;AACvB,gBAAQ,IAAI,wCAAwC,eAAe,KAAK;AAGxE,uBAAe,cAAc,IAAI,MAAM,SAAS,EAAE,SAAS,KAAI,CAAE,CAAC;MACpE,OAAO;AACL,gBAAQ,KAAK,gDAAgD;MAC/D;AAGA,YAAM,sBAAsB,SAAS,eAAe,sBAAsB;AAC1E,UAAI,qBAAqB;AACvB,4BAAoB,QAAQ;AAC5B,gBAAQ,IAAI,6CAA6C,oBAAoB,KAAK;AAGlF,4BAAoB,cAAc,IAAI,MAAM,SAAS,EAAE,SAAS,KAAI,CAAE,CAAC;MACzE,OAAO;AACL,gBAAQ,KAAK,qDAAqD;MACpE;IACF,SAAS,OAAO;AACd,cAAQ,MAAM,wDAAwD,KAAK;IAC7E;EACF;EAEA,cAAW;AACT,SAAK,cAAc,YAAW;EAChC;;;;;EAMA,eAAe,OAAY;AAEzB,QAAI,CAAC,KAAK,WAAW;AACnB,cAAQ,MAAM,gEAAgE;AAC9E;IACF;AAEA,YAAQ,IAAI,4CAA4C;AAExD,UAAM,QAAQ,MAAM;AACpB,QAAI,MAAM,SAAS,MAAM,MAAM,SAAS,GAAG;AACzC,YAAM,OAAO,MAAM,MAAM,CAAC;AAC1B,cAAQ,IAAI,8BAA8B,KAAK,IAAI,WAAW,KAAK,IAAI,WAAW,KAAK,IAAI,QAAQ;AAEnG,UAAI,KAAK,SAAS,mBAAmB;AACnC,aAAK,kBAAkB,KAAK;AAC5B,aAAK,kBAAkB;AACvB,gBAAQ,IAAI,qEAAqE;AAGjF,cAAM,SAAS,IAAI,WAAU;AAE7B,eAAO,cAAc,MAAK;AACxB,kBAAQ,IAAI,8CAA8C;QAC5D;AAEA,eAAO,aAAa,CAACA,WAAS;AAC5B,cAAIA,OAAM,kBAAkB;AAC1B,kBAAM,gBAAgB,KAAK,MAAOA,OAAM,SAASA,OAAM,QAAS,GAAG;AACnE,oBAAQ,IAAI,6CAA6C,aAAa,GAAG;UAC3E;QACF;AAEA,eAAO,SAAS,CAAC,MAAK;AACpB,kBAAQ,IAAI,kDAAkD;AAC9D,gBAAM,YAAY,EAAE,QAAQ;AAE5B,eAAK,kBAAkB;AAGvB,gBAAM,gBAAgB,KAAK,iBAAiB,UAAU,GAAG,GAAG,IAAI;AAChE,kBAAQ,IAAI,gDAAgD,KAAK,iBAAiB,UAAU,MAAM,EAAE;AACpG,kBAAQ,IAAI,mDAAmD,aAAa,EAAE;AAG9E,cAAI,KAAK,iBAAiB,WAAW,8BAA8B,GAAG;AACpE,oBAAQ,IAAI,0DAA0D;UACxE,OAAO;AACL,oBAAQ,KAAK,qEAAqE;UACpF;AAEA,kBAAQ,IAAI,yDAAyD;QACvE;AAEA,eAAO,UAAU,CAAC,UAAS;AACzB,kBAAQ,MAAM,iCAAiC,KAAK;AACpD,eAAK,kBAAkB;AACvB,eAAK,kBAAkB;AACvB,eAAK,aAAa;AAClB,gBAAM,sCAAsC;QAC9C;AAEA,gBAAQ,IAAI,kDAAkD;AAC9D,eAAO,cAAc,IAAI;MAC3B,OAAO;AACL,aAAK,kBAAkB;AACvB,aAAK,kBAAkB;AACvB,aAAK,aAAa;AAClB,gBAAQ,MAAM,kCAAkC,KAAK,IAAI,4BAA4B;AACrF,cAAM,iCAAiC;MACzC;IACF,OAAO;AACL,cAAQ,KAAK,gEAAgE;AAC7E,WAAK,kBAAkB;AACvB,WAAK,kBAAkB;AACvB,WAAK,aAAa;IACpB;EACF;;;;;;;;;EAUA,YAAY,KAAQ;AAClB,YAAQ,IAAI,wCAAwC;AACpD,YAAQ,IAAI,6BAA6B,GAAG;AAE5C,QAAI,OAAO,IAAI,UAAU;AACvB,WAAK,aAAa,IAAI;AACtB,cAAQ,IAAI,+BAA+B,KAAK,UAAU,QAAQ;IACpE;AAGA,eAAW,MAAK;AACd,cAAQ,IAAI,iDAAiD,KAAK,6BAA4B,CAAE;AAChG,cAAQ,IAAI,4CAA4C,KAAK,MAAM,kBAAkB;IACvF,GAAG,GAAG;EACR;;;;;EAMA,aAAa,YAAkB;AAC7B,YAAQ,IAAI,+BAA+B,UAAU,EAAE;AACvD,SAAK,cAAc;EACrB;;;;;EAMA,aAAa,SAAwB;AAGnC,UAAM,YAAY,OAAO,YAAY,WAAW,WAAW,OAAO,IAAI;AACtE,YAAQ,IAAI,0CAA0C,SAAS,EAAE;AAIjE,SAAK,OAAO;EACd;;;;;;;;;;;;EAcA,iBAAiB,OAAU;AACzB,YAAQ,MAAM,iCAAiC,KAAK;AAEpD,YAAQ,MAAM,mCAAmC;MAC/C,iBAAiB,CAAC,CAAC,KAAK;MACxB,oBAAoB,KAAK,kBAAkB,KAAK,gBAAgB,SAAS;MACzE,oBAAoB,KAAK,kBAAkB,KAAK,gBAAgB,UAAU,GAAG,EAAE,IAAI;MACnF,UAAU,KAAK;MACf,YAAY,KAAK;MACjB,WAAW,KAAK,YAAa,OAAe,eAAe;MAC3D,kBAAkB,KAAK,6BAA4B;MACnD,YAAY,KAAK;MACjB,gBAAgB,KAAK,kBAAkB,KAAK,UAAU;MACtD,wBAAwB,KAAK;MAC7B,6BAA6B;KAC9B;EACH;;;;;;EAOQ,kBAAkB,MAAY;AACpC,YAAQ,MAAM;MACZ,KAAK;AAAG,eAAO;MACf,KAAK;AAAG,eAAO;MACf,KAAK;AAAG,eAAO;MACf;AAAS,eAAO;IAClB;EACF;;;;;EAMQ,+BAA4B;AAClC,QAAI,CAAC,KAAK,WAAW;AACnB,aAAO,EAAE,OAAO,OAAO,QAAQ,MAAK;IACtC;AAEA,UAAM,YAAY,SAAS,cAAc,qBAAqB;AAC9D,QAAI,CAAC,WAAW;AACd,aAAO,EAAE,OAAO,uBAAuB,QAAQ,sBAAqB;IACtE;AAEA,WAAO;MACL,OAAO,GAAG,UAAU,WAAW;MAC/B,QAAQ,GAAG,UAAU,YAAY;;EAErC;;qCA9WW,qBAAkB,4BAAA,2BAAA,GAAA,4BAAA,UAAA,GAAA,4BAiCnB,WAAW,CAAA;EAAA;yEAjCV,qBAAkB,WAAA,CAAA,CAAA,gBAAA,CAAA,GAAA,OAAA,GAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,sBAAA,GAAA,CAAA,GAAA,oBAAA,GAAA,CAAA,GAAA,OAAA,QAAA,QAAA,aAAA,eAAA,UAAA,YAAA,eAAA,qBAAA,kBAAA,qBAAA,mBAAA,8BAAA,sBAAA,mBAAA,sBAAA,sBAAA,8BAAA,oBAAA,sBAAA,wBAAA,oBAAA,wBAAA,cAAA,gBAAA,WAAA,WAAA,cAAA,cAAA,aAAA,SAAA,GAAA,MAAA,GAAA,CAAA,SAAA,kBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,mBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,cAAA,cAAA,aAAA,SAAA,OAAA,QAAA,QAAA,aAAA,eAAA,UAAA,YAAA,eAAA,qBAAA,kBAAA,qBAAA,mBAAA,8BAAA,sBAAA,mBAAA,sBAAA,sBAAA,8BAAA,oBAAA,sBAAA,wBAAA,oBAAA,wBAAA,cAAA,gBAAA,WAAA,SAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,iBAAA,CAAA,GAAA,UAAA,SAAA,4BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACf/B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAkC,GAAA,OAAA,CAAA;AAG9B,MAAA,qBAAA,GAAA,uDAAA,GAAA,IAAA,2BAAA,CAAA,EAgCC,GAAA,mCAAA,GAAA,GAAA,OAAA,CAAA,EAGiE,GAAA,mCAAA,GAAA,GAAA,OAAA,CAAA;AAQpE,MAAA,uBAAA,EAAM;;;AA3CsB,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,aAAA,IAAA,eAAA;AAmCpB,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,aAAA,CAAA,IAAA,eAAA;AAKA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,SAAA;;oBDjCE,cAAY,MAAE,4BAA0B,+BAAE,WAAW,GAAA,QAAA,CAAA,6yGAAA,EAAA,CAAA;;;sEAKpD,oBAAkB,CAAA;UAR9B;;gBACW;MAAgB,YACd;MAAI,SACP,CAAC,cAAc,4BAA4B,WAAW;MAAC,SAGvD,CAAC,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UAmCvB;WAAO,WAAW;;;;6EAjCV,oBAAkB,EAAA,WAAA,sBAAA,UAAA,+EAAA,YAAA,GAAA,CAAA;AAAA,GAAA;;;AEyBzB,IAAO,2BAAP,MAAO,0BAAwB;EACJ;EAE/B,UAAkB;EAClB,kBAAkC;;EAGlC,mBAAqC;IACnC,cAAc;IACd,iBAAiB;IACjB,UAAU;IACV,YAAY;IACZ,aAAa;IACb,QAAQ;IACR,KAAK;IACL,cAAc;IACd,KAAK;;;EAIP,cAA2B;IACzB,MAAM;IACN,QAAQ;;EAGV,YAAwB;IACtB,EAAE,OAAO,aAAa,OAAO,cAAc,MAAM,YAAI;IACrD,EAAE,OAAO,WAAW,OAAO,YAAY,MAAM,YAAI;IACjD,EAAE,OAAO,YAAY,OAAO,aAAa,MAAM,eAAI;IACnD,EAAE,OAAO,QAAQ,OAAO,SAAS,MAAM,SAAG;IAC1C,EAAE,OAAO,UAAU,QAAQ,MAAM,KAAK,OAAM,GAAI,MAAM,YAAI;;;EAI5D,WAAsB;IACpB;MACE,IAAI;MACJ,eAAe;MACf,UAAU;MACV,WAAW;MACX,MAAM;MACN,SAAS;MACT,SAAS;;IAEX;MACE,IAAI;MACJ,eAAe;MACf,UAAU;MACV,WAAW;MACX,MAAM;MACN,SAAS;MACT,SAAS;;IAEX;MACE,IAAI;MACJ,eAAe;MACf,UAAU;MACV,WAAW;MACX,MAAM;MACN,SAAS;MACT,SAAS;;;;EAKb,cAA2B;IACzB,UAAU;IACV,YAAY;IACZ,KAAK;IACL,MAAM;IACN,eAAe;IACf,OAAO;;;;;EAQD,QAAQ,OAAO,cAAc;EAC7B,SAAS,OAAO,MAAM;EACtB,aAAa,OAAO,UAAU;;EAC9B,OAAO,OAAO,UAAU;;EACxB,aAAa,OAAO,WAAW;;;EAIvC,WAAQ;AAEN,SAAK,UAAU,KAAK,MAAM,SAAS,SAAS,IAAI,IAAI,KAAK;AAEzD,QAAI,KAAK,SAAS;AAChB,cAAQ,IAAI,uDAAuD,KAAK,OAAO,EAAE;AAGjF,YAAM,SAAS,iBAAiB,KAAK,OAAO;AAI5C,WAAK,WAAW,QAAQ,MAAM,EAAE,UAAU;QACxC,MAAM,CAAC,WAAU;AACf,kBAAQ,IAAI,8DAA8D,MAAM,YAAY,OAAO,QAAQ,EAAE;QAE/G;QACA,OAAO,CAAC,QAAO;AACb,kBAAQ,MAAM,wDAAwD,MAAM,MAAM,GAAG;QAGvF;OACD;IACH,OAAO;AACL,cAAQ,KAAK,2EAA2E;IAE1F;AAGA,SAAK,uBAAsB;AAE3B,YAAQ,IAAI,oDAAoD,KAAK,gBAAgB;AAGrF,SAAK,iBAAgB;EAIvB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAmCO,YAAY,YAAkB;AACnC,QAAI,KAAK,oBAAoB;AAC3B,cAAQ,IAAI,6CAA6C,UAAU,EAAE;AACrE,WAAK,mBAAmB,cAAc;IACxC,OAAO;AACL,cAAQ,KAAK,gFAAgF;IAC/F;EACF;;EAGA,cAAW;AACT,YAAQ,IAAI,cAAc;AAC1B,SAAK,OAAO,SAAS,CAAC,YAAY,CAAC;EACrC;EAEA,cAAW;AACT,YAAQ,IAAI,cAAc;EAC5B;EAEA,iBAAiB,QAAe;AAC9B,YAAQ,IAAI,qBAAqB,MAAM;EACzC;EAEA,gBAAgB,MAAc;AAC5B,YAAQ,IAAI,sBAAsB,IAAI;AACtC,QAAI,KAAK,OAAO;AACd,WAAK,OAAO,SAAS,CAAC,KAAK,KAAK,CAAC;IACnC,WAAW,KAAK,QAAQ;AACtB,WAAK,OAAM;IACb;EACF;EAEA,SAAM;AACJ,YAAQ,IAAI,gBAAgB;AAC5B,SAAK,OAAO,SAAS,CAAC,GAAG,CAAC;EAC5B;;EAGA,0BAAuB;AACrB,YAAQ,IAAI,kCAAkC;AAC9C,SAAK,OAAO,SAAS,CAAC,YAAY,CAAC;EACrC;;EAGQ,yBAAsB;AAC5B,UAAM,WAAW,KAAK;AACtB,YAAQ,IAAI,0DAA0D,QAAQ,EAAE;AAGhF,SAAK,iBAAiB,WAAW,YAAY;AAG7C,QAAI,aAAa,YAAY;AAC3B,WAAK,iBAAiB,aAAa;AACnC,WAAK,iBAAiB,cAAc;AACpC,WAAK,iBAAiB,SAAS;AAC/B,WAAK,iBAAiB,MAAM;AAC5B,WAAK,iBAAiB,eAAe;AACrC,WAAK,iBAAiB,MAAM;IAC9B,OAAO;AAEL,WAAK,iBAAiB,aAAa;AACnC,WAAK,iBAAiB,cAAc;AACpC,WAAK,iBAAiB,SAAS;AAC/B,WAAK,iBAAiB,MAAM;AAC5B,WAAK,iBAAiB,eAAe;AACrC,WAAK,iBAAiB,MAAM;IAC9B;AAEA,YAAQ,IAAI,2CAA2C,KAAK,gBAAgB;EAC9E;;EAGQ,mBAAgB;AACtB,UAAM,WAAW,KAAK;AACtB,YAAQ,IAAI,gDAAgD,QAAQ,EAAE;AAEtE,QAAI,CAAC,UAAU;AACb,cAAQ,KAAK,+DAA+D;AAC5E,WAAK,gBAAe;AACpB;IACF;AAGA,UAAM,UAAU,iBAAiB,QAAQ;AACzC,YAAQ,IAAI,kDAAkD,OAAO,EAAE;AAEvE,SAAK,KAAK,IAAI,SAAS,EAAE,cAAc,OAAM,CAAE,EAC5C,UAAU;MACT,MAAM,CAAC,SAAQ;AACb,gBAAQ,IAAI,oCAAoC,QAAQ,sBAAsB;AAC9E,aAAK,4BAA4B,IAAI;MACvC;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,kDAAkD,QAAQ,KAAK,KAAK;AAClF,gBAAQ,IAAI,kDAAkD;AAC9D,aAAK,gBAAe;MACtB;KACD;EACL;;EAGQ,kBAAe;AACrB,YAAQ,IAAI,2CAA2C;AAGvD,SAAK,KAAK,IAAI,4CAA4C,EAAE,cAAc,OAAM,CAAE,EAC/E,UAAU;MACT,MAAM,CAAC,SAAQ;AACb,gBAAQ,IAAI,oDAAoD;AAChE,aAAK,4BAA4B,IAAI;MACvC;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,iDAAiD,KAAK;AACpE,gBAAQ,IAAI,gDAAgD;AAG5D,aAAK,KAAK,IAAI,8BAA8B,EAAE,cAAc,OAAM,CAAE,EACjE,UAAU;UACT,MAAM,CAAC,SAAQ;AACb,oBAAQ,IAAI,0DAA0D;AACtE,iBAAK,4BAA4B,IAAI;UACvC;UACA,OAAO,CAAC,aAAY;AAClB,oBAAQ,MAAM,uDAAuD,QAAQ;AAC7E,oBAAQ,MAAM,6CAA6C;UAC7D;SACD;MACL;KACD;EACL;;EAGQ,4BAA4B,MAAU;AAE5C,QAAI,CAAC,kBAAkB,KAAK,UAAU,GAAG;AACvC,cAAQ,IAAI,6EAA6E;AACzF;IACF;AAGA,QAAI,OAAO,eAAe,aAAa;AACrC,cAAQ,MAAM,mEAAmE;AACjF;IACF;AAEA,UAAM,SAAS,IAAI,WAAU;AAC7B,WAAO,SAAS,MAAK;AACnB,YAAM,UAAU,OAAO;AACvB,cAAQ,IAAI,gEAAgE;AAG5E,WAAK,WAAW,mBAAmB,OAAO,EAAE,UAAU;QACpD,MAAM,CAAC,WAAU;AACf,kBAAQ,IAAI,8CAA8C,OAAO,QAAQ,QAAQ;QACnF;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,oDAAoD,KAAK;QACzE;OACD;IACH;AACA,WAAO,cAAc,IAAI;EAC3B;;EAGA,iBAAiB,MAAe;AAC9B,YAAQ,IAAI,sBAAsB,IAAI;AACtC,SAAK,WAAW;EAClB;EAEA,gBAAgB,OAAqC;AACnD,YAAQ,IAAI,sBAAsB,KAAK;AACvC,SAAK,YAAY,MAAM,IAAI;EAC7B;EAEA,oBAAoB,OAAwC;AAC1D,YAAQ,IAAI,yBAAyB,KAAK;AAC1C,UAAM,WAAW,KAAK,SAAS,UAAU,SAAO,IAAI,OAAO,MAAM,IAAI,EAAE;AACvE,QAAI,aAAa,IAAI;AACnB,WAAK,SAAS,QAAQ,EAAE,UAAU,MAAM;IAC1C;EACF;EAEA,oBAAoB,OAAyC;AAC3D,YAAQ,IAAI,yBAAyB,KAAK;AAC1C,UAAM,WAAW,KAAK,SAAS,UAAU,SAAO,IAAI,OAAO,MAAM,IAAI,EAAE;AACvE,QAAI,aAAa,IAAI;AACnB,WAAK,SAAS,QAAQ,EAAE,UAAU,MAAM;IAC1C;EACF;;EAGA,oBAAoB,MAAiB;AACnC,YAAQ,IAAI,yBAAyB,IAAI;AACzC,SAAK,cAAc;EACrB;;EAGA,mBAAgB;AACd,YAAQ,IAAI,yBAAyB;AACrC,YAAQ,IAAI,iBAAiB,KAAK,WAAW;AAC7C,YAAQ,IAAI,cAAc,KAAK,QAAQ;EAEzC;;qCA3WW,2BAAwB;EAAA;yEAAxB,2BAAwB,WAAA,CAAA,CAAA,uBAAA,CAAA,GAAA,WAAA,SAAA,+BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;4BACxB,oBAAkB,CAAA;;;;;;;;ACzC/B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAuB,GAAA,YAAA,CAAA;AAOnB,MAAA,qBAAA,aAAA,SAAA,kEAAA;AAAA,eAAa,IAAA,YAAA;MAAa,CAAA,EAAC,aAAA,SAAA,kEAAA;AAAA,eACd,IAAA,YAAA;MAAa,CAAA,EAAC,kBAAA,SAAA,qEAAA,QAAA;AAAA,eACT,IAAA,iBAAA,MAAA;MAAwB,CAAA,EAAC,iBAAA,SAAA,oEAAA,QAAA;AAAA,eAC1B,IAAA,gBAAA,MAAA;MAAuB,CAAA;AAC1C,MAAA,uBAAA;AAEA,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA0B,GAAA,OAAA,CAAA,EAEU,GAAA,oBAAA,CAAA;AAK9B,MAAA,qBAAA,aAAA,SAAA,0EAAA;AAAA,eAAa,IAAA,wBAAA;MAAyB,CAAA;AACxC,MAAA,uBAAA,EAAmB;AAIrB,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA4B,GAAA,OAAA,CAAA;AAGxB,MAAA,oBAAA,GAAA,gBAAA;AACF,MAAA,uBAAA;AAGA,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA0B,GAAA,OAAA,CAAA,EAEE,IAAA,YAAA,CAAA;AAItB,MAAA,qBAAA,cAAA,SAAA,kEAAA,QAAA;AAAA,eAAc,IAAA,iBAAA,MAAA;MAAwB,CAAA,EAAC,aAAA,SAAA,iEAAA,QAAA;AAAA,eAC1B,IAAA,gBAAA,MAAA;MAAuB,CAAA,EAAC,iBAAA,SAAA,qEAAA,QAAA;AAAA,eACpB,IAAA,oBAAA,MAAA;MAA2B,CAAA,EAAC,iBAAA,SAAA,qEAAA,QAAA;AAAA,eAC5B,IAAA,oBAAA,MAAA;MAA2B,CAAA;AAC9C,MAAA,uBAAA,EAAW;AAIb,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA6B,IAAA,eAAA,EAAA;AAGzB,MAAA,2BAAA,iBAAA,SAAA,wEAAA,QAAA;AAAA,QAAA,6BAAA,IAAA,aAAA,MAAA,MAAA,IAAA,cAAA;AAAA,eAAA;MAAA,CAAA;AACA,MAAA,qBAAA,cAAA,SAAA,qEAAA,QAAA;AAAA,eAAc,IAAA,oBAAA,MAAA;MAA2B,CAAA;AAC3C,MAAA,uBAAA,EAAc;AAIhB,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA4B,IAAA,cAAA,EAAA;AAGxB,MAAA,qBAAA,eAAA,SAAA,uEAAA;AAAA,eAAe,IAAA,iBAAA;MAAkB,CAAA;AACjC,MAAA,iBAAA,IAAA,UAAA;AACF,MAAA,uBAAA,EAAa,EACT,EACF,EACF,EACF;;;AA3DJ,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,WAAA,EAAoB,aAAA,IAAA,SAAA;AAYhB,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,gBAAA,EAAyB,kBAAA,IAAA;AAoBrB,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,QAAA;AAYA,MAAA,oBAAA,CAAA;AAAA,MAAA,2BAAA,WAAA,IAAA,WAAA;;;IDtBR;IACA;IACA;IAAW;IAAA;IACX;IACA;IACA;IACA;IACA;IACA;EAAY,GAAA,QAAA,CAAA,mlIAAA,EAAA,CAAA;;;sEAKH,0BAAwB,CAAA;UAjBpC;uBACW,yBAAuB,YACrB,MAAI,SACP;MACP;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;OACD,UAAA,0kEAAA,QAAA,CAAA,6sHAAA,EAAA,CAAA;cAK8B,oBAAkB,CAAA;UAAhD;WAAU,kBAAkB;;;;6EADlB,0BAAwB,EAAA,WAAA,4BAAA,UAAA,wFAAA,YAAA,GAAA,CAAA;AAAA,GAAA;;;AEnCrC,IAAM,SAAiB;EACrB;IACE,MAAM;;IACN,WAAW;;EAEb;IACE,MAAM;IACN,WAAW;;;AAQT,IAAO,2BAAP,MAAO,0BAAwB;;qCAAxB,2BAAwB;EAAA;wEAAxB,0BAAwB,CAAA;4EAHzB,aAAa,SAAS,MAAM,GAC5B,YAAY,EAAA,CAAA;;;sEAEX,0BAAwB,CAAA;UAJpC;WAAS;MACR,SAAS,CAAC,aAAa,SAAS,MAAM,CAAC;MACvC,SAAS,CAAC,YAAY;KACvB;;;;;ACTK,IAAO,sBAAP,MAAO,qBAAmB;;qCAAnB,sBAAmB;EAAA;yEAAnB,sBAAmB,WAAA,CAAA,CAAA,gBAAA,CAAA,GAAA,OAAA,GAAA,MAAA,GAAA,UAAA,SAAA,6BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACVhC,MAAA,yBAAA,GAAA,GAAA;AAAG,MAAA,iBAAA,GAAA,mBAAA;AAAiB,MAAA,uBAAA;;oBDMR,YAAY,GAAA,eAAA,EAAA,CAAA;;;sEAIX,qBAAmB,CAAA;UAP/B;uBACW,kBAAgB,YACd,MAAI,SACP,CAAC,YAAY,GAAC,UAAA,+BAAA,CAAA;;;;6EAIZ,qBAAmB,EAAA,WAAA,uBAAA,UAAA,+EAAA,YAAA,GAAA,CAAA;AAAA,GAAA;;;AEA1B,IAAO,sBAAP,MAAO,qBAAmB;;qCAAnB,sBAAmB;EAAA;yEAAnB,sBAAmB,WAAA,CAAA,CAAA,gBAAA,CAAA,GAAA,OAAA,GAAA,MAAA,GAAA,UAAA,SAAA,6BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACVhC,MAAA,yBAAA,GAAA,GAAA;AAAG,MAAA,iBAAA,GAAA,mBAAA;AAAiB,MAAA,uBAAA;;oBDMR,YAAY,GAAA,eAAA,EAAA,CAAA;;;sEAIX,qBAAmB,CAAA;UAP/B;uBACW,kBAAgB,YACd,MAAI,SACP,CAAC,YAAY,GAAC,UAAA,+BAAA,CAAA;;;;6EAIZ,qBAAmB,EAAA,WAAA,uBAAA,UAAA,+EAAA,YAAA,GAAA,CAAA;AAAA,GAAA;;;AEsB1B,IAAO,oBAAP,MAAO,mBAAiB;;qCAAjB,oBAAiB;EAAA;wEAAjB,mBAAiB,CAAA;;IAf1B;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;EAAwB,EAAA,CAAA;;;sEAMf,mBAAiB,CAAA;UApB7B;WAAS;MACR,cAAc;;;MAGd,SAAS;QACP;QACA;QACA;QACA;;QAEA;QACA;QACA;QACA;QACA;;MAEF,SAAS;;;KAGV;;;", "names": ["event"]}
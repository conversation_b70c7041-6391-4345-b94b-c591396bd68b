import './polyfills.server.mjs';
import {
  destroyAngularServerApp,
  extractRoutesAndCreateRouteTree,
  getOrCreateAngularServerApp,
  main_server_default,
  setAngularAppManifest
} from "./chunk-QXOJH7I6.mjs";
import "./chunk-VBQR5DVL.mjs";
import "./chunk-2CYAPXQH.mjs";
import "./chunk-EQJNO3NM.mjs";
import {
  resetCompiledComponents
} from "./chunk-LMSTY3IA.mjs";
import "./chunk-5WKMABBB.mjs";

// angular:main-server-inject-manifest:angular:main-server-inject-manifest
import manifest from "./angular-app-manifest.mjs";
setAngularAppManifest(manifest);
export {
  main_server_default as default,
  destroyAngularServerApp as \u0275destroyAngularServerApp,
  extractRoutesAndCreateRouteTree as \u0275extractRoutesAndCreateRouteTree,
  getOrCreateAngularServerApp as \u0275getOrCreateAngularServerApp,
  resetCompiledComponents as \u0275resetCompiledComponents
};
//# sourceMappingURL=main.server.mjs.map

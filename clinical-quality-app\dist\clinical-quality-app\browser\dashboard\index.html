<!DOCTYPE html><html lang="en"><head>
  <meta charset="utf-8">
  <title>ClinicalQualityApp</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/png" href="assets/logos/Stellarus-Favicon-red.png">
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&amp;display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
<link rel="stylesheet" href="styles.css"><style ng-app-id="ng">

.dashboard-container[_ngcontent-ng-c3160001710] {
  padding: 20px;
  background-color: var(--light-background, #F9FBFC);
  min-height: calc(100vh - 80px);
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.dashboard-section[_ngcontent-ng-c3160001710] {
  background-color: var(--white, white);
  border-radius: 8px;
  border: 1px solid var(--light-borders, #F1F5F7);
  padding: 20px;
  display: flex;
  flex-direction: column;
}
.section-header[_ngcontent-ng-c3160001710] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20px;
}
.section-header[_ngcontent-ng-c3160001710]   h2[_ngcontent-ng-c3160001710] {
  font-size: 20px;
  font-weight: 600;
  line-height: 32px;
  color: var(--text-black, #17181A);
  margin: 0;
}
.section-actions[_ngcontent-ng-c3160001710] {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  justify-content: flex-end;
}
/*# sourceMappingURL=/dashboard-page.component.css.map */</style><style ng-app-id="ng">

.menu-container[_ngcontent-ng-c3806090911] {
  width: 100%;
  height: 80px;
  background: #FFFFFF;
  border-bottom: 1px solid #F1F5F7;
  position: sticky;
  top: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
}
.menu-content[_ngcontent-ng-c3806090911] {
  width: 100%;
  height: 100%;
  padding: 12px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.logo-section[_ngcontent-ng-c3806090911] {
  flex: 1;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.logo-container[_ngcontent-ng-c3806090911] {
  width: 240px;
  padding: 10px 20px;
  background: #FFFFFF;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: opacity 0.2s ease;
}
.logo-container[_ngcontent-ng-c3806090911]:hover {
  opacity: 0.8;
}
.logo-image[_ngcontent-ng-c3806090911] {
  width: 150px;
  height: 37.4px;
  object-fit: contain;
}
.logo-placeholder[_ngcontent-ng-c3806090911] {
  font-size: 18px;
  font-family: "Urbane", sans-serif;
  font-weight: 600;
  color: #17181A;
}
.user-section[_ngcontent-ng-c3806090911] {
  position: relative;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 16px;
}
.user-info[_ngcontent-ng-c3806090911] {
  padding: 12px 20px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 12px;
}
.user-name[_ngcontent-ng-c3806090911] {
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  font-weight: 300;
  line-height: 20px;
  color: #17181A;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.user-avatar[_ngcontent-ng-c3806090911] {
  cursor: pointer;
  transition: transform 0.2s ease;
}
.user-avatar[_ngcontent-ng-c3806090911]:hover {
  transform: scale(1.05);
}
.avatar-circle[_ngcontent-ng-c3806090911] {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #F1F5F7;
  border: 1px solid #D9E1E7;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}
.avatar-circle[_ngcontent-ng-c3806090911]:hover {
  border-color: #547996;
  background: #FFFFFF;
}
.user-icon[_ngcontent-ng-c3806090911] {
  width: 20px;
  height: 20px;
  color: #547996;
  transition: color 0.2s ease;
}
.avatar-circle[_ngcontent-ng-c3806090911]:hover   .user-icon[_ngcontent-ng-c3806090911] {
  color: #17181A;
}
.dropdown-arrow[_ngcontent-ng-c3806090911] {
  cursor: pointer;
  transition: transform 0.2s ease;
  padding: 4px;
}
.dropdown-arrow.open[_ngcontent-ng-c3806090911] {
  transform: rotate(180deg);
}
.dropdown-arrow[_ngcontent-ng-c3806090911]:hover {
  background-color: #F1F5F7;
  border-radius: 4px;
}
.arrow-icon[_ngcontent-ng-c3806090911] {
  width: 16px;
  height: 16px;
  color: #547996;
  transition: color 0.2s ease;
}
.dropdown-arrow[_ngcontent-ng-c3806090911]:hover   .arrow-icon[_ngcontent-ng-c3806090911] {
  color: #17181A;
}
.user-dropdown[_ngcontent-ng-c3806090911] {
  position: absolute;
  top: 100%;
  right: 0;
  background: #FFFFFF;
  border-radius: 10px;
  border: 1px solid #D9E1E7;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1001;
  margin-top: 8px;
  min-width: 200px;
  overflow: hidden;
}
.dropdown-content[_ngcontent-ng-c3806090911] {
  padding: 8px 0;
}
.dropdown-item[_ngcontent-ng-c3806090911] {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  font-weight: 300;
  color: #17181A;
}
.dropdown-item[_ngcontent-ng-c3806090911]:hover {
  background-color: #F1F5F7;
}
.dropdown-item[_ngcontent-ng-c3806090911]:active {
  background-color: #D9E1E7;
}
.item-icon[_ngcontent-ng-c3806090911] {
  font-size: 14px;
  width: 16px;
  text-align: center;
}
.item-label[_ngcontent-ng-c3806090911] {
  flex: 1;
}
@media (min-width: 600px) {
  .menu-content[_ngcontent-ng-c3806090911] {
    padding: 12px 20px;
  }
  .logo-container[_ngcontent-ng-c3806090911] {
    width: 200px;
    padding: 8px 16px;
  }
  .logo-image[_ngcontent-ng-c3806090911] {
    width: 120px;
    height: 30px;
  }
}
@media (max-width: 599px) {
  .menu-content[_ngcontent-ng-c3806090911] {
    padding: 8px 16px;
  }
  .logo-container[_ngcontent-ng-c3806090911] {
    width: auto;
    padding: 4px 8px;
  }
  .logo-image[_ngcontent-ng-c3806090911] {
    width: 100px;
    height: 25px;
  }
  .user-name[_ngcontent-ng-c3806090911] {
    display: none;
  }
  .user-dropdown[_ngcontent-ng-c3806090911] {
    right: -16px;
    min-width: 180px;
  }
}
/*# sourceMappingURL=/menu.component.css.map */</style><style ng-app-id="ng">

@font-face {
  font-family: "Urbane";
  src: url("./media/Urbane-Light.woff2") format("woff2"), url("./media/Urbane-Light.woff") format("woff");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Urbane";
  src: url("./media/Urbane-Medium.woff2") format("woff2"), url("./media/Urbane-Medium.woff") format("woff");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Urbane";
  src: url("./media/Urbane-Medium.woff2") format("woff2"), url("./media/Urbane-Medium.woff") format("woff");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Urbane";
  src: url("./media/Urbane-DemiBold.woff2") format("woff2"), url("./media/Urbane-DemiBold.woff") format("woff");
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}
.text-xs[_ngcontent-ng-c502759576] {
  font-size: 10px;
  font-family: "Urbane", sans-serif;
  line-height: 20px;
}
.text-sm[_ngcontent-ng-c502759576] {
  font-size: 11px;
  font-family: "Urbane", sans-serif;
  line-height: 20px;
}
.text-base[_ngcontent-ng-c502759576] {
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  line-height: 20px;
}
.text-md[_ngcontent-ng-c502759576] {
  font-size: 14px;
  font-family: "Urbane", sans-serif;
  line-height: 20px;
}
.text-lg[_ngcontent-ng-c502759576] {
  font-size: 16px;
  font-family: "Urbane", sans-serif;
  line-height: 20px;
}
.text-xl[_ngcontent-ng-c502759576] {
  font-size: 20px;
  font-family: "Urbane", sans-serif;
  line-height: 32px;
}
.text-xxl[_ngcontent-ng-c502759576] {
  font-size: 24px;
  font-family: "Urbane", sans-serif;
  line-height: 32px;
}
.font-light[_ngcontent-ng-c502759576] {
  font-weight: 300;
}
.font-regular[_ngcontent-ng-c502759576] {
  font-weight: 400;
}
.font-medium[_ngcontent-ng-c502759576] {
  font-weight: 500;
}
.font-semibold[_ngcontent-ng-c502759576] {
  font-weight: 600;
}
.label-text[_ngcontent-ng-c502759576] {
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  line-height: 20px;
  font-weight: 300;
  color: #17181A;
}
.link-text[_ngcontent-ng-c502759576] {
  font-size: 12px;
  font-family: "Urbane", sans-serif;
  line-height: 20px;
  font-weight: 500;
  color: #0071BC;
}
.heading-text[_ngcontent-ng-c502759576] {
  font-size: 20px;
  font-family: "Urbane", sans-serif;
  line-height: 32px;
  font-weight: 600;
  color: #17181A;
}
.subheading-text[_ngcontent-ng-c502759576] {
  font-size: 14px;
  font-family: "Urbane", sans-serif;
  line-height: 20px;
  font-weight: 600;
  color: #17181A;
}
.caption-text[_ngcontent-ng-c502759576] {
  font-size: 10px;
  font-family: "Urbane", sans-serif;
  line-height: 20px;
  font-weight: 500;
  color: #547996;
}
body[_ngcontent-ng-c502759576] {
  font-family: "Urbane", sans-serif;
  font-size: 12px;
  line-height: 20px;
  color: #17181A;
}
.button[_ngcontent-ng-c502759576] {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border-radius: 8px;
  font-family: "Urbane", sans-serif;
  font-size: 12px;
  font-weight: 500;
  line-height: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  outline: none;
}
.button[_ngcontent-ng-c502759576]:focus {
  outline: none;
}
.button.button-with-icon[_ngcontent-ng-c502759576] {
  gap: 8px;
}
.button[_ngcontent-ng-c502759576]   .button-icon[_ngcontent-ng-c502759576] {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}
.button[_ngcontent-ng-c502759576]   app-refresh-icon[_ngcontent-ng-c502759576] {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  vertical-align: middle;
  flex-shrink: 0;
}
.button.btn-icon-right[_ngcontent-ng-c502759576] {
  flex-direction: row-reverse;
}
.button-primary[_ngcontent-ng-c502759576] {
  background-color: #3870B8;
  color: #FFFFFF;
  padding: 8px 16px;
}
.button-primary.figma-exact[_ngcontent-ng-c502759576] {
  padding: 10px 16px;
}
.button-primary.figma-state-inactive[_ngcontent-ng-c502759576] {
  background-color: #BFD0EE;
  color: #FFFFFF;
  cursor: not-allowed;
}
.button-primary.figma-state-inactive[_ngcontent-ng-c502759576]:hover {
  background-color: #BFD0EE;
}
.button-primary.figma-state-default[_ngcontent-ng-c502759576] {
  background-color: #3870B8;
  color: #FFFFFF;
}
.button-primary.figma-state-hover[_ngcontent-ng-c502759576] {
  background-color: #468CE7;
  color: #FFFFFF;
}
.button-primary.figma-state-click[_ngcontent-ng-c502759576] {
  background-color: #285082;
  color: #FFFFFF;
}
.button-primary[_ngcontent-ng-c502759576]:not(.figma-state-inactive):not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click):hover {
  background-color: #468CE7;
}
.button-primary[_ngcontent-ng-c502759576]:not(.figma-state-inactive):not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click):active {
  background-color: #285082;
}
.button-primary.figma-exact[_ngcontent-ng-c502759576]:not(.figma-state-inactive):not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click):hover {
  background-color: #468CE7;
}
.button-primary.figma-exact[_ngcontent-ng-c502759576]:not(.figma-state-inactive):not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click):active {
  background-color: #285082;
}
.button-primary.button-disabled[_ngcontent-ng-c502759576] {
  background-color: #BFD0EE;
  color: #FFFFFF;
  cursor: not-allowed;
}
.button-secondary[_ngcontent-ng-c502759576] {
  background-color: #FFFFFF;
  color: #17181A;
  border: 1px solid #D9E1E7;
  padding: 8px 12px;
}
.button-secondary.figma-exact[_ngcontent-ng-c502759576] {
  padding: 8px 14px;
}
.button-secondary.figma-state-default[_ngcontent-ng-c502759576] {
  background-color: #FFFFFF;
  color: #17181A;
  border: 1px solid #D9E1E7;
}
.button-secondary.figma-state-default[_ngcontent-ng-c502759576]   app-refresh-icon[_ngcontent-ng-c502759576] {
  color: #17181A;
}
.button-secondary.figma-state-hover[_ngcontent-ng-c502759576] {
  background-color: #F1F5F7;
  color: #17181A;
  border: 1px solid #D9E1E7;
}
.button-secondary.figma-state-hover[_ngcontent-ng-c502759576]   app-refresh-icon[_ngcontent-ng-c502759576] {
  color: #17181A;
}
.button-secondary.figma-state-click[_ngcontent-ng-c502759576] {
  background-color: #17181A;
  color: #FFFFFF;
  border: 1px solid #D9E1E7;
}
.button-secondary.figma-state-click[_ngcontent-ng-c502759576]   app-refresh-icon[_ngcontent-ng-c502759576] {
  color: #FFFFFF;
}
.button-secondary[_ngcontent-ng-c502759576]:not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click):hover {
  background-color: #F1F5F7;
}
.button-secondary[_ngcontent-ng-c502759576]:not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click):active {
  background-color: #17181A;
  color: #FFFFFF;
}
.button-secondary.figma-exact[_ngcontent-ng-c502759576]:not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click)   app-refresh-icon[_ngcontent-ng-c502759576] {
  color: #17181A;
}
.button-secondary.figma-exact[_ngcontent-ng-c502759576]:not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click):hover {
  background-color: #F1F5F7;
}
.button-secondary.figma-exact[_ngcontent-ng-c502759576]:not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click):hover   app-refresh-icon[_ngcontent-ng-c502759576] {
  color: #17181A;
}
.button-secondary.figma-exact[_ngcontent-ng-c502759576]:not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click):active {
  background-color: #17181A;
  color: #FFFFFF;
}
.button-secondary.figma-exact[_ngcontent-ng-c502759576]:not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click):active   app-refresh-icon[_ngcontent-ng-c502759576] {
  color: #FFFFFF;
}
.button-secondary.button-disabled[_ngcontent-ng-c502759576] {
  color: #547996;
  border-color: #F1F5F7;
  cursor: not-allowed;
}
.button-tertiary[_ngcontent-ng-c502759576] {
  background-color: transparent;
  color: #3870B8;
  box-shadow: none;
}
.button-tertiary[_ngcontent-ng-c502759576]:hover {
  background-color: rgba(56, 112, 184, 0.05);
}
.button-tertiary[_ngcontent-ng-c502759576]:active {
  background-color: rgba(56, 112, 184, 0.1);
}
.button-tertiary.button-disabled[_ngcontent-ng-c502759576] {
  color: #547996;
  cursor: not-allowed;
}
.button-sm[_ngcontent-ng-c502759576] {
  padding: 8px 12px;
  font-size: 11px;
}
.button-lg[_ngcontent-ng-c502759576] {
  padding: 16px 20px;
  font-size: 14px;
}
.button-block[_ngcontent-ng-c502759576] {
  width: 100%;
  display: flex;
}
/*# sourceMappingURL=/button.component.css.map */</style><style ng-app-id="ng">

[_nghost-ng-c2659116941] {
  display: inline-block;
  width: 16px;
  height: 16px;
}
.refresh-icon-default[_ngcontent-ng-c2659116941] {
  color: #17181A;
}
.refresh-icon-hover[_ngcontent-ng-c2659116941] {
  color: #17181A;
}
.refresh-icon-click[_ngcontent-ng-c2659116941] {
  color: #FFFFFF;
}
svg[_ngcontent-ng-c2659116941] {
  width: 16px;
  height: 16px;
  display: block;
}
/*# sourceMappingURL=/refresh-icon.component.css.map */</style><style ng-app-id="ng">

.assigned-table_1134-1191[_ngcontent-ng-c2702405285] {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 20px;
  box-sizing: border-box;
  width: 1380px;
}
.table_1134-1192[_ngcontent-ng-c2702405285] {
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  border-radius: 8px;
  border-color: #f1f5f7;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  width: 100%;
}
.table_1134-1208[_ngcontent-ng-c2702405285] {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: 100%;
}
.columns_1134-1209[_ngcontent-ng-c2702405285] {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: 100%;
}
.column_1134-1210[_ngcontent-ng-c2702405285] {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: 100%;
}
.header-item_1134-1211[_ngcontent-ng-c2702405285] {
  padding: 0px 12px 0px 12px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  width: 100%;
}
.table-item_1134-1212[_ngcontent-ng-c2702405285] {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 68px;
}
.text-label_1134-1214[_ngcontent-ng-c2702405285] {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}
.table-item_1134-1215[_ngcontent-ng-c2702405285] {
  padding: 13px 12px 13px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 68px;
}
.text-label_1134-1216[_ngcontent-ng-c2702405285] {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}
.column_1235-930[_ngcontent-ng-c2702405285] {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: 107px;
}
.header-item_1235-931[_ngcontent-ng-c2702405285] {
  padding: 0px 12px 0px 12px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  width: 100%;
}
.table-item_1235-932[_ngcontent-ng-c2702405285] {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 68px;
}
.text-label_1235-934[_ngcontent-ng-c2702405285] {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}
.table-item_1235-939[_ngcontent-ng-c2702405285] {
  padding: 10px 12px 10px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 68px;
}
.icon-text_1235-940[_ngcontent-ng-c2702405285] {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_1235-941[_ngcontent-ng-c2702405285] {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}
.column_1134-1235[_ngcontent-ng-c2702405285] {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: 106px;
}
.header-item_1134-1236[_ngcontent-ng-c2702405285] {
  padding: 0px 12px 0px 12px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  width: 100%;
}
.table-item_1134-1237[_ngcontent-ng-c2702405285] {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 68px;
}
.text-label_1134-1239[_ngcontent-ng-c2702405285] {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}
.table-item_1134-1240[_ngcontent-ng-c2702405285] {
  padding: 10px 12px 10px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 68px;
}
.icon-text_1134-1241[_ngcontent-ng-c2702405285] {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_1134-1242[_ngcontent-ng-c2702405285] {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}
.column_1235-969[_ngcontent-ng-c2702405285] {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: 102px;
}
.header-item_1235-970[_ngcontent-ng-c2702405285] {
  padding: 0px 12px 0px 12px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  width: 100%;
}
.table-item_1235-971[_ngcontent-ng-c2702405285] {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 68px;
}
.text-label_1235-973[_ngcontent-ng-c2702405285] {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}
.table-item_1235-974[_ngcontent-ng-c2702405285] {
  padding: 10px 12px 10px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 68px;
}
.icon-text_1235-975[_ngcontent-ng-c2702405285] {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_1235-979[_ngcontent-ng-c2702405285] {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 78px;
}
.column_1134-1270[_ngcontent-ng-c2702405285] {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: 99px;
}
.header-item_1134-1271[_ngcontent-ng-c2702405285] {
  padding: 0px 12px 0px 12px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  width: 100%;
}
.table-item_1134-1272[_ngcontent-ng-c2702405285] {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 68px;
}
.text-label_1134-1274[_ngcontent-ng-c2702405285] {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}
.table-item_1134-1275[_ngcontent-ng-c2702405285] {
  padding: 10px 12px 10px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 68px;
}
.icon-text_1134-1276[_ngcontent-ng-c2702405285] {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_1134-1277[_ngcontent-ng-c2702405285] {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}
.no-charts-message[_ngcontent-ng-c2702405285] {
  padding: 40px 20px;
  text-align: center;
  font-size: 14px;
  font-family: "Urbane", sans-serif;
  color: #757575;
  font-style: italic;
  background: #ffffff;
}
/*# sourceMappingURL=/assigned-table.component.css.map */</style><style ng-app-id="ng">

.submit-button-default[_ngcontent-ng-c2891085390] {
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 12px;
  font-family: Urbane;
  font-weight: 500;
  line-height: 20px;
  text-align: center;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 80px;
  transition: all 0.2s ease;
  background-color: #1976d2;
  color: #ffffff;
  border: none;
  cursor: pointer;
}
.submit-button-default[_ngcontent-ng-c2891085390]:hover {
  background-color: #1565c0;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.submit-button-default[_ngcontent-ng-c2891085390]:active {
  background-color: #0d47a1;
  transform: translateY(0);
}
.submit-button-inactive[_ngcontent-ng-c2891085390] {
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 12px;
  font-family: Urbane;
  font-weight: 500;
  line-height: 20px;
  text-align: center;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 80px;
  background-color: #BFD0EE;
  color: #ffffff;
  border: none;
  cursor: not-allowed;
  opacity: 0.8;
}
.submit-button-inactive[_ngcontent-ng-c2891085390]:hover {
  background-color: #BFD0EE;
  transform: none;
  box-shadow: none;
}
/*# sourceMappingURL=/submit-button.component.css.map */</style></head>
<body class="mat-typography"><!--nghm--><script type="text/javascript" id="ng-event-dispatch-contract">(()=>{function p(t,n,r,o,e,i,f,m){return{eventType:t,event:n,targetElement:r,eic:o,timeStamp:e,eia:i,eirp:f,eiack:m}}function u(t){let n=[],r=e=>{n.push(e)};return{c:t,q:n,et:[],etc:[],d:r,h:e=>{r(p(e.type,e,e.target,t,Date.now()))}}}function s(t,n,r){for(let o=0;o<n.length;o++){let e=n[o];(r?t.etc:t.et).push(e),t.c.addEventListener(e,t.h,r)}}function c(t,n,r,o,e=window){let i=u(t);e._ejsas||(e._ejsas={}),e._ejsas[n]=i,s(i,r),s(i,o,!0)}window.__jsaction_bootstrap=c;})();
</script><script>window.__jsaction_bootstrap(document.body,"ng",["click","mousedown","mouseup"],[]);</script>
  <app-root ng-version="19.2.9" ngh="5" ng-server-context="ssg"><router-outlet></router-outlet><app-dashboard-page _nghost-ng-c3160001710="" ngh="4"><app-menu _ngcontent-ng-c3160001710="" logosrc="assets/logos/Stellarus_logo_2C_blacktype.png?v=2024" logoalt="Stellarus Logo" _nghost-ng-c3806090911="" ng-reflect-logo-src="assets/logos/Stellarus_logo_2C" ng-reflect-logo-alt="Stellarus Logo" ng-reflect-user="[object Object]" ng-reflect-menu-items="[object Object],[object Object" ngh="0"><nav _ngcontent-ng-c3806090911="" class="menu-container"><div _ngcontent-ng-c3806090911="" class="menu-content"><div _ngcontent-ng-c3806090911="" class="logo-section"><div _ngcontent-ng-c3806090911="" class="logo-container" jsaction="click:;"><img _ngcontent-ng-c3806090911="" class="logo-image" src="assets/logos/Stellarus_logo_2C_blacktype.png?v=2024" alt="Stellarus Logo"><!--bindings={
  "ng-reflect-ng-if": "assets/logos/Stellarus_logo_2C"
}--><!--bindings={
  "ng-reflect-ng-if": "false"
}--></div></div><div _ngcontent-ng-c3806090911="" class="user-section"><div _ngcontent-ng-c3806090911="" class="user-info"><span _ngcontent-ng-c3806090911="" class="user-name">Jane Chu</span><div _ngcontent-ng-c3806090911="" class="user-avatar" jsaction="click:;"><div _ngcontent-ng-c3806090911="" class="avatar-circle"><svg _ngcontent-ng-c3806090911="" viewBox="0 0 20 20" fill="none" class="user-icon"><circle _ngcontent-ng-c3806090911="" cx="10" cy="7.5" r="2.75" stroke="currentColor" stroke-width="1.5" fill="none"></circle><path _ngcontent-ng-c3806090911="" d="M4.5 16.5c0-3 2.5-5.5 5.5-5.5s5.5 2.5 5.5 5.5" stroke="currentColor" stroke-width="1.5" fill="none"></path><circle _ngcontent-ng-c3806090911="" cx="10" cy="10" r="8.33" stroke="currentColor" stroke-width="1.5" fill="none"></circle></svg></div></div><div _ngcontent-ng-c3806090911="" class="dropdown-arrow" jsaction="click:;"><svg _ngcontent-ng-c3806090911="" viewBox="0 0 24 24" fill="none" class="arrow-icon"><path _ngcontent-ng-c3806090911="" d="M8 10l4 4 4-4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></div><!--bindings={
  "ng-reflect-ng-if": "true"
}--></div><!--bindings={
  "ng-reflect-ng-if": "false"
}--></div><!--bindings={
  "ng-reflect-ng-if": "[object Object]"
}--></div></nav></app-menu><div _ngcontent-ng-c3160001710="" class="dashboard-container"><div _ngcontent-ng-c3160001710="" class="dashboard-section"><div _ngcontent-ng-c3160001710="" class="section-header"><h2 _ngcontent-ng-c3160001710="">Assigned charts</h2><div _ngcontent-ng-c3160001710="" class="section-actions"><app-button _ngcontent-ng-c3160001710="" variant="secondary" _nghost-ng-c502759576="" ng-reflect-variant="secondary" ngh="1"><button _ngcontent-ng-c502759576="" type="button" ng-reflect-ng-class="button,button-secondary,,,butt" class="button button-secondary button-icon-left" jsaction="click:;mousedown:;mouseup:;"><!--bindings={
  "ng-reflect-ng-if": null
}--><app-refresh-icon _ngcontent-ng-c3160001710="" _nghost-ng-c2659116941="" ng-reflect-color="default" ngh="2"><svg _ngcontent-ng-c2659116941="" width="16" height="16" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" ng-reflect-ng-class="refresh-icon-default" class="refresh-icon-default"><path _ngcontent-ng-c2659116941="" d="M9.9993 18.3332C14.6017 18.3332 18.3327 14.6022 18.3327 9.9998C18.3327 5.3975 14.6017 1.6665 9.9993 1.6665C5.397 1.6665 1.666 5.3975 1.666 9.9998C1.666 14.6022 5.397 18.3332 9.9993 18.3332Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path _ngcontent-ng-c2659116941="" d="M6.6758 12.0917C6.8258 12.3417 7.0091 12.5751 7.2174 12.7834C8.7508 14.3167 11.2424 14.3167 12.7841 12.7834C13.4091 12.1584 13.7674 11.3667 13.8841 10.5583" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path _ngcontent-ng-c2659116941="" d="M6.1172 9.4417C6.2339 8.625 6.5922 7.8417 7.2172 7.2167C8.7505 5.6833 11.2422 5.6833 12.7839 7.2167C13.0005 7.4333 13.1755 7.6667 13.3255 7.9083" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path _ngcontent-ng-c2659116941="" d="M6.5176 14.3165V12.0916H8.7426" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path _ngcontent-ng-c2659116941="" d="M13.4848 5.6831V7.9081H11.2598" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></app-refresh-icon>Refresh charts <!--bindings={
  "ng-reflect-ng-if": null
}--></button></app-button></div></div><app-assigned-table _ngcontent-ng-c3160001710="" _nghost-ng-c2702405285="" ng-reflect-charts="[object Object],[object Object" ng-reflect-search-text="" ngh="3"><div _ngcontent-ng-c2702405285="" class="assigned-table_1134-1191"><div _ngcontent-ng-c2702405285="" class="table_1134-1192"><div _ngcontent-ng-c2702405285="" class="table_1134-1208"><div _ngcontent-ng-c2702405285="" class="columns_1134-1209"><div _ngcontent-ng-c2702405285="" class="column_1134-1210"><div _ngcontent-ng-c2702405285="" class="header-item_1134-1211"><div _ngcontent-ng-c2702405285="" class="table-item_1134-1212"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1214">Member ID</span></div></div><div _ngcontent-ng-c2702405285="" class="table-item_1134-1215"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1216">55820474</span></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1134-1215"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1216">302274401</span></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1134-1215"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1216">**********</span></div><!--ng-container--><!--bindings={
  "ng-reflect-ng-for-of": "[object Object],[object Object"
}--></div><div _ngcontent-ng-c2702405285="" class="column_1235-930"><div _ngcontent-ng-c2702405285="" class="header-item_1235-931"><div _ngcontent-ng-c2702405285="" class="table-item_1235-932"><span _ngcontent-ng-c2702405285="" class="text-label_1235-934">First name</span></div></div><div _ngcontent-ng-c2702405285="" class="table-item_1235-939"><div _ngcontent-ng-c2702405285="" class="icon-text_1235-940"><span _ngcontent-ng-c2702405285="" class="text-label_1235-941">JOHN</span></div></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1235-939"><div _ngcontent-ng-c2702405285="" class="icon-text_1235-940"><span _ngcontent-ng-c2702405285="" class="text-label_1235-941">ALMA</span></div></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1235-939"><div _ngcontent-ng-c2702405285="" class="icon-text_1235-940"><span _ngcontent-ng-c2702405285="" class="text-label_1235-941">JOANNE</span></div></div><!--ng-container--><!--bindings={
  "ng-reflect-ng-for-of": "[object Object],[object Object"
}--></div><div _ngcontent-ng-c2702405285="" class="column_1134-1235"><div _ngcontent-ng-c2702405285="" class="header-item_1134-1236"><div _ngcontent-ng-c2702405285="" class="table-item_1134-1237"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1239">Last name</span></div></div><div _ngcontent-ng-c2702405285="" class="table-item_1134-1240"><div _ngcontent-ng-c2702405285="" class="icon-text_1134-1241"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1242">DEY</span></div></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1134-1240"><div _ngcontent-ng-c2702405285="" class="icon-text_1134-1241"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1242">ANDERS</span></div></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1134-1240"><div _ngcontent-ng-c2702405285="" class="icon-text_1134-1241"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1242">SMITH</span></div></div><!--ng-container--><!--bindings={
  "ng-reflect-ng-for-of": "[object Object],[object Object"
}--></div><div _ngcontent-ng-c2702405285="" class="column_1235-969"><div _ngcontent-ng-c2702405285="" class="header-item_1235-970"><div _ngcontent-ng-c2702405285="" class="table-item_1235-971"><span _ngcontent-ng-c2702405285="" class="text-label_1235-973">Middle name</span></div></div><div _ngcontent-ng-c2702405285="" class="table-item_1235-974"><div _ngcontent-ng-c2702405285="" class="icon-text_1235-975"><!--bindings={
  "ng-reflect-ng-if": ""
}--></div></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1235-974"><div _ngcontent-ng-c2702405285="" class="icon-text_1235-975"><span _ngcontent-ng-c2702405285="" class="text-label_1235-979">G</span><!--bindings={
  "ng-reflect-ng-if": "G"
}--></div></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1235-974"><div _ngcontent-ng-c2702405285="" class="icon-text_1235-975"><!--bindings={
  "ng-reflect-ng-if": ""
}--></div></div><!--ng-container--><!--bindings={
  "ng-reflect-ng-for-of": "[object Object],[object Object"
}--></div><div _ngcontent-ng-c2702405285="" class="column_1134-1270"><div _ngcontent-ng-c2702405285="" class="header-item_1134-1271"><div _ngcontent-ng-c2702405285="" class="table-item_1134-1272"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1274">DOB</span></div></div><div _ngcontent-ng-c2702405285="" class="table-item_1134-1275"><div _ngcontent-ng-c2702405285="" class="icon-text_1134-1276"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1277">01/05/1972</span></div></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1134-1275"><div _ngcontent-ng-c2702405285="" class="icon-text_1134-1276"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1277">12/15/1953</span></div></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1134-1275"><div _ngcontent-ng-c2702405285="" class="icon-text_1134-1276"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1277">06/30/1951</span></div></div><!--ng-container--><!--bindings={
  "ng-reflect-ng-for-of": "[object Object],[object Object"
}--></div><div _ngcontent-ng-c2702405285="" class="column_1134-1305"><div _ngcontent-ng-c2702405285="" class="header-item_1134-1306"><div _ngcontent-ng-c2702405285="" class="table-item_1134-1307"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1309">LOB</span></div></div><div _ngcontent-ng-c2702405285="" class="table-item_1134-1310"><div _ngcontent-ng-c2702405285="" class="icon-text_1134-1311"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1312">MA HMO</span></div></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1134-1310"><div _ngcontent-ng-c2702405285="" class="icon-text_1134-1311"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1312">MA HMO</span></div></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1134-1310"><div _ngcontent-ng-c2702405285="" class="icon-text_1134-1311"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1312">MA HMO</span></div></div><!--ng-container--><!--bindings={
  "ng-reflect-ng-for-of": "[object Object],[object Object"
}--></div><div _ngcontent-ng-c2702405285="" class="column_1134-1340"><div _ngcontent-ng-c2702405285="" class="header-item_1134-1341"><div _ngcontent-ng-c2702405285="" class="table-item_1134-1342"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1344">Measure</span></div></div><div _ngcontent-ng-c2702405285="" class="table-item_1134-1345"><div _ngcontent-ng-c2702405285="" class="icon-text_1134-1346"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1347">CBP</span></div></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1134-1345"><div _ngcontent-ng-c2702405285="" class="icon-text_1134-1346"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1347">CBP</span></div></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1134-1345"><div _ngcontent-ng-c2702405285="" class="icon-text_1134-1346"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1347">CBP</span></div></div><!--ng-container--><!--bindings={
  "ng-reflect-ng-for-of": "[object Object],[object Object"
}--></div><div _ngcontent-ng-c2702405285="" class="column_1134-1375"><div _ngcontent-ng-c2702405285="" class="header-item_1134-1376"><div _ngcontent-ng-c2702405285="" class="table-item_1134-1377"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1379">Review 1</span></div></div><div _ngcontent-ng-c2702405285="" class="table-item_1134-1380"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1381">Jane Chu</span></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1134-1380"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1381">Jane Chu</span></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1134-1380"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1381">Jane Chu</span></div><!--ng-container--><!--bindings={
  "ng-reflect-ng-for-of": "[object Object],[object Object"
}--></div><div _ngcontent-ng-c2702405285="" class="column_1134-1400"><div _ngcontent-ng-c2702405285="" class="header-item_1134-1401"><div _ngcontent-ng-c2702405285="" class="table-item_1134-1402"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1404">Review 2</span></div></div><div _ngcontent-ng-c2702405285="" class="table-item_1134-1405"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1406">-</span></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1134-1405"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1406">-</span></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1134-1405"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1406">-</span></div><!--ng-container--><!--bindings={
  "ng-reflect-ng-for-of": "[object Object],[object Object"
}--></div><div _ngcontent-ng-c2702405285="" class="column_1134-1425"><div _ngcontent-ng-c2702405285="" class="header-item_1134-1426"><div _ngcontent-ng-c2702405285="" class="table-item_1134-1427"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1429">Assigned</span></div></div><div _ngcontent-ng-c2702405285="" class="table-item_1134-1430"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1431">04/15/25 1:30pm</span></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1134-1430"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1431">04/15/25 1:30pm</span></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1134-1430"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1431">04/15/25 1:30pm</span></div><!--ng-container--><!--bindings={
  "ng-reflect-ng-for-of": "[object Object],[object Object"
}--></div><div _ngcontent-ng-c2702405285="" class="column_1134-1450"><div _ngcontent-ng-c2702405285="" class="header-item_1134-1451"><div _ngcontent-ng-c2702405285="" class="table-item_1134-1452"><span _ngcontent-ng-c2702405285="" class="text-label_1134-1454">Status</span></div></div><div _ngcontent-ng-c2702405285="" class="table-item_1134-1455"><app-sumbit-button _ngcontent-ng-c2702405285="" class="sumbit-button_1134-1462" _nghost-ng-c2891085390="" ng-reflect-property1="Default" ngh="2" jsaction="click:;"><button _ngcontent-ng-c2891085390="" class="submit-button-default" jsaction="click:;"> Review </button></app-sumbit-button></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1134-1455"><app-sumbit-button _ngcontent-ng-c2702405285="" class="sumbit-button_1134-1462" _nghost-ng-c2891085390="" ng-reflect-property1="Default" ngh="2" jsaction="click:;"><button _ngcontent-ng-c2891085390="" class="submit-button-default" jsaction="click:;"> Review </button></app-sumbit-button></div><!--ng-container--><div _ngcontent-ng-c2702405285="" class="table-item_1134-1455"><app-sumbit-button _ngcontent-ng-c2702405285="" class="sumbit-button_1134-1462" _nghost-ng-c2891085390="" ng-reflect-property1="Default" ngh="2" jsaction="click:;"><button _ngcontent-ng-c2891085390="" class="submit-button-default" jsaction="click:;"> Review </button></app-sumbit-button></div><!--ng-container--><!--bindings={
  "ng-reflect-ng-for-of": "[object Object],[object Object"
}--></div></div></div><!--bindings={
  "ng-reflect-ng-if": "false"
}--></div></div></app-assigned-table></div></div></app-dashboard-page><!--container--></app-root>
<link rel="modulepreload" href="chunk-YLWVG4A5.js"><link rel="modulepreload" href="chunk-FVKW5FZS.js"><link rel="modulepreload" href="chunk-JRLQF6CE.js"><link rel="modulepreload" href="chunk-F5HTA5WY.js"><link rel="modulepreload" href="chunk-PC6IZSQ2.js"><script src="polyfills.js" type="module"></script><script src="main.js" type="module"></script>
<link rel="modulepreload" href="chunk-QO3WNN6R.js">


<script id="ng-state" type="application/json">{"873280017":{"b":"FILENAME,MBR_LNAME,MBR_MNAME,MBR_FNAME,MBR_DOB,LOB,MeasureKey,BSC_MBR_ID,MBR_GENDER,PRVR_NPI,PRVR_LNAME,PRVR_FNAME\r\nCBP_Redacted_John_Dey,DEY,,JOHN,1/5/1972,MA HMO,CBP,55820474,M,882716229,DEJONG,NICOLAS\r\nCBP_Redacted_Alma_Anders,ANDERS,G,ALMA,12/15/1953,MA HMO,CBP,302274401,F,771552845,RAMOS,THOMAS\r\nCBP_Redacted_Joanne_Smith,SMITH,,JOANNE,6/30/1951,MA HMO,CBP,**********,F,104297254,PETERSON,SAMANTHA\r\n","h":{},"s":200,"st":"OK","u":"assets/CSVs/MRSDS_Template_CQ(Template).csv","rt":"text"},"__nghData__":[{"t":{"4":"t0","5":"t1","6":"t2"},"c":{"4":[{"i":"t0","r":1}],"5":[],"6":[{"i":"t2","r":1,"t":{"10":"t3","11":"t4"},"c":{"10":[{"i":"t3","r":1}],"11":[]}}]}},{"t":{"1":"t5","3":"t6"},"c":{"1":[],"3":[]},"n":{"3":"0fn3"}},{},{"t":{"9":"t7","15":"t8","21":"t9","27":"t10","33":"t12","39":"t13","45":"t14","51":"t15","57":"t16","63":"t17","69":"t18","70":"t19"},"c":{"9":[{"i":"t7","r":2,"e":{"0":1},"x":3}],"15":[{"i":"t8","r":2,"e":{"0":1},"x":3}],"21":[{"i":"t9","r":2,"e":{"0":1},"x":3}],"27":[{"i":"t10","r":2,"e":{"0":1},"t":{"3":"t11"},"c":{"3":[]}},{"i":"t10","r":2,"e":{"0":1},"t":{"3":"t11"},"c":{"3":[{"i":"t11","r":1}]}},{"i":"t10","r":2,"e":{"0":1},"t":{"3":"t11"},"c":{"3":[]}}],"33":[{"i":"t12","r":2,"e":{"0":1},"x":3}],"39":[{"i":"t13","r":2,"e":{"0":1},"x":3}],"45":[{"i":"t14","r":2,"e":{"0":1},"x":3}],"51":[{"i":"t15","r":2,"e":{"0":1},"x":3}],"57":[{"i":"t16","r":2,"e":{"0":1},"x":3}],"63":[{"i":"t17","r":2,"e":{"0":1},"x":3}],"69":[{"i":"t18","r":2,"e":{"0":1},"x":3}],"70":[]}},{"n":{"9":"7f2n"}},{"c":{"0":[{"i":"c3160001710","r":1}]}}]}</script></body></html>
{"version": 3, "sources": ["src/app/shared/components/form-controls/dropdown/dropdown.component.scss", "src/styles/_variables.scss", "src/styles/_mixins.scss"], "sourcesContent": ["@use 'variables' as variables;\n@use 'mixins' as mix;\n\n.dropdown-container {\n  position: relative;\n  width: 100%;\n\n  &.disabled {\n    opacity: 0.5;\n    pointer-events: none;\n  }\n}\n\n.dropdown-label {\n  display: block;\n  font-size: 12px;\n  font-family: 'Urbane', sans-serif;\n  font-weight: 300;\n  color: variables.$text-black;\n  margin-bottom: variables.$spacing-xs;\n\n  .required-indicator {\n    color: variables.$primary-blue;\n    margin-left: 2px;\n  }\n}\n\n.dropdown-trigger {\n  padding: 4px; // Exact Figma padding from dropdown.scss line 4\n  display: flex; // Exact Figma display from dropdown.scss line 5\n  flex-direction: column; // Exact Figma flex-direction from dropdown.scss line 6\n  justify-content: center; // Exact Figma justify from dropdown.scss line 7\n  align-items: center; // Exact Figma align from dropdown.scss line 9\n  gap: 0px; // Exact Figma gap from dropdown.scss line 10\n  overflow: hidden; // Exact Figma overflow from dropdown.scss line 12\n  border-radius: 10px; // Exact Figma border-radius from dropdown.scss line 13\n  border: 1px solid #d9e1e7; // Exact Figma border from dropdown.scss lines 14-16\n  background: #ffffff; // Exact Figma background from dropdown.scss line 17\n  height: 48px; // Exact Figma height from dropdown.scss line 18\n  width: 100%; // Exact Figma width from dropdown.scss line 19\n  cursor: pointer;\n  transition: all 0.2s ease;\n  position: relative;\n  box-sizing: border-box;\n\n  &:hover:not(.disabled) {\n    border-color: #547996; // gray-3\n  }\n\n  &.open {\n    border-color: #547996; // gray-3\n  }\n\n  &:focus {\n    outline: none;\n    border-color: #1976d2; // primary-blue\n    box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.1);\n  }\n\n  &.disabled {\n    background-color: #f1f5f7; // gray-1\n    cursor: not-allowed;\n  }\n\n  &.has-error {\n    border-color: #F4454E;\n  }\n}\n\n.dropdown-content {\n  padding: 8px 12px 8px 12px; // Exact Figma padding from dropdown.scss line 22\n  display: flex; // Exact Figma display from dropdown.scss line 23\n  flex-direction: row; // Exact Figma flex-direction from dropdown.scss line 24\n  justify-content: space-between; // Exact Figma justify from dropdown.scss line 25\n  align-items: center; // Exact Figma align from dropdown.scss line 27\n  gap: 12px; // Exact Figma gap from dropdown.scss line 28\n  border-radius: 6px; // Exact Figma border-radius from dropdown.scss line 30\n  background: #ffffff; // Exact Figma background from dropdown.scss line 31\n  width: 100%; // Exact Figma width from dropdown.scss line 32\n  box-sizing: border-box;\n}\n\n.dropdown-text {\n  color: #547996; // Exact Figma color from dropdown.scss line 35 (gray-3)\n  font-size: 12px; // Exact Figma font-size from dropdown.scss line 36\n  font-family: Urbane; // Exact Figma font-family from dropdown.scss line 37\n  line-height: 20px; // Exact Figma line-height from dropdown.scss line 38\n  font-weight: 300; // Exact Figma font-weight from dropdown.scss line 41\n  text-align: left; // Exact Figma text-align from dropdown.scss line 42\n  text-wrap: wrap; // Exact Figma text-wrap from dropdown.scss line 43\n  width: 100%; // Exact Figma width from dropdown.scss line 44\n  flex: 1;\n\n  &.placeholder {\n    color: #547996; // gray-3\n  }\n\n  &.has-value {\n    color: #17181a; // text-black when has value\n  }\n}\n\n.dropdown-icon {\n  position: relative; // Exact Figma position from dropdown.scss line 48\n  display: flex; // Exact Figma display from dropdown.scss line 51\n  transition: transform 0.2s ease;\n  color: #547996; // gray-3\n  box-sizing: border-box; // Exact Figma box-sizing from dropdown.scss line 49\n  overflow: hidden; // Exact Figma overflow from dropdown.scss line 50\n\n  &.rotated {\n    transform: rotate(180deg);\n  }\n\n  svg {\n    top: 2px; // Exact Figma top from dropdown.scss line 54\n    left: 4px; // Exact Figma left from dropdown.scss line 55\n    position: absolute; // Exact Figma position from dropdown.scss line 56\n    display: flex; // Exact Figma display from dropdown.scss line 57\n    width: 14px;\n    height: 8px;\n  }\n}\n\n.dropdown-options {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  right: 0;\n  background: variables.$white;\n  border-radius: 10px;\n  border: 1px solid variables.$gray-3;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  z-index: 1000;\n  max-height: 300px;\n  overflow-y: auto;\n  margin-top: -1px; // Overlap with trigger border\n}\n\n.dropdown-separator {\n  height: 1px;\n  background: variables.$gray-2;\n  margin: 0 8px;\n}\n\n.dropdown-option {\n  display: flex;\n  align-items: center;\n  padding: 8px 12px;\n  gap: 12px;\n  cursor: pointer;\n  transition: background-color 0.2s ease;\n  border-radius: 6px;\n  margin: 0 4px;\n\n  &:hover:not(.disabled) {\n    background-color: variables.$gray-1;\n  }\n\n  &.selected {\n    background-color: variables.$light-background;\n  }\n\n  &.disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n}\n\n.option-checkbox {\n  display: flex;\n  align-items: center;\n\n  input[type=\"checkbox\"] {\n    @include mix.checkbox;\n    margin: 0;\n    pointer-events: none; // Prevent direct checkbox interaction\n  }\n}\n\n.option-text {\n  flex: 1;\n  font-size: 12px;\n  font-family: 'Urbane', sans-serif;\n  font-weight: 300;\n  line-height: 20px;\n  color: variables.$text-black;\n}\n\n.error-message {\n  margin-top: variables.$spacing-xs;\n  font-size: 11px;\n  font-family: 'Urbane', sans-serif;\n  font-weight: 300;\n  color: #F4454E;\n}\n\n// Responsive adjustments\n@include mix.for-phone-only {\n  .dropdown-options {\n    max-height: 200px;\n  }\n}\n", "// Color Variables\r\n$text-black: #17181A;\r\n$primary-blue: #3870B8;\r\n$hover-blue: #468CE7;\r\n$click-blue: #285082;\r\n$light-blue: #BFD0EE;\r\n$link: #0071BC;\r\n$gray-1: #F1F5F7;\r\n$gray-2: #D9E1E7;\r\n$gray-3: #547996;\r\n$gray-4: #384455;\r\n$success-green: #1AD598;\r\n$white: #FFFFFF;\r\n$background-gray: #F6F6F6;\r\n$light-background: #F9FBFC;\r\n$light-primary: #809FB8;\r\n\r\n// Opacity Variables\r\n$primary-blue-opacity-20: rgba(56, 112, 184, 0.20);\r\n$success-green-opacity-10: rgba(26, 213, 152, 0.10);\r\n$success-green-opacity-40: rgba(26, 213, 152, 0.40);\r\n\r\n// Spacing Variables\r\n$spacing-xs: 4px;\r\n$spacing-sm: 8px;\r\n$spacing-md: 12px;\r\n$spacing-lg: 16px;\r\n$spacing-xl: 20px;\r\n$spacing-xxl: 24px;\r\n$spacing-xxxl: 30px;\r\n$spacing-huge: 40px;\r\n$spacing-giant: 48px;\r\n$spacing-mega: 55px;\r\n\r\n// Border Radius\r\n$border-radius-sm: 5px;\r\n$border-radius-md: 6px;\r\n$border-radius-lg: 8px;\r\n$border-radius-xl: 10px;\r\n$border-radius-round: 90px;\r\n$border-radius-circle: 120px;\r\n\r\n// Box Shadow\r\n$box-shadow-sm: 0px 1px 2px rgba(16, 24, 40, 0.05);\r\n\r\n// Border Width\r\n$border-width-default: 1px;\r\n$border-width-thick: 1.5px;\r\n\r\n// Z-index\r\n$z-index-default: 1;\r\n$z-index-dropdown: 1000;\r\n$z-index-sticky: 1020;\r\n$z-index-fixed: 1030;\r\n$z-index-modal-backdrop: 1040;\r\n$z-index-modal: 1050;\r\n$z-index-popover: 1060;\r\n$z-index-tooltip: 1070;", "// Import variables\r\n@use 'variables' as variables;\r\n\r\n// Flexbox Mixins\r\n@mixin flex-row {\r\n  display: flex;\r\n  flex-direction: row;\r\n}\r\n\r\n@mixin flex-column {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n@mixin flex-center {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n@mixin flex-between {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n@mixin flex-start {\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  align-items: center;\r\n}\r\n\r\n@mixin flex-end {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  align-items: center;\r\n}\r\n\r\n// Layout Mixins\r\n@mixin container {\r\n  width: 100%;\r\n  padding-left: variables.$spacing-xxxl;\r\n  padding-right: variables.$spacing-xxxl;\r\n}\r\n\r\n@mixin card {\r\n  background: variables.$white;\r\n  border-radius: variables.$border-radius-lg;\r\n  outline: variables.$border-width-default variables.$gray-1 solid;\r\n  outline-offset: -(variables.$border-width-default);\r\n  padding: variables.$spacing-xl;\r\n  margin-bottom: variables.$spacing-xl;\r\n}\r\n\r\n// Button Mixins\r\n@mixin button-base {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  gap: variables.$spacing-sm;\r\n  border-radius: variables.$border-radius-lg;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  border: none;\r\n  outline: none;\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n@mixin button-primary {\r\n  @include button-base;\r\n  background: variables.$primary-blue;\r\n  color: variables.$white;\r\n  padding: variables.$spacing-sm variables.$spacing-lg;\r\n\r\n  &:hover {\r\n    background: variables.$hover-blue;\r\n  }\r\n\r\n  &:active {\r\n    background: variables.$click-blue;\r\n  }\r\n\r\n  &:disabled {\r\n    background: variables.$light-blue;\r\n    cursor: not-allowed;\r\n  }\r\n}\r\n\r\n@mixin button-secondary {\r\n  @include button-base;\r\n  background: variables.$white;\r\n  outline: variables.$border-width-default variables.$gray-2 solid;\r\n  outline-offset: -(variables.$border-width-default);\r\n  color: variables.$text-black;\r\n  padding: variables.$spacing-sm variables.$spacing-md;\r\n\r\n  &:hover {\r\n    background: variables.$gray-1;\r\n  }\r\n\r\n  &:active {\r\n    background: variables.$text-black;\r\n    color: variables.$white;\r\n  }\r\n}\r\n\r\n@mixin button-icon {\r\n  @include button-base;\r\n  gap: variables.$spacing-sm;\r\n\r\n  .icon {\r\n    width: 20px;\r\n    height: 20px;\r\n  }\r\n}\r\n\r\n// Form Element Mixins\r\n@mixin input-field {\r\n  padding: variables.$spacing-md;\r\n  border-radius: variables.$border-radius-xl;\r\n  outline: variables.$border-width-default variables.$gray-2 solid;\r\n  outline-offset: -(variables.$border-width-default);\r\n  font-size: 12px;\r\n  font-family: 'Urbane', sans-serif;\r\n  width: 100%;\r\n\r\n  &:focus {\r\n    outline-color: variables.$gray-3;\r\n  }\r\n}\r\n\r\n@mixin textarea-field {\r\n  padding: variables.$spacing-md;\r\n  border-radius: variables.$border-radius-xl;\r\n  outline: variables.$border-width-default variables.$gray-2 solid;\r\n  outline-offset: -(variables.$border-width-default);\r\n  font-size: 12px;\r\n  font-family: 'Urbane', sans-serif;\r\n  width: 100%;\r\n  min-height: 88px;\r\n\r\n  &:focus {\r\n    outline-color: variables.$gray-3;\r\n  }\r\n}\r\n\r\n@mixin checkbox {\r\n  width: 16px;\r\n  height: 16px;\r\n  border-radius: 5px; // Figma specifies 5px border radius\r\n  border: 1px solid variables.$gray-2;\r\n  background-color: variables.$white;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  appearance: none;\r\n  -webkit-appearance: none;\r\n  -moz-appearance: none;\r\n  position: relative;\r\n\r\n  &:checked {\r\n    background-color: variables.$text-black;\r\n    border-color: variables.$text-black;\r\n\r\n    &::after {\r\n      content: '';\r\n      position: absolute;\r\n      left: 4px;\r\n      top: 4px;\r\n      width: 8.33px;\r\n      height: 7.5px;\r\n      background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8.33' height='7.5' viewBox='0 0 8.33 7.5'%3E%3Cpath d='M1 3.75L3.5 6.25L7.33 1.25' stroke='white' stroke-width='1.5' fill='none' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E\");\r\n      background-repeat: no-repeat;\r\n      background-position: center;\r\n      background-size: contain;\r\n    }\r\n  }\r\n\r\n  &:hover:not(:disabled) {\r\n    border-color: variables.$gray-3;\r\n  }\r\n\r\n  &:focus {\r\n    outline: 2px solid variables.$primary-blue;\r\n    outline-offset: 2px;\r\n  }\r\n\r\n  &:disabled {\r\n    opacity: 0.5;\r\n    cursor: not-allowed;\r\n  }\r\n}\r\n\r\n// Table Mixins\r\n@mixin table-header {\r\n  padding: variables.$spacing-md;\r\n  border-bottom: variables.$border-width-default variables.$gray-1 solid;\r\n  font-weight: 500;\r\n  color: variables.$text-black;\r\n  height: 68px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n@mixin table-cell {\r\n  padding: variables.$spacing-md;\r\n  font-weight: 300;\r\n  height: 68px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-start;\r\n}\r\n\r\n\r\n\r\n// Icon Mixins\r\n@mixin icon-container {\r\n  width: 20px;\r\n  height: 20px;\r\n  position: relative;\r\n}\r\n\r\n// Status Indicators\r\n@mixin status-badge($color, $bg-color) {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: variables.$spacing-xs variables.$spacing-sm;\r\n  border-radius: variables.$border-radius-round;\r\n  background-color: $bg-color;\r\n  color: $color;\r\n  font-size: 11px;\r\n  font-weight: 300;\r\n}\r\n\r\n@mixin success-badge {\r\n  @include status-badge(variables.$success-green, variables.$success-green-opacity-10);\r\n  outline: variables.$border-width-default variables.$success-green-opacity-40 solid;\r\n  outline-offset: -(variables.$border-width-default);\r\n}\r\n\r\n// Responsive Mixins\r\n@mixin for-phone-only {\r\n  @media (max-width: 599px) { @content; }\r\n}\r\n\r\n@mixin for-tablet-portrait-up {\r\n  @media (min-width: 600px) { @content; }\r\n}\r\n\r\n@mixin for-tablet-landscape-up {\r\n  @media (min-width: 900px) { @content; }\r\n}\r\n\r\n@mixin for-desktop-up {\r\n  @media (min-width: 1200px) { @content; }\r\n}\r\n\r\n@mixin for-big-desktop-up {\r\n  @media (min-width: 1800px) { @content; }\r\n}"], "mappings": ";AAGA,CAAA;AACE,YAAA;AACA,SAAA;;AAEA,CAJF,kBAIE,CAAA;AACE,WAAA;AACA,kBAAA;;AAIJ,CAAA;AACE,WAAA;AACA,aAAA;AACA,eAAA,QAAA,EAAA;AACA,eAAA;AACA,SCjBW;ADkBX,iBCIW;;ADFX,CARF,eAQE,CAAA;AACE,SCpBW;ADqBX,eAAA;;AAIJ,CAAA;AACE,WAAA;AACA,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,eAAA;AACA,OAAA;AACA,YAAA;AACA,iBAAA;AACA,UAAA,IAAA,MAAA;AACA,cAAA;AACA,UAAA;AACA,SAAA;AACA,UAAA;AACA,cAAA,IAAA,KAAA;AACA,YAAA;AACA,cAAA;;AAEA,CAlBF,gBAkBE,MAAA,KAAA,CAtCA;AAuCE,gBAAA;;AAGF,CAtBF,gBAsBE,CAAA;AACE,gBAAA;;AAGF,CA1BF,gBA0BE;AACE,WAAA;AACA,gBAAA;AACA,cAAA,EAAA,EAAA,EAAA,IAAA,KAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAGF,CAhCF,gBAgCE,CApDA;AAqDE,oBAAA;AACA,UAAA;;AAGF,CArCF,gBAqCE,CAAA;AACE,gBAAA;;AAIJ,CAAA;AACE,WAAA,IAAA,KAAA,IAAA;AACA,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,eAAA;AACA,OAAA;AACA,iBAAA;AACA,cAAA;AACA,SAAA;AACA,cAAA;;AAGF,CAAA;AACE,SAAA;AACA,aAAA;AACA,eAAA;AACA,eAAA;AACA,eAAA;AACA,cAAA;AACA,aAAA;AACA,SAAA;AACA,QAAA;;AAEA,CAXF,aAWE,CAAA;AACE,SAAA;;AAGF,CAfF,aAeE,CAAA;AACE,SAAA;;AAIJ,CAAA;AACE,YAAA;AACA,WAAA;AACA,cAAA,UAAA,KAAA;AACA,SAAA;AACA,cAAA;AACA,YAAA;;AAEA,CARF,aAQE,CAAA;AACE,aAAA,OAAA;;AAGF,CAZF,cAYE;AACE,OAAA;AACA,QAAA;AACA,YAAA;AACA,WAAA;AACA,SAAA;AACA,UAAA;;AAIJ,CAAA;AACE,YAAA;AACA,OAAA;AACA,QAAA;AACA,SAAA;AACA,cCrHM;ADsHN,iBAAA;AACA,UAAA,IAAA,MAAA;AACA,cAAA,EAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,WAAA;AACA,cAAA;AACA,cAAA;AACA,cAAA;;AAGF,CAAA;AACE,UAAA;AACA,cCrIO;ADsIP,UAAA,EAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,WAAA,IAAA;AACA,OAAA;AACA,UAAA;AACA,cAAA,iBAAA,KAAA;AACA,iBAAA;AACA,UAAA,EAAA;;AAEA,CAVF,eAUE,MAAA,KAAA,CApJA;AAqJE,oBCrJK;;ADwJP,CAdF,eAcE,CAAA;AACE,oBClJe;;ADqJjB,CAlBF,eAkBE,CA5JA;AA6JE,WAAA;AACA,UAAA;;AAIJ,CAAA;AACE,WAAA;AACA,eAAA;;AAEA,CAJF,gBAIE,KAAA,CAAA;AE1BA,SAAA;AACA,UAAA;AACA,iBAAA;AACA,UAAA,IAAA,MAAA;AACA,oBD3IM;AC4IN,UAAA;AACA,cAAA,IAAA,KAAA;AACA,cAAA;AACA,sBAAA;AACA,mBAAA;AACA,YAAA;AFkBE,UAAA;AACA,kBAAA;;AEjBF,CFUF,gBEVE,KAAA,CAAA,cAAA;AACE,oBD/JS;ACgKT,gBDhKS;;ACkKT,CFMJ,gBENI,KAAA,CAAA,cAAA,QAAA;AACE,WAAA;AACA,YAAA;AACA,QAAA;AACA,OAAA;AACA,SAAA;AACA,UAAA;AACA,oBAAA;AACA,qBAAA;AACA,uBAAA;AACA,mBAAA;;AAIJ,CFRF,gBEQE,KAAA,CAAA,cAAA,MAAA,KAAA;AACE,gBDzKK;;AC4KP,CFZF,gBEYE,KAAA,CAAA,cAAA;AACE,WAAA,IAAA,MAAA;AACA,kBAAA;;AAGF,CFjBF,gBEiBE,KAAA,CAAA,cAAA;AACE,WAAA;AACA,UAAA;;AFRJ,CAAA;AACE,QAAA;AACA,aAAA;AACA,eAAA,QAAA,EAAA;AACA,eAAA;AACA,eAAA;AACA,SCzLW;;AD4Lb,CAAA;AACE,cCvKW;ADwKX,aAAA;AACA,eAAA,QAAA,EAAA;AACA,eAAA;AACA,SAAA;;AEgDA,OAAA,CAAA,SAAA,EAAA;AF3CA,GA3EF;AA4EI,gBAAA;;;", "names": []}
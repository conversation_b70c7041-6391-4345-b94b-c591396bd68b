{"name": "regex-parser", "version": "2.3.1", "description": "A module that parses a string as regular expression and returns the parsed value.", "main": "lib/index.js", "typings": "lib/typings/regex-parser.d.ts", "scripts": {"test": "vows --spec --isolate"}, "repository": {"type": "git", "url": "**************:IonicaBizau/regex-parser.js.git"}, "keywords": ["regular", "expressions", "node", "parser", "string"], "author": "Ionic<PERSON> Bizău <<EMAIL>> (https://ionicabizau.net)", "license": "MIT", "bugs": {"url": "https://github.com/IonicaBizau/regex-parser.js/issues"}, "homepage": "https://github.com/IonicaBizau/regex-parser.js", "directories": {"test": "test"}, "devDependencies": {"vows": "^0.8.1"}, "files": ["bin/", "app/", "lib/", "dist/", "src/", "scripts/", "resources/", "menu/", "cli.js", "index.js", "index.d.ts", "package-lock.json", "bloggify.js", "bloggify.json", "bloggify/"]}
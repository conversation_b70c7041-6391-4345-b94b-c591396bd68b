
export default {
  bootstrap: () => import('./main.server.mjs').then(m => m.default),
  inlineCriticalCss: false,
  baseHref: '/',
  locale: undefined,
  routes: undefined,
  entryPointToBrowserMapping: {
  "src/app/features/dashboard/dashboard.module.ts": [
    {
      "path": "chunk-QO3WNN6R.js",
      "dynamicImport": false
    }
  ],
  "node_modules/pdfjs-dist/build/pdf.mjs": [
    {
      "path": "chunk-MSP5HGLP.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/chart-review/chart-review.module.ts": [
    {
      "path": "chunk-JQPK26PE.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-MSP5HGLP.js",
      "dynamicImport": true
    }
  ],
  "src/app/features/auth/auth.module.ts": [
    {
      "path": "chunk-WX24KWY2.js",
      "dynamicImport": false
    }
  ]
},
  assets: {
    'index.csr.html': {size: 952, hash: '832f9161b2034b8876f2497e3505367488623ccd8cdbf4efc9adf167efd24a2f', text: () => import('./assets-chunks/index_csr_html.mjs').then(m => m.default)},
    'index.server.html': {size: 1492, hash: 'fec2a66eea91e06101f9ed0afe9049edf5cf1ff9349a85d2153c9099b2424a6b', text: () => import('./assets-chunks/index_server_html.mjs').then(m => m.default)}
  },
};

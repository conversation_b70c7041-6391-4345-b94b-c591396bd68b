{"version": 3, "sources": ["src/app/shared/components/form-controls/results/results.component.scss"], "sourcesContent": ["@use 'variables' as variables;\n@use 'mixins' as mix;\n\n.results-container {\n  padding: 20px; // Exact Figma padding from results.scss line 4\n  display: flex; // Exact Figma display from results.scss line 5\n  flex-direction: column; // Exact Figma flex-direction from results.scss line 6\n  justify-content: center; // Exact Figma justify from results.scss line 7\n  align-items: stretch; // Changed to stretch for full width\n  gap: 0px; // Exact Figma gap from results.scss line 10\n  border-radius: 8px; // Exact Figma border-radius from results.scss line 12\n  border: 1px solid #f1f5f7; // Exact Figma border from results.scss lines 13-15\n  background: #ffffff; // Exact Figma background from results.scss line 16\n  width: 100%; // Exact Figma width from results.scss line 17\n  box-sizing: border-box;\n\n  &.disabled {\n    opacity: 0.6;\n    pointer-events: none;\n  }\n\n  &.has-error {\n    border-color: #F4454E;\n  }\n}\n\n.results-header {\n  padding: 0px 0px 12px 0px; // Exact Figma padding from results.scss line 20\n  display: flex; // Exact Figma display from results.scss line 21\n  flex-direction: row; // Exact Figma flex-direction from results.scss line 22\n  justify-content: flex-start; // Exact Figma justify from results.scss line 23\n  align-items: center; // Exact Figma align from results.scss line 25\n  gap: 20px; // Exact Figma gap from results.scss line 26\n  width: 100%; // Exact Figma width from results.scss line 28\n  box-sizing: border-box;\n}\n\n.results-title-section {\n  display: flex; // Exact Figma display from results.scss line 31\n  flex-direction: column; // Exact Figma flex-direction from results.scss line 32\n  justify-content: flex-start; // Exact Figma justify from results.scss line 33\n  align-items: flex-start; // Exact Figma align from results.scss line 35\n  gap: 12px; // Exact Figma gap from results.scss line 36\n  width: 100%; // Exact Figma width from results.scss line 38\n  box-sizing: border-box;\n}\n\n.results-title {\n  color: #17181A;\n  font-family: Urbane;\n  font-size: 20px;\n  font-style: normal;\n  font-weight: 600;\n  line-height: 32px; /* 160% */\n  text-align: left; // Exact Figma text-align from results.scss line 54\n  text-wrap: nowrap; // Exact Figma text-wrap from results.scss line 55\n  margin: 0;\n}\n\n.tab-navigation {\n  display: flex; // Exact Figma display from results.scss line 82\n  flex-direction: row; // Exact Figma flex-direction from results.scss line 83\n  justify-content: flex-start; // Exact Figma justify from results.scss line 84\n  align-items: center; // Exact Figma align from results.scss line 86\n  gap: 0px; // Exact Figma gap from results.scss line 87\n  border-bottom: 1px solid #d9e1e7; // Exact Figma border from results.scss lines 89-91\n  background: #ffffff; // Exact Figma background from results.scss line 92\n  width: 100%; // Exact Figma width from results.scss line 93\n  box-sizing: content-box; // Exact Figma box-sizing from results.scss line 88\n}\n\n.tab-button {\n  padding: 4px 8px 4px 8px; // Exact Figma padding from results.scss line 139\n  display: flex; // Exact Figma display from results.scss line 140\n  flex-direction: row; // Exact Figma flex-direction from results.scss line 141\n  justify-content: flex-start; // Exact Figma justify from results.scss line 142\n  align-items: center; // Exact Figma align from results.scss line 144\n  gap: 12px; // Exact Figma gap from results.scss line 145\n  height: 100%; // Exact Figma height from results.scss line 147\n  background: transparent;\n  border: none;\n  border-bottom: 1px solid transparent;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  box-sizing: border-box;\n\n  // Tab button text styling\n  color: #547996; // Exact Figma color from results.scss line 199 (gray-3)\n  font-weight: 600; // Exact Figma font-weight from results.scss line 162\n  text-align: left; // Exact Figma text-align from results.scss line 163\n  text-wrap: nowrap; // Exact Figma text-wrap from results.scss line 164\n\n  &:hover:not(:disabled) {\n    color: #17181a; // text-black\n  }\n\n  &.active {\n    color: #1976d2; // primary-blue from results.scss line 160\n    border-bottom: 1px solid #1976d2; // Exact Figma border from results.scss lines 133-135\n  }\n\n  &:disabled {\n    cursor: not-allowed;\n    opacity: 0.5;\n  }\n}\n\n.tab-content {\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  align-items: stretch; // Changed to stretch\n  gap: 12px;\n  width: 100%;\n  box-sizing: border-box;\n}\n\n\n\n\n\n\n\n\n\n\n\n\n.error-message {\n  color: #F4454E;\n  font-size: 12px;\n  font-family: 'Urbane', sans-serif;\n  font-weight: 300;\n  line-height: 16px;\n  margin-top: 4px;\n}\n\n// Responsive design\n@media (max-width: 768px) {\n  .results-container {\n    padding: 16px;\n  }\n\n  .tab-navigation {\n    flex-wrap: wrap;\n    gap: 8px;\n  }\n\n  .tab-button {\n    font-size: 12px;\n    padding: 2px 6px;\n  }\n}"], "mappings": ";AAGA,CAAA;AACE,WAAA;AACA,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,eAAA;AACA,OAAA;AACA,iBAAA;AACA,UAAA,IAAA,MAAA;AACA,cAAA;AACA,SAAA;AACA,cAAA;;AAEA,CAbF,iBAaE,CAAA;AACE,WAAA;AACA,kBAAA;;AAGF,CAlBF,iBAkBE,CAAA;AACE,gBAAA;;AAIJ,CAAA;AACE,WAAA,IAAA,IAAA,KAAA;AACA,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,eAAA;AACA,OAAA;AACA,SAAA;AACA,cAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,eAAA;AACA,OAAA;AACA,SAAA;AACA,cAAA;;AAGF,CAAA;AACE,SAAA;AACA,eAAA;AACA,aAAA;AACA,cAAA;AACA,eAAA;AACA,eAAA;AACA,cAAA;AACA,aAAA;AACA,UAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,eAAA;AACA,OAAA;AACA,iBAAA,IAAA,MAAA;AACA,cAAA;AACA,SAAA;AACA,cAAA;;AAGF,CAAA;AACE,WAAA,IAAA,IAAA,IAAA;AACA,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,eAAA;AACA,OAAA;AACA,UAAA;AACA,cAAA;AACA,UAAA;AACA,iBAAA,IAAA,MAAA;AACA,UAAA;AACA,cAAA,IAAA,KAAA;AACA,cAAA;AAGA,SAAA;AACA,eAAA;AACA,cAAA;AACA,aAAA;;AAEA,CArBF,UAqBE,MAAA,KAAA;AACE,SAAA;;AAGF,CAzBF,UAyBE,CAAA;AACE,SAAA;AACA,iBAAA,IAAA,MAAA;;AAGF,CA9BF,UA8BE;AACE,UAAA;AACA,WAAA;;AAIJ,CAAA;AACE,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,eAAA;AACA,OAAA;AACA,SAAA;AACA,cAAA;;AAcF,CAAA;AACE,SAAA;AACA,aAAA;AACA,eAAA,QAAA,EAAA;AACA,eAAA;AACA,eAAA;AACA,cAAA;;AAIF,OAAA,CAAA,SAAA,EAAA;AACE,GAxIF;AAyII,aAAA;;AAGF,GApFF;AAqFI,eAAA;AACA,SAAA;;AAGF,GA7EF;AA8EI,eAAA;AACA,aAAA,IAAA;;;", "names": []}
{"version": 3, "sources": ["src/app/shared/components/menu/menu.component.scss", "src/styles/_variables.scss", "src/styles/_mixins.scss"], "sourcesContent": ["@use 'variables' as variables;\n@use 'mixins' as mix;\n\n.menu-container {\n  width: 100%;\n  height: 80px;\n  background: variables.$white;\n  border-bottom: 1px solid variables.$gray-1;\n  position: sticky;\n  top: 0;\n  z-index: 1000;\n  display: flex;\n  align-items: center;\n}\n\n.menu-content {\n  width: 100%;\n  height: 100%;\n  padding: 12px 30px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.logo-section {\n  flex: 1;\n  display: flex;\n  justify-content: flex-start;\n  align-items: center;\n}\n\n.logo-container {\n  width: 240px;\n  padding: 10px 20px;\n  background: variables.$white;\n  display: flex;\n  justify-content: flex-start;\n  align-items: center;\n  gap: 12px;\n  cursor: pointer;\n  transition: opacity 0.2s ease;\n\n  &:hover {\n    opacity: 0.8;\n  }\n}\n\n.logo-image {\n  width: 150px;\n  height: 37.4px;\n  object-fit: contain;\n}\n\n.logo-placeholder {\n  font-size: 18px;\n  font-family: 'Urbane', sans-serif;\n  font-weight: 600;\n  color: variables.$text-black;\n}\n\n.user-section {\n  position: relative;\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n  gap: 16px;\n}\n\n.user-info {\n  padding: 12px 20px;\n  display: flex;\n  justify-content: flex-start;\n  align-items: center;\n  gap: 12px;\n}\n\n.user-name {\n  font-size: 12px;\n  font-family: 'Urbane', sans-serif;\n  font-weight: 300;\n  line-height: 20px;\n  color: variables.$text-black;\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n}\n\n.user-avatar {\n  cursor: pointer;\n  transition: transform 0.2s ease;\n\n  &:hover {\n    transform: scale(1.05);\n  }\n}\n\n.avatar-circle {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background: variables.$gray-1;\n  border: 1px solid variables.$gray-2;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.2s ease;\n\n  &:hover {\n    border-color: variables.$gray-3;\n    background: variables.$white;\n  }\n}\n\n.user-icon {\n  width: 20px;\n  height: 20px;\n  color: variables.$gray-3;\n  transition: color 0.2s ease;\n\n  .avatar-circle:hover & {\n    color: variables.$text-black;\n  }\n}\n\n.dropdown-arrow {\n  cursor: pointer;\n  transition: transform 0.2s ease;\n  padding: 4px;\n\n  &.open {\n    transform: rotate(180deg);\n  }\n\n  &:hover {\n    background-color: variables.$gray-1;\n    border-radius: 4px;\n  }\n}\n\n.arrow-icon {\n  width: 16px;\n  height: 16px;\n  color: variables.$gray-3;\n  transition: color 0.2s ease;\n\n  .dropdown-arrow:hover & {\n    color: variables.$text-black;\n  }\n}\n\n.user-dropdown {\n  position: absolute;\n  top: 100%;\n  right: 0;\n  background: variables.$white;\n  border-radius: 10px;\n  border: 1px solid variables.$gray-2;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  z-index: 1001;\n  margin-top: 8px;\n  min-width: 200px;\n  overflow: hidden;\n}\n\n.dropdown-content {\n  padding: 8px 0;\n}\n\n.dropdown-item {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px 20px;\n  cursor: pointer;\n  transition: background-color 0.2s ease;\n  font-size: 12px;\n  font-family: 'Urbane', sans-serif;\n  font-weight: 300;\n  color: variables.$text-black;\n\n  &:hover {\n    background-color: variables.$gray-1;\n  }\n\n  &:active {\n    background-color: variables.$gray-2;\n  }\n}\n\n.item-icon {\n  font-size: 14px;\n  width: 16px;\n  text-align: center;\n}\n\n.item-label {\n  flex: 1;\n}\n\n// Responsive behavior\n@include mix.for-tablet-portrait-up {\n  .menu-content {\n    padding: 12px 20px;\n  }\n  \n  .logo-container {\n    width: 200px;\n    padding: 8px 16px;\n  }\n  \n  .logo-image {\n    width: 120px;\n    height: 30px;\n  }\n}\n\n@include mix.for-phone-only {\n  .menu-content {\n    padding: 8px 16px;\n  }\n  \n  .logo-container {\n    width: auto;\n    padding: 4px 8px;\n  }\n  \n  .logo-image {\n    width: 100px;\n    height: 25px;\n  }\n  \n  .user-name {\n    display: none; // Hide user name on mobile\n  }\n  \n  .user-dropdown {\n    right: -16px; // Adjust for mobile padding\n    min-width: 180px;\n  }\n}\n", "// Color Variables\r\n$text-black: #17181A;\r\n$primary-blue: #3870B8;\r\n$hover-blue: #468CE7;\r\n$click-blue: #285082;\r\n$light-blue: #BFD0EE;\r\n$link: #0071BC;\r\n$gray-1: #F1F5F7;\r\n$gray-2: #D9E1E7;\r\n$gray-3: #547996;\r\n$gray-4: #384455;\r\n$success-green: #1AD598;\r\n$white: #FFFFFF;\r\n$background-gray: #F6F6F6;\r\n$light-background: #F9FBFC;\r\n$light-primary: #809FB8;\r\n\r\n// Opacity Variables\r\n$primary-blue-opacity-20: rgba(56, 112, 184, 0.20);\r\n$success-green-opacity-10: rgba(26, 213, 152, 0.10);\r\n$success-green-opacity-40: rgba(26, 213, 152, 0.40);\r\n\r\n// Spacing Variables\r\n$spacing-xs: 4px;\r\n$spacing-sm: 8px;\r\n$spacing-md: 12px;\r\n$spacing-lg: 16px;\r\n$spacing-xl: 20px;\r\n$spacing-xxl: 24px;\r\n$spacing-xxxl: 30px;\r\n$spacing-huge: 40px;\r\n$spacing-giant: 48px;\r\n$spacing-mega: 55px;\r\n\r\n// Border Radius\r\n$border-radius-sm: 5px;\r\n$border-radius-md: 6px;\r\n$border-radius-lg: 8px;\r\n$border-radius-xl: 10px;\r\n$border-radius-round: 90px;\r\n$border-radius-circle: 120px;\r\n\r\n// Box Shadow\r\n$box-shadow-sm: 0px 1px 2px rgba(16, 24, 40, 0.05);\r\n\r\n// Border Width\r\n$border-width-default: 1px;\r\n$border-width-thick: 1.5px;\r\n\r\n// Z-index\r\n$z-index-default: 1;\r\n$z-index-dropdown: 1000;\r\n$z-index-sticky: 1020;\r\n$z-index-fixed: 1030;\r\n$z-index-modal-backdrop: 1040;\r\n$z-index-modal: 1050;\r\n$z-index-popover: 1060;\r\n$z-index-tooltip: 1070;", "// Import variables\r\n@use 'variables' as variables;\r\n\r\n// Flexbox Mixins\r\n@mixin flex-row {\r\n  display: flex;\r\n  flex-direction: row;\r\n}\r\n\r\n@mixin flex-column {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n@mixin flex-center {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n@mixin flex-between {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n@mixin flex-start {\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  align-items: center;\r\n}\r\n\r\n@mixin flex-end {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  align-items: center;\r\n}\r\n\r\n// Layout Mixins\r\n@mixin container {\r\n  width: 100%;\r\n  padding-left: variables.$spacing-xxxl;\r\n  padding-right: variables.$spacing-xxxl;\r\n}\r\n\r\n@mixin card {\r\n  background: variables.$white;\r\n  border-radius: variables.$border-radius-lg;\r\n  outline: variables.$border-width-default variables.$gray-1 solid;\r\n  outline-offset: -(variables.$border-width-default);\r\n  padding: variables.$spacing-xl;\r\n  margin-bottom: variables.$spacing-xl;\r\n}\r\n\r\n// Button Mixins\r\n@mixin button-base {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  gap: variables.$spacing-sm;\r\n  border-radius: variables.$border-radius-lg;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  border: none;\r\n  outline: none;\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n@mixin button-primary {\r\n  @include button-base;\r\n  background: variables.$primary-blue;\r\n  color: variables.$white;\r\n  padding: variables.$spacing-sm variables.$spacing-lg;\r\n\r\n  &:hover {\r\n    background: variables.$hover-blue;\r\n  }\r\n\r\n  &:active {\r\n    background: variables.$click-blue;\r\n  }\r\n\r\n  &:disabled {\r\n    background: variables.$light-blue;\r\n    cursor: not-allowed;\r\n  }\r\n}\r\n\r\n@mixin button-secondary {\r\n  @include button-base;\r\n  background: variables.$white;\r\n  outline: variables.$border-width-default variables.$gray-2 solid;\r\n  outline-offset: -(variables.$border-width-default);\r\n  color: variables.$text-black;\r\n  padding: variables.$spacing-sm variables.$spacing-md;\r\n\r\n  &:hover {\r\n    background: variables.$gray-1;\r\n  }\r\n\r\n  &:active {\r\n    background: variables.$text-black;\r\n    color: variables.$white;\r\n  }\r\n}\r\n\r\n@mixin button-icon {\r\n  @include button-base;\r\n  gap: variables.$spacing-sm;\r\n\r\n  .icon {\r\n    width: 20px;\r\n    height: 20px;\r\n  }\r\n}\r\n\r\n// Form Element Mixins\r\n@mixin input-field {\r\n  padding: variables.$spacing-md;\r\n  border-radius: variables.$border-radius-xl;\r\n  outline: variables.$border-width-default variables.$gray-2 solid;\r\n  outline-offset: -(variables.$border-width-default);\r\n  font-size: 12px;\r\n  font-family: 'Urbane', sans-serif;\r\n  width: 100%;\r\n\r\n  &:focus {\r\n    outline-color: variables.$gray-3;\r\n  }\r\n}\r\n\r\n@mixin textarea-field {\r\n  padding: variables.$spacing-md;\r\n  border-radius: variables.$border-radius-xl;\r\n  outline: variables.$border-width-default variables.$gray-2 solid;\r\n  outline-offset: -(variables.$border-width-default);\r\n  font-size: 12px;\r\n  font-family: 'Urbane', sans-serif;\r\n  width: 100%;\r\n  min-height: 88px;\r\n\r\n  &:focus {\r\n    outline-color: variables.$gray-3;\r\n  }\r\n}\r\n\r\n@mixin checkbox {\r\n  width: 16px;\r\n  height: 16px;\r\n  border-radius: 5px; // Figma specifies 5px border radius\r\n  border: 1px solid variables.$gray-2;\r\n  background-color: variables.$white;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  appearance: none;\r\n  -webkit-appearance: none;\r\n  -moz-appearance: none;\r\n  position: relative;\r\n\r\n  &:checked {\r\n    background-color: variables.$text-black;\r\n    border-color: variables.$text-black;\r\n\r\n    &::after {\r\n      content: '';\r\n      position: absolute;\r\n      left: 4px;\r\n      top: 4px;\r\n      width: 8.33px;\r\n      height: 7.5px;\r\n      background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8.33' height='7.5' viewBox='0 0 8.33 7.5'%3E%3Cpath d='M1 3.75L3.5 6.25L7.33 1.25' stroke='white' stroke-width='1.5' fill='none' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E\");\r\n      background-repeat: no-repeat;\r\n      background-position: center;\r\n      background-size: contain;\r\n    }\r\n  }\r\n\r\n  &:hover:not(:disabled) {\r\n    border-color: variables.$gray-3;\r\n  }\r\n\r\n  &:focus {\r\n    outline: 2px solid variables.$primary-blue;\r\n    outline-offset: 2px;\r\n  }\r\n\r\n  &:disabled {\r\n    opacity: 0.5;\r\n    cursor: not-allowed;\r\n  }\r\n}\r\n\r\n// Table Mixins\r\n@mixin table-header {\r\n  padding: variables.$spacing-md;\r\n  border-bottom: variables.$border-width-default variables.$gray-1 solid;\r\n  font-weight: 500;\r\n  color: variables.$text-black;\r\n  height: 68px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n@mixin table-cell {\r\n  padding: variables.$spacing-md;\r\n  font-weight: 300;\r\n  height: 68px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-start;\r\n}\r\n\r\n\r\n\r\n// Icon Mixins\r\n@mixin icon-container {\r\n  width: 20px;\r\n  height: 20px;\r\n  position: relative;\r\n}\r\n\r\n// Status Indicators\r\n@mixin status-badge($color, $bg-color) {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: variables.$spacing-xs variables.$spacing-sm;\r\n  border-radius: variables.$border-radius-round;\r\n  background-color: $bg-color;\r\n  color: $color;\r\n  font-size: 11px;\r\n  font-weight: 300;\r\n}\r\n\r\n@mixin success-badge {\r\n  @include status-badge(variables.$success-green, variables.$success-green-opacity-10);\r\n  outline: variables.$border-width-default variables.$success-green-opacity-40 solid;\r\n  outline-offset: -(variables.$border-width-default);\r\n}\r\n\r\n// Responsive Mixins\r\n@mixin for-phone-only {\r\n  @media (max-width: 599px) { @content; }\r\n}\r\n\r\n@mixin for-tablet-portrait-up {\r\n  @media (min-width: 600px) { @content; }\r\n}\r\n\r\n@mixin for-tablet-landscape-up {\r\n  @media (min-width: 900px) { @content; }\r\n}\r\n\r\n@mixin for-desktop-up {\r\n  @media (min-width: 1200px) { @content; }\r\n}\r\n\r\n@mixin for-big-desktop-up {\r\n  @media (min-width: 1800px) { @content; }\r\n}"], "mappings": ";AAGA,CAAA;AACE,SAAA;AACA,UAAA;AACA,cCMM;ADLN,iBAAA,IAAA,MAAA;AACA,YAAA;AACA,OAAA;AACA,WAAA;AACA,WAAA;AACA,eAAA;;AAGF,CAAA;AACE,SAAA;AACA,UAAA;AACA,WAAA,KAAA;AACA,WAAA;AACA,mBAAA;AACA,eAAA;;AAGF,CAAA;AACE,QAAA;AACA,WAAA;AACA,mBAAA;AACA,eAAA;;AAGF,CAAA;AACE,SAAA;AACA,WAAA,KAAA;AACA,cCtBM;ADuBN,WAAA;AACA,mBAAA;AACA,eAAA;AACA,OAAA;AACA,UAAA;AACA,cAAA,QAAA,KAAA;;AAEA,CAXF,cAWE;AACE,WAAA;;AAIJ,CAAA;AACE,SAAA;AACA,UAAA;AACA,cAAA;;AAGF,CAAA;AACE,aAAA;AACA,eAAA,QAAA,EAAA;AACA,eAAA;AACA,SCxDW;;AD2Db,CAAA;AACE,YAAA;AACA,WAAA;AACA,mBAAA;AACA,eAAA;AACA,OAAA;;AAGF,CAAA;AACE,WAAA,KAAA;AACA,WAAA;AACA,mBAAA;AACA,eAAA;AACA,OAAA;;AAGF,CAAA;AACE,aAAA;AACA,eAAA,QAAA,EAAA;AACA,eAAA;AACA,eAAA;AACA,SChFW;ADiFX,WAAA;AACA,mBAAA;AACA,eAAA;;AAGF,CAAA;AACE,UAAA;AACA,cAAA,UAAA,KAAA;;AAEA,CAJF,WAIE;AACE,aAAA,MAAA;;AAIJ,CAAA;AACE,SAAA;AACA,UAAA;AACA,iBAAA;AACA,cC7FO;AD8FP,UAAA,IAAA,MAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,cAAA,IAAA,KAAA;;AAEA,CAXF,aAWE;AACE,gBCnGK;ADoGL,cCjGI;;ADqGR,CAAA;AACE,SAAA;AACA,UAAA;AACA,SC3GO;AD4GP,cAAA,MAAA,KAAA;;AAEA,CAvBF,aAuBE,OAAA,CANF;AAOI,SCvHS;;AD2Hb,CAAA;AACE,UAAA;AACA,cAAA,UAAA,KAAA;AACA,WAAA;;AAEA,CALF,cAKE,CAAA;AACE,aAAA,OAAA;;AAGF,CATF,cASE;AACE,oBC/HK;ADgIL,iBAAA;;AAIJ,CAAA;AACE,SAAA;AACA,UAAA;AACA,SCrIO;ADsIP,cAAA,MAAA,KAAA;;AAEA,CArBF,cAqBE,OAAA,CANF;AAOI,SCjJS;;ADqJb,CAAA;AACE,YAAA;AACA,OAAA;AACA,SAAA;AACA,cC9IM;AD+IN,iBAAA;AACA,UAAA,IAAA,MAAA;AACA,cAAA,EAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,WAAA;AACA,cAAA;AACA,aAAA;AACA,YAAA;;AAGF,CAAA;AACE,WAAA,IAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;AACA,WAAA,KAAA;AACA,UAAA;AACA,cAAA,iBAAA,KAAA;AACA,aAAA;AACA,eAAA,QAAA,EAAA;AACA,eAAA;AACA,SCjLW;;ADmLX,CAZF,aAYE;AACE,oBC9KK;;ADiLP,CAhBF,aAgBE;AACE,oBCjLK;;ADqLT,CAAA;AACE,aAAA;AACA,SAAA;AACA,cAAA;;AAGF,CAAA;AACE,QAAA;;AEkDA,OAAA,CAAA,SAAA,EAAA;AF7CA,GA1LF;AA2LI,aAAA,KAAA;;AAGF,GA9KF;AA+KI,WAAA;AACA,aAAA,IAAA;;AAGF,GAnKF;AAoKI,WAAA;AACA,YAAA;;;AE8BF,OAAA,CAAA,SAAA,EAAA;AFzBA,GA1MF;AA2MI,aAAA,IAAA;;AAGF,GA9LF;AA+LI,WAAA;AACA,aAAA,IAAA;;AAGF,GAnLF;AAoLI,WAAA;AACA,YAAA;;AAGF,GA3JF;AA4JI,aAAA;;AAGF,GArFF;AAsFI,WAAA;AACA,eAAA;;;", "names": []}
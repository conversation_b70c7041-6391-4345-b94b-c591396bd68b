@use 'variables' as variables;
@use 'mixins' as mix;

.assigned-table-container {
  width: max-content;
  background: variables.$white;
  border-radius: 8px;
  border: none;
  overflow: hidden;
  min-width: 1400px; // Increased to accommodate new columns
}

.assigned-table-header {
  display: flex;
  background: variables.$white;
  border-bottom: 1px solid variables.$gray-1 !important;
  border-top: none !important;
  border-left: none !important;
  border-right: none !important;
}

.assigned-table-header-cell {
  padding: 16px 12px;
  font-size: 12px;
  font-family: 'Urbane', sans-serif;
  font-weight: 500;
  color: variables.$text-black;
  line-height: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  border: none !important;
  border-right: none !important;
  border-left: none !important;
  border-top: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: variables.$white !important;
}

.assigned-table-body {
  width: 100%;
}

.assigned-table-row {
  display: flex;
  background: variables.$white;
  border: none !important;
  border-bottom: none !important;
  border-top: none !important;
  outline: none !important;
  box-shadow: none !important;

  &:hover {
    background: variables.$gray-1;
  }
}

.assigned-table-cell {
  padding: 16px 12px;
  font-size: 12px;
  font-family: 'Urbane', sans-serif;
  font-weight: 300;
  color: variables.$text-black;
  line-height: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  border: none !important;
  border-right: none !important;
  border-left: none !important;
  border-top: none !important;
  border-bottom: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: variables.$white !important;
}

.status-badge {
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 12px;
  font-family: 'Urbane', sans-serif;
  font-weight: 500;
  line-height: 20px;
  text-align: center;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 80px;
  transition: all 0.2s ease;

  &.status-review {
    background-color: variables.$primary-blue;
    color: variables.$white;

    &:hover {
      background-color: variables.$hover-blue;
    }
  }

  &.status-complete {
    background-color: variables.$success-green-opacity-10;
    color: variables.$success-green;
    border: 1px solid variables.$success-green-opacity-40;
    padding: 6px 12px;
    border-radius: 90px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    min-width: 90px;

    &::before {
      content: '';
      display: inline-block;
      width: 14px;
      height: 14px;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='14' height='14' viewBox='0 0 14 14'%3E%3Cpath d='M3 7L5.5 9.5L11 4' stroke='%231AD598' stroke-width='1.5' fill='none'/%3E%3C/svg%3E");
      background-repeat: no-repeat;
      background-position: center;
      background-size: contain;
    }
  }

  &.status-inactive {
    background-color: #BFD0EE;
    color: variables.$white;
    cursor: not-allowed;
    opacity: 0.8;

    &:hover {
      background-color: #BFD0EE;
      transform: none;
      box-shadow: none;
    }
  }

  &.clickable {
    cursor: pointer;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }
}

.no-charts-message {
  padding: 40px 20px;
  text-align: center;
  font-size: 14px;
  font-family: 'Urbane', sans-serif;
  color: variables.$gray-3;
  font-style: italic;
  background: variables.$white;
}

// Responsive behavior
@include mix.for-tablet-landscape-up {
  .assigned-table-container {
    min-width: auto;
  }
}

@include mix.for-phone-only {
  .assigned-table-container {
    min-width: auto;
    overflow-x: auto;
  }

  .assigned-table-header-cell,
  .assigned-table-cell {
    padding: 12px 8px;
    font-size: 11px;
    min-width: 80px;
  }

  .status-badge {
    padding: 6px 12px;
    font-size: 11px;
    min-width: 70px;
  }
}

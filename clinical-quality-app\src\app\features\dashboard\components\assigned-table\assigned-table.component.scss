// Clean table styles with Figma-inspired design
.assigned-table-container {
  width: 100%;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #f1f5f7;
  overflow: hidden;
}

.assigned-table-header {
  display: flex;
  background: #ffffff;
  border-bottom: 1px solid #f1f5f7;
}

.assigned-table-header-cell {
  padding: 16px 12px;
  font-size: 12px;
  font-family: 'Urbane', sans-serif;
  font-weight: 500;
  color: #17181a;
  line-height: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  border-right: 1px solid #f1f5f7;
  box-sizing: border-box;
}

.assigned-table-header-cell:last-child {
  border-right: none;
}

.assigned-table-body {
  width: 100%;
}

.assigned-table-row {
  display: flex;
  background: #ffffff;
  border-bottom: 1px solid #f1f5f7;

  &:hover {
    background: #f8f9fa;
  }

  &:last-child {
    border-bottom: none;
  }
}

.assigned-table-cell {
  padding: 16px 12px;
  font-size: 12px;
  font-family: 'Urbane', sans-serif;
  font-weight: 300;
  color: #17181a;
  line-height: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  border-right: 1px solid #f1f5f7;
  box-sizing: border-box;
}

.assigned-table-cell:last-child {
  border-right: none;
}

.no-charts-message {
  padding: 40px 20px;
  text-align: center;
  font-size: 14px;
  font-family: 'Urbane', sans-serif;
  color: #757575;
  font-style: italic;
  background: #ffffff;
}

// Responsive behavior
@media (max-width: 1200px) {
  .assigned-table-container {
    overflow-x: auto;
  }

  .assigned-table-header,
  .assigned-table-row {
    min-width: 1200px;
  }
}

@media (max-width: 768px) {
  .assigned-table-header-cell,
  .assigned-table-cell {
    padding: 12px 8px;
    font-size: 11px;
  }
}

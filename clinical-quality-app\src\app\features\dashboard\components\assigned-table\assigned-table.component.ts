import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { AssignedChart, TableColumn, DEFAULT_TABLE_COLUMNS } from '../../../../core/data/models/chart-data.models';
import { SubmitButtonComponent } from '../../../../shared/components/buttons/submit-button.component';

@Component({
  selector: 'app-assigned-table',
  standalone: true,
  imports: [CommonModule, RouterModule, SubmitButtonComponent],
  templateUrl: './assigned-table.component.html',
  styleUrl: './assigned-table.component.scss'
})
export class AssignedTableComponent {
  @Input() charts: AssignedChart[] = [];
  @Input() searchText: string = '';

  // Column definitions matching the Figma specifications
  columns: TableColumn[] = DEFAULT_TABLE_COLUMNS;

  constructor(private router: Router) { }

  // Method to get field value safely
  getFieldValue(chart: AssignedChart, field: string): string {
    return (chart as any)[field] || '';
  }

  // Method to navigate to chart review page
  navigateToChartReview(chart: AssignedChart): void {
    if (chart && chart.status && chart.status.toLowerCase() === 'review' && chart.memberId) {
      this.router.navigate(['/chart-review', chart.memberId]);
    } else if (chart && chart.status && chart.status.toLowerCase() === 'review' && !chart.memberId) {
      console.error('Chart data is missing memberId for navigation but status is review', chart);
    }
  }

  // Method to filter charts based on search text
  get filteredCharts() {
    if (!this.searchText) {
      return this.charts;
    }

    const searchLower = this.searchText.toLowerCase();
    return this.charts.filter(chart => {
      return Object.values(chart).some(value =>
        value && typeof value === 'string' ?
          value.toLowerCase().includes(searchLower) :
          String(value).toLowerCase().includes(searchLower)
      );
    });
  }

  // Method to get status class based on status value
  getStatusClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'review':
        return 'status-review';
      case 'complete':
        return 'status-complete';
      case 'inactive':
        return 'status-inactive';
      default:
        return '';
    }
  }
}

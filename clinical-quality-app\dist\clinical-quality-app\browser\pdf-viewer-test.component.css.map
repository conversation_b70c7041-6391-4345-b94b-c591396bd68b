{"version": 3, "sources": ["src/app/features/chart-review/components/pdf-viewer-test/pdf-viewer-test.component.scss"], "sourcesContent": [".pdf-test-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100vh;\r\n  width: 100%;\r\n  background-color: #f5f5f5;\r\n  padding: 20px;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.test-controls {\r\n  background-color: #ffffff;\r\n  padding: 16px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 20px;\r\n}\r\n\r\nh2 {\r\n  margin-top: 0;\r\n  margin-bottom: 16px;\r\n  font-size: 20px;\r\n  color: #333;\r\n}\r\n\r\n.file-input-container {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.file-input-label {\r\n  display: inline-block;\r\n  padding: 8px 16px;\r\n  background-color: #2196f3;\r\n  color: white;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  transition: background-color 0.2s;\r\n  \r\n  &:hover {\r\n    background-color: #1976d2;\r\n  }\r\n}\r\n\r\n.file-input {\r\n  position: absolute;\r\n  width: 1px;\r\n  height: 1px;\r\n  padding: 0;\r\n  margin: -1px;\r\n  overflow: hidden;\r\n  clip: rect(0, 0, 0, 0);\r\n  border: 0;\r\n}\r\n\r\n.config-controls {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.control-group {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  \r\n  label {\r\n    font-size: 14px;\r\n    color: #333;\r\n  }\r\n  \r\n  button, select {\r\n    padding: 4px 8px;\r\n    border: 1px solid #ccc;\r\n    border-radius: 4px;\r\n    background-color: #fff;\r\n    color: #17181A; // Added text color to make buttons readable\r\n    font-size: 14px;\r\n    cursor: pointer;\r\n    \r\n    &:hover {\r\n      background-color: #f0f0f0;\r\n    }\r\n  }\r\n}\r\n\r\n.debug-info {\r\n  background-color: #f0f0f0;\r\n  padding: 12px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  font-family: monospace;\r\n  \r\n  h3 {\r\n    margin-top: 0;\r\n    margin-bottom: 8px;\r\n    font-size: 14px;\r\n  }\r\n  \r\n  div {\r\n    margin-bottom: 4px;\r\n  }\r\n}\r\n\r\n.pdf-viewer-wrapper {\r\n  flex: 1;\r\n  background-color: #ffffff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n  \r\n  /* Important: Ensure the PDF viewer takes full height */\r\n  height: calc(100vh - 300px);\r\n  min-height: 500px;\r\n}\r\n\r\n/* Ensure the ngx-extended-pdf-viewer fills the available space */\r\n:host ::ng-deep ngx-extended-pdf-viewer {\r\n  display: block;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n/* Fix for PDF.js viewer container */\r\n:host ::ng-deep .viewer-container {\r\n  overflow: auto !important;\r\n}\r\n\r\n/* Fix for PDF.js page container */\r\n:host ::ng-deep .page {\r\n  margin: 8px auto !important;\r\n}\r\n\r\n.no-pdf-message, .ssr-placeholder {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 100%;\r\n  font-size: 16px;\r\n  color: #666;\r\n  text-align: center;\r\n  padding: 20px;\r\n}\r\n\r\n/* Responsive adjustments */\r\n@media (max-width: 768px) {\r\n  .config-controls {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n  }\r\n  \r\n  .pdf-viewer-wrapper {\r\n    height: calc(100vh - 400px);\r\n  }\r\n}"], "mappings": ";AAAA,CAAA;AACE,WAAA;AACA,kBAAA;AACA,UAAA;AACA,SAAA;AACA,oBAAA;AACA,WAAA;AACA,cAAA;;AAGF,CAAA;AACE,oBAAA;AACA,WAAA;AACA,iBAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,iBAAA;;AAGF;AACE,cAAA;AACA,iBAAA;AACA,aAAA;AACA,SAAA;;AAGF,CAAA;AACE,iBAAA;;AAGF,CAAA;AACE,WAAA;AACA,WAAA,IAAA;AACA,oBAAA;AACA,SAAA;AACA,iBAAA;AACA,UAAA;AACA,aAAA;AACA,cAAA,iBAAA;;AAEA,CAVF,gBAUE;AACE,oBAAA;;AAIJ,CAAA;AACE,YAAA;AACA,SAAA;AACA,UAAA;AACA,WAAA;AACA,UAAA;AACA,YAAA;AACA,QAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,UAAA;;AAGF,CAAA;AACE,WAAA;AACA,aAAA;AACA,OAAA;AACA,iBAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;;AAEA,CALF,cAKE;AACE,aAAA;AACA,SAAA;;AAGF,CAVF,cAUE;AAAA,CAVF,cAUE;AACE,WAAA,IAAA;AACA,UAAA,IAAA,MAAA;AACA,iBAAA;AACA,oBAAA;AACA,SAAA;AACA,aAAA;AACA,UAAA;;AAEA,CAnBJ,cAmBI,MAAA;AAAA,CAnBJ,cAmBI,MAAA;AACE,oBAAA;;AAKN,CAAA;AACE,oBAAA;AACA,WAAA;AACA,iBAAA;AACA,aAAA;AACA,eAAA;;AAEA,CAPF,WAOE;AACE,cAAA;AACA,iBAAA;AACA,aAAA;;AAGF,CAbF,WAaE;AACE,iBAAA;;AAIJ,CAAA;AACE,QAAA;AACA,oBAAA;AACA,iBAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,YAAA;AACA,WAAA;AACA,kBAAA;AAGA,UAAA,KAAA,MAAA,EAAA;AACA,cAAA;;AAIF,MAAA,UAAA;AACE,WAAA;AACA,SAAA;AACA,UAAA;;AAIF,MAAA,UAAA,CAAA;AACE,YAAA;;AAIF,MAAA,UAAA,CAAA;AACE,UAAA,IAAA;;AAGF,CAAA;AAAA,CAAA;AACE,WAAA;AACA,eAAA;AACA,mBAAA;AACA,UAAA;AACA,aAAA;AACA,SAAA;AACA,cAAA;AACA,WAAA;;AAIF,OAAA,CAAA,SAAA,EAAA;AACE,GA9FF;AA+FI,oBAAA;AACA,iBAAA;;AAGF,GAjDF;AAkDI,YAAA,KAAA,MAAA,EAAA;;;", "names": []}